"""
Modul untuk interaksi dengan DEX (Decentralized Exchange).
"""
import asyncio
import logging
import socket
from typing import Dict, Any, List, Optional, Union, Tuple
from web3 import Web3
from requests.exceptions import ConnectionError, Timeout, ReadTimeout, RequestException

from .network import Network
from .utils import exponential_backoff, apply_slippage

logger = logging.getLogger("arbitrage_detector")

class DEX:
    """
    Kelas untuk interaksi dengan DEX.
    """

    def __init__(self, name: str, network: Network):
        """
        Inisialisasi objek DEX.

        Args:
            name: Nama DEX
            network: Objek Network
        """
        self.name = name
        self.network = network

        # Dapatkan konfigurasi DEX
        if name not in network.dexs:
            raise ValueError(f"DEX {name} tidak ditemukan dalam konfigurasi jaringan {network.name}")

        self.config = network.dexs[name]

        # Dapatkan kontrak DEX
        self.contract = network.get_dex_contract(name)

        # Cek apakah ini Uniswap V3
        self.is_v3 = 'quoter_address' in self.config

        # Simpan pengaturan dari network
        self.settings = network.settings

        logger.info(f"DEX {name} diinisialisasi di jaringan {network.name}")

    async def get_amounts_out(
        self,
        amount_in: int,
        path: List[str],
        slippage_percentage: float = 0.0
    ) -> List[int]:
        """
        Mendapatkan jumlah token keluar untuk jumlah token masuk tertentu.

        Args:
            amount_in: Jumlah token masuk dalam satuan terkecil
            path: Jalur alamat token
            slippage_percentage: Persentase slippage yang diterapkan

        Returns:
            List jumlah token di setiap langkah jalur
        """
        # Cek apakah DEX ada dalam daftar DEX bermasalah
        if self.name in self.network.problematic_dexs and self.settings.get('skip_problematic_dexs', True):
            logger.warning(f"Melewati DEX bermasalah {self.name} di {self.network.name}")
            raise ValueError(f"DEX {self.name} dilewati karena bermasalah")

        # Konversi alamat ke format checksum
        checksum_path = [Web3.to_checksum_address(addr) for addr in path]

        # Terapkan slippage ke amount_in jika diperlukan
        if slippage_percentage > 0:
            amount_in_with_slippage = apply_slippage(amount_in, slippage_percentage, is_input=True)
        else:
            amount_in_with_slippage = amount_in

        # Dapatkan jumlah percobaan dari pengaturan
        retry_attempts = self.settings.get('retry_attempts', 5)

        # Gunakan retry dengan exponential backoff
        for attempt in range(retry_attempts):
            try:
                if self.is_v3:
                    # Implementasi untuk Uniswap V3
                    amounts = await self._get_amounts_out_v3(amount_in_with_slippage, checksum_path)
                else:
                    # Implementasi untuk DEX berbasis Uniswap V2
                    amounts = self.contract.functions.getAmountsOut(
                        amount_in_with_slippage,
                        checksum_path
                    ).call()

                # Terapkan slippage ke amount_out jika diperlukan
                if slippage_percentage > 0:
                    amounts[-1] = apply_slippage(amounts[-1], slippage_percentage, is_input=False)

                return amounts
            except (ConnectionError, Timeout, ReadTimeout, RequestException, socket.error) as e:
                # Error koneksi, coba ganti RPC jika ini adalah percobaan terakhir
                if attempt == retry_attempts - 1:
                    logger.warning(f"Error koneksi saat mendapatkan amounts_out di {self.name} pada jaringan {self.network.name}: {e}")
                    # Coba ganti RPC
                    if self.network._try_alternative_rpc():
                        # Jika berhasil ganti RPC, coba lagi dari awal
                        return await self.get_amounts_out(amount_in, path, slippage_percentage)
                    else:
                        # Jika semua RPC gagal, tandai DEX sebagai bermasalah
                        self.network.problematic_dexs.add(self.name)
                        logger.error(f"Semua RPC gagal saat mendapatkan amounts_out di {self.name} pada jaringan {self.network.name}")
                        raise

                # Tunggu sebelum mencoba lagi
                delay = exponential_backoff(
                    attempt,
                    base_delay=self.settings.get('retry_delay_seconds', 2),
                    max_delay=self.settings.get('max_retry_delay_seconds', 30)
                )
                logger.warning(f"Error koneksi saat mendapatkan amounts_out di {self.name}. Mencoba lagi dalam {delay:.2f} detik.")
                await asyncio.sleep(delay)
            except Exception as e:
                # Cek apakah ini adalah error rate limit
                if "Too Many Requests" in str(e) or "429" in str(e):
                    # Jika ini adalah percobaan terakhir, coba ganti RPC
                    if attempt == retry_attempts - 1:
                        # Coba ganti RPC
                        if self.network._try_alternative_rpc():
                            # Jika berhasil ganti RPC, coba lagi dari awal
                            return await self.get_amounts_out(amount_in, path, slippage_percentage)
                        else:
                            # Jika semua RPC gagal, tandai DEX sebagai bermasalah
                            self.network.problematic_dexs.add(self.name)
                            logger.error(f"Gagal mendapatkan amounts_out di {self.name} pada jaringan {self.network.name} karena rate limit: {e}")
                            raise

                    # Tunggu lebih lama untuk rate limit
                    rate_limit_pause = self.settings.get('rate_limit_pause_seconds', 5)
                    logger.warning(f"Terkena rate limit untuk DEX {self.name}. Menunggu {rate_limit_pause} detik.")
                    await asyncio.sleep(rate_limit_pause)
                elif "execution reverted" in str(e).lower():
                    # Ini adalah error kontrak, mungkin karena likuiditas tidak cukup
                    if attempt == retry_attempts - 1:  # Percobaan terakhir
                        # Tandai DEX sebagai bermasalah untuk pasangan token ini
                        token_pair = f"{path[0]}-{path[-1]}"
                        logger.error(f"Gagal mendapatkan amounts_out di {self.name} pada jaringan {self.network.name} untuk pasangan {token_pair}: {e}")
                        raise
                    else:
                        # Tunggu sebelum mencoba lagi
                        delay = exponential_backoff(
                            attempt,
                            base_delay=self.settings.get('retry_delay_seconds', 2),
                            max_delay=self.settings.get('max_retry_delay_seconds', 30)
                        )
                        logger.warning(f"Percobaan {attempt+1} gagal untuk getAmountsOut di {self.name}. Mencoba lagi dalam {delay:.2f} detik.")
                        await asyncio.sleep(delay)
                elif attempt == retry_attempts - 1:  # Percobaan terakhir
                    # Tandai DEX sebagai bermasalah
                    self.network.problematic_dexs.add(self.name)
                    logger.error(f"Gagal mendapatkan amounts_out di {self.name} pada jaringan {self.network.name}: {e}")
                    raise
                else:
                    # Tunggu sebelum mencoba lagi
                    delay = exponential_backoff(
                        attempt,
                        base_delay=self.settings.get('retry_delay_seconds', 2),
                        max_delay=self.settings.get('max_retry_delay_seconds', 30)
                    )
                    logger.warning(f"Percobaan {attempt+1} gagal untuk getAmountsOut di {self.name}. Mencoba lagi dalam {delay:.2f} detik.")
                    await asyncio.sleep(delay)

    async def _get_amounts_out_v3(self, amount_in: int, path: List[str]) -> List[int]:
        """
        Implementasi khusus untuk mendapatkan amounts_out di Uniswap V3.

        Args:
            amount_in: Jumlah token masuk dalam satuan terkecil
            path: Jalur alamat token

        Returns:
            List jumlah token di setiap langkah jalur
        """
        # Uniswap V3 memerlukan implementasi yang berbeda
        # Ini adalah implementasi sederhana, dalam kasus nyata perlu lebih kompleks

        amounts = [amount_in]

        for i in range(len(path) - 1):
            token_in = path[i]
            token_out = path[i + 1]

            # Gunakan fee default 0.3% (3000)
            fee = 3000

            # Panggil quoteExactInputSingle
            amount_out = self.contract.functions.quoteExactInputSingle(
                token_in,
                token_out,
                fee,
                amount_in,
                0  # sqrtPriceLimitX96 (0 berarti tidak ada batas)
            ).call()

            amounts.append(amount_out)
            amount_in = amount_out

        return amounts

    async def get_amounts_in(
        self,
        amount_out: int,
        path: List[str],
        slippage_percentage: float = 0.0
    ) -> List[int]:
        """
        Mendapatkan jumlah token masuk untuk jumlah token keluar tertentu.

        Args:
            amount_out: Jumlah token keluar dalam satuan terkecil
            path: Jalur alamat token
            slippage_percentage: Persentase slippage yang diterapkan

        Returns:
            List jumlah token di setiap langkah jalur
        """
        # Konversi alamat ke format checksum
        checksum_path = [Web3.to_checksum_address(addr) for addr in path]

        # Terapkan slippage ke amount_out jika diperlukan
        if slippage_percentage > 0:
            amount_out_with_slippage = apply_slippage(amount_out, slippage_percentage, is_input=False)
        else:
            amount_out_with_slippage = amount_out

        # Gunakan retry dengan exponential backoff
        for attempt in range(3):
            try:
                if self.is_v3:
                    # Implementasi untuk Uniswap V3 tidak didukung untuk getAmountsIn
                    # Ini adalah keterbatasan dalam implementasi ini
                    raise NotImplementedError("getAmountsIn tidak didukung untuk Uniswap V3 dalam implementasi ini")
                else:
                    # Implementasi untuk DEX berbasis Uniswap V2
                    amounts = self.contract.functions.getAmountsIn(
                        amount_out_with_slippage,
                        checksum_path
                    ).call()

                # Terapkan slippage ke amount_in jika diperlukan
                if slippage_percentage > 0:
                    amounts[0] = apply_slippage(amounts[0], slippage_percentage, is_input=True)

                return amounts
            except Exception as e:
                if attempt == 2:  # Percobaan terakhir
                    logger.error(f"Gagal mendapatkan amounts_in di {self.name} pada jaringan {self.network.name}: {e}")
                    raise

                # Tunggu sebelum mencoba lagi
                delay = exponential_backoff(attempt)
                logger.warning(f"Percobaan {attempt+1} gagal untuk getAmountsIn. Mencoba lagi dalam {delay:.2f} detik.")
                await asyncio.sleep(delay)

    async def get_token_price(
        self,
        token_address: str,
        base_token_address: str,
        amount: int = 1000000
    ) -> float:
        """
        Mendapatkan harga token relatif terhadap token dasar.

        Args:
            token_address: Alamat token
            base_token_address: Alamat token dasar (misalnya USDC)
            amount: Jumlah token untuk kueri (dalam satuan terkecil)

        Returns:
            Harga token dalam token dasar
        """
        try:
            # Dapatkan jumlah token dasar untuk jumlah token tertentu
            amounts = await self.get_amounts_out(
                amount,
                [token_address, base_token_address]
            )

            # Dapatkan informasi desimal untuk kedua token
            token_contract = await self.network.get_token_contract(token_address)
            base_token_contract = await self.network.get_token_contract(base_token_address)

            token_decimals = token_contract.functions.decimals().call()
            base_token_decimals = base_token_contract.functions.decimals().call()

            # Hitung harga
            token_amount = amount / (10 ** token_decimals)
            base_token_amount = amounts[1] / (10 ** base_token_decimals)

            return base_token_amount / token_amount
        except Exception as e:
            logger.error(f"Gagal mendapatkan harga token {token_address} di {self.name}: {e}")
            return 0.0

    async def estimate_swap_gas(
        self,
        token_in_address: str,
        token_out_address: str,
        amount_in: int
    ) -> int:
        """
        Memperkirakan gas yang digunakan untuk swap.

        Args:
            token_in_address: Alamat token masuk
            token_out_address: Alamat token keluar
            amount_in: Jumlah token masuk

        Returns:
            Perkiraan gas yang digunakan
        """
        # Dalam implementasi nyata, ini akan menggunakan estimate_gas
        # Untuk kesederhanaan, kita gunakan nilai hardcoded

        # Nilai default untuk swap
        if self.is_v3:
            return 150000  # V3 biasanya menggunakan lebih banyak gas
        else:
            return 120000  # V2
