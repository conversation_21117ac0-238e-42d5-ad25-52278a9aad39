"""
Jupiter API module for interacting with Jupiter DEX Aggregator
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any, Tuple

import aiohttp

import config
import utils

logger = logging.getLogger("jupiter_arbitrage")

class JupiterAPI:
    """Class for interacting with Jupiter DEX Aggregator API"""

    def __init__(self, session: aiohttp.ClientSession):
        self.session = session

    async def get_quote(
        self,
        input_mint: str,
        output_mint: str,
        amount: int,
        slippage_bps: int = 50,
        retry_count: int = 0
    ) -> Optional[Dict[str, Any]]:
        """
        Get quote for swapping tokens

        Args:
            input_mint: Input token mint address
            output_mint: Output token mint address
            amount: Amount in input token's smallest unit (lamports for SOL)
            slippage_bps: Slippage in basis points (1 bps = 0.01%)
            retry_count: Current retry attempt

        Returns:
            Quote response or None if failed
        """
        max_retries = config.MAX_RETRIES

        try:
            url = f"{config.JUPITER_BASE_URL}/quote"
            params = {
                "inputMint": input_mint,
                "outputMint": output_mint,
                "amount": amount,
                "slippageBps": slippage_bps
            }

            async with self.session.get(url, params=params, timeout=config.REQUEST_TIMEOUT) as response:
                if response.status == 200:
                    return await response.json()
                elif response.status == 429 and retry_count < max_retries:
                    # Rate limited, wait and retry
                    error_text = await response.text()
                    logger.warning(f"Rate limited. Retrying in {2 ** retry_count} seconds. Error: {error_text}")
                    await asyncio.sleep(2 ** retry_count)  # Exponential backoff
                    return await self.get_quote(input_mint, output_mint, amount, slippage_bps, retry_count + 1)
                else:
                    error_text = await response.text()
                    logger.warning(f"Failed to get quote. Status: {response.status}, Error: {error_text}")
                    return None
        except Exception as e:
            if retry_count < max_retries:
                logger.warning(f"Error getting quote: {str(e)}. Retrying in {2 ** retry_count} seconds.")
                await asyncio.sleep(2 ** retry_count)  # Exponential backoff
                return await self.get_quote(input_mint, output_mint, amount, slippage_bps, retry_count + 1)
            else:
                logger.error(f"Error getting quote after {max_retries} retries: {str(e)}")
                return None

    async def get_price_impact(
        self,
        input_mint: str,
        output_mint: str,
        amount_sol: float
    ) -> Optional[float]:
        """
        Get price impact for a swap

        Args:
            input_mint: Input token mint address
            output_mint: Output token mint address
            amount_sol: Amount in SOL

        Returns:
            Price impact as a percentage or None if failed
        """
        # Convert SOL to lamports (1 SOL = 10^9 lamports)
        amount_lamports = int(amount_sol * 10**9)

        quote = await self.get_quote(input_mint, output_mint, amount_lamports)
        if not quote:
            return None

        try:
            # Calculate price impact from the quote
            in_amount = int(quote.get("inAmount", 0))
            out_amount = int(quote.get("outAmount", 0))

            if in_amount == 0 or out_amount == 0:
                return None

            # Get market price (without impact)
            market_price = float(quote.get("priceImpactPct", 0)) * 100
            return market_price
        except (KeyError, ValueError, TypeError) as e:
            logger.error(f"Error calculating price impact: {str(e)}")
            return None

    async def check_arbitrage_opportunity(
        self,
        token_mint: str,
        sol_mint: str = config.SOL_MINT,
        amount_sol: float = 1.0,
        slippage_bps: int = 50
    ) -> Tuple[Optional[Dict[str, Any]], Optional[Dict[str, Any]]]:
        """
        Check for arbitrage opportunity between SOL and another token

        Args:
            token_mint: Token mint address to check
            sol_mint: SOL mint address
            amount_sol: Amount of SOL to use
            slippage_bps: Slippage in basis points

        Returns:
            Tuple of (buy_quote, sell_quote) or (None, None) if failed
        """
        # Convert SOL to lamports
        amount_lamports = int(amount_sol * 10**9)

        # Get quote for SOL -> Token (buy)
        buy_quote = await self.get_quote(
            input_mint=sol_mint,
            output_mint=token_mint,
            amount=amount_lamports,
            slippage_bps=slippage_bps
        )

        if not buy_quote or "outAmount" not in buy_quote:
            return None, None

        # Get the output amount from the buy quote
        token_amount = int(buy_quote.get("outAmount", 0))

        if token_amount <= 0:
            return buy_quote, None

        # Get quote for Token -> SOL (sell)
        sell_quote = await self.get_quote(
            input_mint=token_mint,
            output_mint=sol_mint,
            amount=token_amount,
            slippage_bps=slippage_bps
        )

        return buy_quote, sell_quote

    async def analyze_arbitrage_opportunity(
        self,
        token_info: Dict[str, Any],
        sol_price_usd: float,
        amount_sol: float = 1.0,
        slippage_bps: int = 50,
        slippage_cost_percentage: float = 5.0
    ) -> Optional[Dict[str, Any]]:
        """
        Analyze arbitrage opportunity for a token

        Args:
            token_info: Token information
            sol_price_usd: SOL price in USD
            amount_sol: Amount of SOL to use
            slippage_bps: Slippage in basis points
            slippage_cost_percentage: Slippage cost as percentage for profit calculation

        Returns:
            Arbitrage opportunity details or None if no opportunity
        """
        token_mint = token_info.get("address")
        token_symbol = token_info.get("symbol", "???")

        if not token_mint:
            return None

        # Log token being analyzed
        logger.info(f"Analyzing arbitrage for {token_symbol} ({token_mint})")

        # Check arbitrage opportunity
        buy_quote, sell_quote = await self.check_arbitrage_opportunity(
            token_mint=token_mint,
            amount_sol=amount_sol,
            slippage_bps=slippage_bps
        )

        if not buy_quote:
            logger.info(f"No buy quote available for {token_symbol}")
            return None

        if not sell_quote:
            logger.info(f"No sell quote available for {token_symbol}")
            return None

        try:
            # Calculate amounts
            sol_in = int(buy_quote.get("inAmount", 0)) / 10**9  # Convert lamports to SOL
            token_out = int(buy_quote.get("outAmount", 0)) / 10**token_info.get("decimals", 0)
            token_in = int(sell_quote.get("inAmount", 0)) / 10**token_info.get("decimals", 0)
            sol_out = int(sell_quote.get("outAmount", 0)) / 10**9  # Convert lamports to SOL

            if sol_in <= 0 or token_out <= 0 or token_in <= 0 or sol_out <= 0:
                logger.info(f"Invalid amounts for {token_symbol}: sol_in={sol_in}, token_out={token_out}, token_in={token_in}, sol_out={sol_out}")
                return None

            # Calculate prices
            buy_price_sol = sol_in / token_out  # SOL per token
            sell_price_sol = sol_out / token_in  # SOL per token

            buy_price_usd = buy_price_sol * sol_price_usd  # USD per token
            sell_price_usd = sell_price_sol * sol_price_usd  # USD per token

            # Calculate profit
            profit_percentage = utils.calculate_profit_percentage(
                buy_price=sol_in,
                sell_price=sol_out,
                slippage_percentage=slippage_cost_percentage
            )

            # Log profit information
            if profit_percentage is not None:
                logger.info(f"Profit for {token_symbol}: {profit_percentage:.2f}% (Buy: ${buy_price_usd:.4f}, Sell: ${sell_price_usd:.4f})")

            # Check if profitable
            if profit_percentage is None or profit_percentage <= 0:
                logger.info(f"Not profitable for {token_symbol}: {profit_percentage if profit_percentage is not None else 'None'}%")
                return None

            # Create opportunity object
            opportunity = {
                "token_mint": token_mint,
                "token_name": token_info.get("name", "Unknown"),
                "token_symbol": token_symbol,
                "token_decimals": token_info.get("decimals", 0),
                "sol_in": sol_in,
                "token_out": token_out,
                "token_in": token_in,
                "sol_out": sol_out,
                "buy_price_sol": buy_price_sol,
                "sell_price_sol": sell_price_sol,
                "buy_price_usd": buy_price_usd,
                "sell_price_usd": sell_price_usd,
                "profit_sol": sol_out - sol_in,
                "profit_percentage": profit_percentage,
                "volume_24h": token_info.get("daily_volume"),
                "capital_amount_sol": amount_sol,
                "buy_route": buy_quote.get("routePlan", []),
                "sell_route": sell_quote.get("routePlan", []),
                "timestamp": utils.datetime.now().isoformat()
            }

            logger.info(f"Found arbitrage opportunity for {token_symbol} with profit {profit_percentage:.2f}%")
            return opportunity
        except Exception as e:
            logger.error(f"Error analyzing arbitrage opportunity for {token_symbol}: {str(e)}")
            return None
