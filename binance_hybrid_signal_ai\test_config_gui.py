#!/usr/bin/env python3
"""
Test script untuk halaman konfigurasi GUI
IntelliTrader X V5 Performance Enhanced
"""

import sys
import os

# Add the parent directory to the path to import the main module
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PySide6.QtWidgets import QApplication
from PySide6.QtCore import Qt
from PySide6.QtGui import QFont

# Import the configuration classes
from binance3timeframe import TradingConfig, ConfigurationWidget, COLOR_BACKGROUND_MAIN

def main():
    """Test the configuration GUI"""
    
    # Set up high DPI handling
    if hasattr(QApplication, 'setHighDpiScaleFactorRoundingPolicy'):
        QApplication.setHighDpiScaleFactorRoundingPolicy(Qt.HighDpiScaleFactorRoundingPolicy.PassThrough)
    if hasattr(Qt, 'AA_EnableHighDpiScaling'): 
        QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    if hasattr(Qt, 'AA_UseHighDpiPixmaps'): 
        QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
    
    app = QApplication(sys.argv)
    
    # Set default font
    try:
        default_font = QFont("Inter", 10 if sys.platform != "darwin" else 11)
    except RuntimeError:
        default_font = QFont()
    app.setFont(default_font)
    
    # Create configuration instance
    config = TradingConfig()
    
    # Create and show configuration widget
    config_widget = ConfigurationWidget(config)
    config_widget.setWindowTitle("IntelliTrader X - Pengaturan Trading")
    config_widget.setMinimumSize(800, 600)
    config_widget.setStyleSheet(f"background-color: {COLOR_BACKGROUND_MAIN.name()};")
    
    # Connect config changed signal
    def on_config_changed():
        print("Konfigurasi telah diubah!")
        print(f"Timeframes: {config.TIMEFRAMES_TO_ANALYZE}")
        print(f"Confidence Threshold: {config.MIN_CONFIDENCE_THRESHOLD}")
        print(f"Max Workers: {config.MAX_WORKERS_ANALYSIS}")
    
    config_widget.config_changed.connect(on_config_changed)
    
    config_widget.show()
    
    print("=" * 60)
    print("🚀 INTELLITRADER X - TEST KONFIGURASI GUI")
    print("=" * 60)
    print("Testing halaman konfigurasi...")
    print("Silakan test semua fitur konfigurasi:")
    print("1. Ubah timeframes")
    print("2. Adjust confidence thresholds")
    print("3. Modify indicator weights")
    print("4. Test save/load configuration")
    print("5. Test reset functionality")
    print("=" * 60)
    
    try:
        sys.exit(app.exec())
    except KeyboardInterrupt:
        print("\n⚠️  Program dihentikan oleh user (Ctrl+C)")
    except Exception as e:
        print(f"\n❌ Error fatal: {e}")
    finally:
        print("🔚 Test konfigurasi GUI selesai.")

if __name__ == '__main__':
    main()
