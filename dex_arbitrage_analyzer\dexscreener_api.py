"""
Modul untuk berinteraksi dengan API Dexscreener.
"""

import requests
import time
import json
from typing import Dict, List, Any, Optional, Tuple
import logging

from config import RATE_LIMIT_DELAY, MAX_RETRIES, BACKOFF_FACTOR
from target_chains import TARGET_CHAINS, EXPENSIVE_CHAINS

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("dexscreener_api")

class DexscreenerAPI:
    """Kelas untuk berinteraksi dengan API Dexscreener."""

    BASE_URL = "https://api.dexscreener.com"

    def __init__(self):
        self.session = requests.Session()
        self.last_request_time = 0

    def _handle_rate_limit(self):
        """Menangani rate limit dengan menambahkan delay antar request."""
        current_time = time.time()
        time_since_last_request = current_time - self.last_request_time

        if time_since_last_request < RATE_LIMIT_DELAY:
            sleep_time = RATE_LIMIT_DELAY - time_since_last_request
            time.sleep(sleep_time)

        self.last_request_time = time.time()

    def _make_request(self, endpoint: str) -> Dict[str, Any]:
        """
        Membuat request ke API Dexscreener dengan retry dan backoff eksponensial.

        Args:
            endpoint: Endpoint API yang akan diakses.

        Returns:
            Dict berisi respons dari API.

        Raises:
            Exception: Jika terjadi error saat melakukan request setelah semua percobaan.
        """
        url = f"{self.BASE_URL}{endpoint}"
        retry_count = 0
        current_delay = RATE_LIMIT_DELAY

        while retry_count <= MAX_RETRIES:
            try:
                # Tunggu delay untuk rate limit
                self._handle_rate_limit()

                response = self.session.get(url)

                # Cek jika rate limit terlampaui
                if response.status_code == 429:  # Too Many Requests
                    retry_count += 1
                    if retry_count > MAX_RETRIES:
                        logger.error(f"Rate limit terlampaui untuk {url} setelah {MAX_RETRIES} percobaan")
                        raise Exception(f"Rate limit API terlampaui setelah {MAX_RETRIES} percobaan")

                    # Hitung delay dengan backoff eksponensial
                    backoff_delay = current_delay * (BACKOFF_FACTOR ** (retry_count - 1))
                    logger.warning(f"Rate limit terdeteksi, menunggu {backoff_delay:.2f} detik sebelum mencoba lagi (percobaan {retry_count}/{MAX_RETRIES})")
                    time.sleep(backoff_delay)
                    continue

                response.raise_for_status()
                return response.json()

            except requests.exceptions.HTTPError as e:
                if e.response.status_code == 429:  # Too Many Requests
                    retry_count += 1
                    if retry_count > MAX_RETRIES:
                        logger.error(f"Rate limit terlampaui untuk {url} setelah {MAX_RETRIES} percobaan")
                        raise Exception(f"Rate limit API terlampaui setelah {MAX_RETRIES} percobaan")

                    # Hitung delay dengan backoff eksponensial
                    backoff_delay = current_delay * (BACKOFF_FACTOR ** (retry_count - 1))
                    logger.warning(f"Rate limit terdeteksi, menunggu {backoff_delay:.2f} detik sebelum mencoba lagi (percobaan {retry_count}/{MAX_RETRIES})")
                    time.sleep(backoff_delay)
                else:
                    logger.error(f"Error HTTP saat melakukan request ke {url}: {e}")
                    raise Exception(f"Error saat mengakses API Dexscreener: {e}")
            except requests.exceptions.ConnectionError as e:
                retry_count += 1
                if retry_count > MAX_RETRIES:
                    logger.error(f"Koneksi gagal untuk {url} setelah {MAX_RETRIES} percobaan")
                    raise Exception(f"Koneksi ke API gagal setelah {MAX_RETRIES} percobaan")

                # Hitung delay dengan backoff eksponensial
                backoff_delay = current_delay * (BACKOFF_FACTOR ** (retry_count - 1))
                logger.warning(f"Koneksi gagal, menunggu {backoff_delay:.2f} detik sebelum mencoba lagi (percobaan {retry_count}/{MAX_RETRIES})")
                time.sleep(backoff_delay)
            except requests.exceptions.Timeout as e:
                retry_count += 1
                if retry_count > MAX_RETRIES:
                    logger.error(f"Timeout untuk {url} setelah {MAX_RETRIES} percobaan")
                    raise Exception(f"Timeout API setelah {MAX_RETRIES} percobaan")

                # Hitung delay dengan backoff eksponensial
                backoff_delay = current_delay * (BACKOFF_FACTOR ** (retry_count - 1))
                logger.warning(f"Timeout terdeteksi, menunggu {backoff_delay:.2f} detik sebelum mencoba lagi (percobaan {retry_count}/{MAX_RETRIES})")
                time.sleep(backoff_delay)
            except requests.exceptions.RequestException as e:
                logger.error(f"Error saat melakukan request ke {url}: {e}")
                raise Exception(f"Error saat mengakses API Dexscreener: {e}")
            except json.JSONDecodeError as e:
                logger.error(f"Error saat parsing JSON dari {url}: {e}")
                raise Exception(f"Error saat parsing respons API: {e}")

        # Jika kita sampai di sini, berarti semua percobaan gagal
        raise Exception(f"Gagal mengakses API setelah {MAX_RETRIES} percobaan")

    def search_pairs(self, query: str) -> List[Dict[str, Any]]:
        """
        Mencari pasangan token berdasarkan query dan memfilter berdasarkan chain target.

        Args:
            query: Query pencarian.

        Returns:
            List berisi pasangan token yang ditemukan pada chain target.
        """
        endpoint = f"/latest/dex/search?q={query}"
        response = self._make_request(endpoint)

        if "pairs" not in response:
            logger.warning(f"Tidak ada pasangan yang ditemukan untuk query: {query}")
            return []

        # Filter pairs berdasarkan chain target
        filtered_pairs = []
        for pair in response["pairs"]:
            chain_id = pair.get("chainId", "")
            if chain_id in TARGET_CHAINS:
                filtered_pairs.append(pair)
            elif chain_id in EXPENSIVE_CHAINS:
                # Log untuk chain mahal yang dilewati
                logger.debug(f"Melewati pair di chain mahal: {chain_id} untuk {pair.get('baseToken', {}).get('symbol', '')} / {pair.get('quoteToken', {}).get('symbol', '')}")

        if len(filtered_pairs) < len(response["pairs"]):
            logger.info(f"Memfilter {len(response['pairs']) - len(filtered_pairs)} pairs dari chain yang tidak ditargetkan untuk query: {query}")

        return filtered_pairs

    def get_pair_details(self, chain_id: str, pair_address: str) -> Optional[Dict[str, Any]]:
        """
        Mendapatkan detail pasangan token berdasarkan chain ID dan alamat pair.

        Args:
            chain_id: ID chain.
            pair_address: Alamat pair.

        Returns:
            Dict berisi detail pasangan token atau None jika tidak ditemukan.
        """
        endpoint = f"/latest/dex/pairs/{chain_id}/{pair_address}"
        response = self._make_request(endpoint)

        if "pairs" not in response or not response["pairs"]:
            logger.warning(f"Tidak ada detail yang ditemukan untuk pair: {chain_id}/{pair_address}")
            return None

        return response["pairs"][0]

    def get_token_pairs(self, chain_id: str, token_address: str) -> List[Dict[str, Any]]:
        """
        Mendapatkan semua pasangan token untuk token tertentu.

        Args:
            chain_id: ID chain.
            token_address: Alamat token.

        Returns:
            List berisi pasangan token yang ditemukan.
        """
        endpoint = f"/token-pairs/v1/{chain_id}/{token_address}"
        response = self._make_request(endpoint)

        if not response:
            logger.warning(f"Tidak ada pasangan yang ditemukan untuk token: {chain_id}/{token_address}")
            return []

        return response

    def get_multiple_tokens(self, chain_id: str, token_addresses: List[str]) -> List[Dict[str, Any]]:
        """
        Mendapatkan informasi untuk beberapa token sekaligus.

        Args:
            chain_id: ID chain.
            token_addresses: List alamat token (maksimal 30).

        Returns:
            List berisi informasi token yang ditemukan.
        """
        if len(token_addresses) > 30:
            logger.warning("API Dexscreener hanya mendukung maksimal 30 alamat token per request.")
            token_addresses = token_addresses[:30]

        addresses_str = ",".join(token_addresses)
        endpoint = f"/tokens/v1/{chain_id}/{addresses_str}"
        response = self._make_request(endpoint)

        if not response:
            logger.warning(f"Tidak ada token yang ditemukan untuk alamat: {addresses_str}")
            return []

        return response

    def fetch_all_pairs_for_queries(self, queries: List[str]) -> List[Dict[str, Any]]:
        """
        Mengambil semua pasangan token untuk daftar query.

        Args:
            queries: List query pencarian.

        Returns:
            List berisi semua pasangan token yang ditemukan.
        """
        all_pairs = []

        for query in queries:
            try:
                logger.info(f"Mencari pasangan untuk query: {query}")
                pairs = self.search_pairs(query)
                all_pairs.extend(pairs)
                logger.info(f"Ditemukan {len(pairs)} pasangan untuk query: {query}")
            except Exception as e:
                logger.error(f"Error saat mencari pasangan untuk query {query}: {e}")

        # Menghapus duplikat berdasarkan pairAddress dan chainId
        unique_pairs = {}
        for pair in all_pairs:
            key = f"{pair.get('chainId', '')}-{pair.get('pairAddress', '')}"
            if key not in unique_pairs:
                unique_pairs[key] = pair

        return list(unique_pairs.values())

    def fetch_pair_volatility(self, chain_id: str, pair_address: str) -> Optional[float]:
        """
        Mengambil data volatilitas 1 jam untuk pasangan token.

        Args:
            chain_id: ID chain.
            pair_address: Alamat pair.

        Returns:
            Nilai volatilitas 1 jam atau None jika tidak tersedia.
        """
        pair_details = self.get_pair_details(chain_id, pair_address)

        if not pair_details or "priceChange" not in pair_details:
            return None

        price_change = pair_details.get("priceChange", {})
        h1_change = price_change.get("h1")

        if h1_change is None:
            return None

        return abs(float(h1_change))
