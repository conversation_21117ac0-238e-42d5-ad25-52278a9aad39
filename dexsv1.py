# -*- coding: utf-8 -*-
"""
DEX Arbitrage Scanner - Versi 1.0 (Optimized with Rate Limit Protection)

Program untuk mencari peluang arbitrase di berbagai DEX (Decentralized Exchanges)
dengan menggunakan API Dexscreener.

Fitur:
- Pencarian asinkron dengan optimasi asyncio
- Penanganan rate limit dengan backoff eksponensial dan jitter
- Filter ketat untuk memastikan peluang arbitrase valid
- Tampilan konsol yang informatif dengan Rich

Optimasi Asyncio:
- Penggunaan asyncio.shield untuk melindungi operasi penting
- Penanganan error yang lebih baik dengan retry dan backoff
- Struktur task yang lebih efisien
- Pengelolaan sumber daya yang lebih baik

Perlindungan Rate Limit:
- Konkurensi API yang lebih rendah (3 request bersamaan)
- Batching request dengan delay antar batch
- Backoff eksponensial yang lebih agresif untuk error 429
- Header HTTP tambahan untuk menghindari deteksi bot
- Delay acak antara request untuk menghindari pola yang mudah dideteksi
- Retry yang lebih banyak (5 kali) dengan waktu tunggu yang lebih lama
"""
import time
import json
from collections import defaultdict
import logging
from datetime import datetime
import sys
import asyncio
import aiohttp # Diperlukan karena mode async
import signal
import random # Diperlukan untuk jitter pada backoff

# Impor Rich untuk Output Konsol
from rich.console import Console
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TimeElapsedColumn
from rich.text import Text

# --- Konfigurasi ---

# -- API & Fetching --
API_BASE_URL = "https://api.dexscreener.com/latest/dex"
TARGET_CHAINS = ["ethereum", "bsc", "polygon", "arbitrum", "optimism", "base", "solana", "avalanche", "fantom", "cronos", "gnosis", "celo"] # Ditambah Gnosis, Celo
# Diperbanyak lagi (~1000++ token) - PERINGATAN: SANGAT BANYAK!
SEARCH_QUERIES = list(set([ # Gunakan set untuk coba deduplikasi awal
    # Major Coins & Stablecoins (Expanded++)
    "WETH", "WBTC", "USDC", "USDT", "DAI", "BUSD", "TUSD", "USDP", "GUSD", "PAXG", "ETH", "BTC", "USDD", "FRAX", "LUSD", "USTC", "MIM", "EUROC", "XSGD", "XCHF", "EURT", "USDK", "FEI", "ALUSD", "PYUSD", "sUSD", "BEAN", "MAI", "TOR", "AGEUR", "SEUR", "EURS", "CUSD", "CEUR", "crvUSD", "GHO", "mkUSD",
    # L1 Tokens (Expanded++)
    "SOL", "BNB", "MATIC", "AVAX", "DOT", "ADA", "NEAR", "ATOM", "FTM", "ALGO", "XTZ", "HBAR", "EGLD", "FLOW", "ICP", "KSM", "MINA", "ONE", "CELO", "ROSE", "KAVA", "SCRT", "IOTX", "ZIL", "SYS", "VET", "TRX", "XLM", "EOS", "BSV", "WAVES", "KLAY", "XEC", "MIOTA", "NEO", "DASH", "ZEC", "XEM", "BTG", "QTUM", "ICX", "ONT", "NANO", "SC", "LSK", "STEEM", "ARDR", "DGB", "RVN", "ZEN", "KDA", "FLUX", "ERG", "CFX", "CKB", "IOST", "WAN", "XPR", "SUI", "APT", "SEI", "INJ", "OSMO", "JUNO", "EVMOS", "AXL", "STRD", "STARS", "FET", "AKT", "TIA", "DYM", "SAGA", "PYTH",
    # L2 Tokens (Expanded++)
    "ARB", "OP", "METIS", "IMX", "LRC", "SKL", "BOBA", "CELR", "MATICX", "STG", "VLX", "OMG", "AZERO", "SDN", "ASTR", "MOVR", "GLMR", "AURORA", "EWT", "ZK", "MANTA", "STRK", "ZKS", "EDU", "RON", "BEAM", "GLQ", "SYS", "BOBA", "METIS", "MNT", "CANTO",
    # DeFi Blue Chips (Expanded++)
    "LINK", "UNI", "AAVE", "SUSHI", "CRV", "MKR", "COMP", "SNX", "YFI", "BAL", "1INCH", "ZRX", "REN", "KNC", "LDO", "RPL", "FXS", "CVX", "GNO", "RUNE", "CAKE", "THOR", "BIFI", "BADGER", "ALCX", "OHM", "SPELL", "JOE", "QUICK", "RAY", "SRM", "WOO", "PERP", "DYDX", "GMX", "UMA", "NEXO", "CEL", "BNT", "AMP", "AUDIO", "BAND", "KEEP", "NU", "TRIBE", "LQTY", "PENDLE", "RDNT", "GRAIL", "DPX", "JONES", "VELO", "AERO", "MAV", "SSV", "OGV", "SD", "INST", "GNS", "MCB", "KWENTA",
    # DeFi Mid/Small Caps (Heavily Expanded++ - MANY MAY BE LOW QUALITY)
    "VELA", "LOOKS", "X2Y2", "BTRFLY", "TOKE", "ANGLE", "SYN", "MVX", "SPA", "GEIST", "SOLAR", "HOP", "SONNE", "THE", "PEARL", "OXD", "SOLID", "RAM", "PSI", "ORCA", "SABER", "STEP", "LYRA", "HEC", "BENT", "YAK", "XDEFI", "DFL", "SLND", "PORT", "MNGO", "FAB", "APEX", "MMF", "SVN", "METF", "DARK", "TOMB", "LIFE", "PLSX", "PLSD", "HEX", "HDRN", "MAXI", "LUAG", "VVS", "TONIC", "PHOTON", "FORT", "HND", "BABL", "STFX", "LVL", "PLS", "INC", "ATRI", "BDP", "BOND", "CREAM", "DODO", "EPS", "FARM", "FORTH", "INV", "KP3R", "MLN", "MTA", "PICKLE", "POOL", "RAI", "RGT", "STAKE", "TRU", "VISR", "VSP", "YAX", "ZERO", "ALPACA", "AUTO", "BUNNY", "DEFI", "DFYN", "FEG", "FLURRY", "FRAX", "GEAR", "INDEX", "MDX", "QI", "SOLAPE", "SWAP", "TITAN", "TULIP", "YELD", "YFII", # ... and hundreds more
    # Meme Coins (Expanded++)
    "PEPE", "SHIB", "DOGE", "FLOKI", "BONK", "ELON", "BABYDOGE", "SAFEMOON", "VOLT", "KISHU", "PIG", "SAITAMA", "CULT", "TAMA", "PIT", "LEASH", "BONE", "VINU", "QUACK", "HAM", "MONSTA", "AKITA", "HOGE", "SAMO", "WOOF", "KITTY", "CATE", "MARVIN", "POLYDOGE", "LOWB", "CHEEMS", "TSUKA", "BOB", "TURBO", "MOG", "LADYS", "RFD", "WSB", "BEN", "RIBBIT", "POOH", "FOUR", "MONG", "SPONGE", "AI", "CAPY", "PSYOP", "WIF", "MYRO", "WEN", "POPCAT", "SILLY", "ANALOS", "COQ",
    # Metaverse/Gaming (Expanded++)
    "MANA", "SAND", "AXS", "GALA", "ENJ", "ILV", "APE", "MAGIC", "YGG", "PYR", "UOS", "CHR", "ALICE", "TLM", "DAR", "GHST", "REVV", "MBOX", "SLP", "RACA", "CEEK", "HIGH", "WILD", "SPS", "DG", "COMBO", "NAKA", "HERO", "ATLAS", "POLIS", "STARL", "VOXEL", "RON", "MC", "GF", "BLOK", "VR", "DPET", "SKILL", "MIST", "NFT", "UFO", "SOUL", "SIN", "PYE", "GSG", "GGG", "XWG", "YOOSHI", "RADIO", "MCRT", "MOOV", "POSI", "SPL", "TOWER", "VERA", "XANA",
    # Infrastructure/Web3 (Expanded++)
    "GRT", "FIL", "THETA", "CHZ", "ANKR", "BAT", "ENS", "API3", "AUDIO", "MASK", "OGN", "RLY", "UMA", "STORJ", "BAND", "OCEAN", "INJ", "FET", "AR", "HNT", "LPT", "POWR", "REN", "RLC", "CTSI", "DIA", "BLZ", "TRAC", "VIDT", "NU", "KEEP", "POLY", "COTI", "PHA", "RAD", "REQ", "SKL", "SUPER", "TVK", "PUNDIX", "COVAL", "RARE", "AKT", "CUDOS", "MNW", "ORION", "OXT", "POND", "ROOK", "SWFTC", "UMB", "PROM", "BLT", "DENT", "FUN", "HOT", "KEY", "MBL", "MTL", "NKN", "NMR", "POWR", "REQ", "RIF", "STMX", "SNT", "TFUEL", "VRA", "XYO", "ALEPH", "ARPA", "CQT", "CTX", "DF", "ERN", "FLUX", "FRONT", "GTC", "IDEX", "IRIS", "LINA", "LIT", "PERL", "POLS", "POND", "PROS", "REEF", "STPT", "TLM", "UTK", "VID", "WING",
]))
PAIRS_LIMIT_PER_QUERY = 15 # Kurangi lagi limit per query
# Filter Likuiditas/Aktivitas (Lebih Ketat)
MIN_VOLUME_USD_H24 = 5000 # Naikkan batas volume
MIN_TRANSACTIONS_H24 = 25  # Naikkan batas transaksi
MIN_LIQUIDITY_USD = 10000 # Naikkan batas likuiditas minimal
MAX_BUY_SELL_TXN_RATIO = 10.0 # Perketat sedikit rasio (lebih sensitif terhadap ketidakseimbangan)
# Filter Harga & Profit
MIN_PERCENTAGE_DIFF_THRESHOLD = 0.6 # Naikkan sedikit min diff agar lebih signifikan
MAX_PERCENTAGE_DIFF_THRESHOLD = 150.0 # Turunkan max diff untuk menghindari anomali ekstrim
# Filter Kelayakan Profit vs Gas (BARU)
PROFIT_GAS_RATIO_THRESHOLD = 1.75 # Profit bersih terendah harus > 1.75x biaya gas estimasi
# Filter DEX Terpercaya (BARU - Contoh, perlu dilengkapi)
TRUSTED_DEX_IDS = [
    # Ethereum
    "uniswap", "uniswap_v3", "sushiswap", "balancer", "balancer_v2", "curve", "bancor", "shibaswap",
    # BSC
    "pancakeswap", "pancakeswap_v2", "pancakeswap_v3", "apeswap", "biswap", "babyswap",
    # Polygon
    "quickswap", "quickswap_v3", "apeswap_polygon", "sushiswap_polygon", "dfyn", "balancer_v2_polygon",
    # Arbitrum
    "uniswap_v3_arbitrum", "sushiswap_arbitrum", "balancer_v2_arbitrum", "camelot", "traderjoe_arbitrum", "chronos", "ramses",
    # Optimism
    "uniswap_v3_optimism", "sushiswap_optimism", "balancer_v2_optimism", "velodrome",
    # Base
    "uniswap_v3_base", "aerodrome", "baseswap",
    # Solana
    "raydium", "orca", "serum", "meteora",
    # Avalanche
    "traderjoe", "traderjoe_v2", "pangolin", "sushiswap_avalanche", "platypus_finance",
    # Fantom
    "spookyswap", "spiritswap", "equalizer_exchange",
    # Cronos
    "vvs_finance", "mm_finance", "cronaswap",
    # Gnosis
    "sushiswap_xdai", "honeyswap",
    # Celo
    "ubeswap", "sushiswap_celo",
    # Tambahkan DEX terpercaya lainnya sesuai riset Anda
]

# Estimasi Biaya
ESTIMATED_DEX_FEE_PERCENT = 0.3
ESTIMATED_GAS_FEE_USD = {
    "ethereum": 12.0, "bsc": 0.35, "polygon": 0.06, "arbitrum": 0.25, "optimism": 0.25,
    "base": 0.12, "solana": 0.01, "avalanche": 0.6, "fantom": 0.25, "cronos": 0.15,
    "gnosis": 0.05, "celo": 0.05 # Sesuaikan estimasi gas
}
IDR_CAPITAL = 3000000.0
APPROX_IDR_USD_RATE = 16000.0
SLIPPAGE_PERCENT_LOW = 5.0
SLIPPAGE_PERCENT_HIGH = 10.0
# Loop & Timing
DELAY_AFTER_OUTPUT_SECONDS = 90 # Tingkatkan delay karena siklus lebih lama
REQUEST_TIMEOUT = 40 # Tingkatkan timeout
API_CONCURRENCY = 3 # Dikurangi drastis untuk menghindari rate limit
BATCH_SIZE = 10 # Jumlah permintaan per batch
BATCH_DELAY = 0.2 # Delay antar batch dalam detik

# --- Setup ---
console = Console()
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s] %(message)s', stream=sys.stderr, datefmt='%Y-%m-%d %H:%M:%S')
logging.getLogger('aiohttp').setLevel(logging.WARNING)
logging.getLogger('urllib3.connectionpool').setLevel(logging.WARNING)

# --- Fungsi Bantuan ---
def get_available_chains(): return TARGET_CHAINS

def normalize_pair_key(token0_addr: str, token1_addr: str) -> str:
    """
    Membuat kunci unik yang konsisten untuk pasangan token berdasarkan alamat.

    Perbaikan:
    1. Validasi input yang lebih baik
    2. Penanganan edge case
    3. Optimasi perbandingan string

    Args:
        token0_addr: Alamat token pertama
        token1_addr: Alamat token kedua

    Returns:
        String kunci normalisasi dalam format "addr_kecil-addr_besar" atau None jika input tidak valid
    """
    # Validasi input
    if not token0_addr or not token1_addr or not isinstance(token0_addr, str) or not isinstance(token1_addr, str):
        return None

    # Normalisasi alamat (lowercase)
    addr0 = token0_addr.lower().strip()
    addr1 = token1_addr.lower().strip()

    # Validasi tambahan setelah normalisasi
    if not addr0 or not addr1:
        return None

    # Urutkan alamat secara leksikografis untuk konsistensi
    if addr0 < addr1:
        return f"{addr0}-{addr1}"
    else:
        return f"{addr1}-{addr0}"

# --- Fungsi API Async ---
shutdown_event = asyncio.Event()

def handle_shutdown_signal(sig, _):  # Parameter kedua (frame) tidak digunakan
    """
    Menangani sinyal shutdown (SIGINT, SIGTERM) dengan lebih baik.
    Menetapkan event untuk memberi tahu semua task asyncio untuk berhenti dengan bersih.

    Args:
        sig: Sinyal yang diterima (SIGINT, SIGTERM)
        _: Frame stack (tidak digunakan, tapi diperlukan oleh API signal handler)
    """
    if not shutdown_event.is_set():
        shutdown_event.set()
        console.print(f"\n[bold yellow]Signal {sig} diterima, shutdown (async)...[/bold yellow]")
        console.print("[yellow]Menunggu operasi yang sedang berjalan selesai. Tekan Ctrl+C lagi untuk paksa keluar.[/yellow]")
async def async_make_api_request(session: aiohttp.ClientSession, endpoint: str, params: dict = None,
                            semaphore: asyncio.Semaphore = None, retry_count: int = 0, max_retries: int = 5):
    """
    Membuat request API dengan backoff eksponensial dan jitter untuk penanganan rate limit yang lebih baik.

    Args:
        session: Sesi aiohttp
        endpoint: Endpoint API
        params: Parameter query
        semaphore: Semaphore untuk membatasi konkurensi
        retry_count: Jumlah percobaan ulang saat ini
        max_retries: Jumlah maksimum percobaan ulang (ditingkatkan menjadi 5)
    """
    url = f"{API_BASE_URL}{endpoint}"

    # Tambahkan delay awal kecil untuk menghindari burst request
    if retry_count == 0:
        await asyncio.sleep(random.uniform(0.1, 0.5))

    async with semaphore:
        if shutdown_event.is_set():
            return None

        try:
            # Tambahkan header User-Agent generik dan header tambahan untuk menghindari rate limit
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': 'application/json',
                'Accept-Language': 'en-US,en;q=0.9',
                'Cache-Control': 'no-cache',
                'Pragma': 'no-cache',
                'Referer': 'https://dexscreener.com/'
            }

            # Tambahkan delay kecil sebelum request untuk menghindari burst
            await asyncio.sleep(random.uniform(0.2, 0.5))

            async with session.get(
                url,
                params=params,
                timeout=aiohttp.ClientTimeout(total=REQUEST_TIMEOUT),
                headers=headers
            ) as response:
                response.raise_for_status()
                return await response.json()

        except asyncio.TimeoutError:
            logging.warning(f"Timeout (async) {url}")
            if retry_count < max_retries:
                # Backoff eksponensial dengan jitter untuk timeout
                wait_time = min(15 * (2 ** retry_count), 120) * random.uniform(0.8, 1.2)
                logging.info(f"Timeout retry {retry_count+1}/{max_retries}, waiting {wait_time:.1f}s")
                await asyncio.sleep(wait_time)
                return await async_make_api_request(session, endpoint, params, semaphore, retry_count + 1, max_retries)

        except aiohttp.ClientResponseError as e:
            logging.warning(f"HTTP Error {e.status} (async) {url}: {e.message}")

            if e.status == 429 and retry_count < max_retries:
                # Implementasi backoff eksponensial dengan jitter untuk rate limit
                # Gunakan backoff yang lebih agresif untuk rate limit
                base_wait = min(60 * (3 ** retry_count), 300)  # Max 300 detik (5 menit)
                jitter = random.uniform(0.8, 1.2)  # Jitter 20% ke atas atau ke bawah
                wait_time = base_wait * jitter

                console.print(f"[yellow](Async Rate Limit {e.status} - Retry {retry_count+1}/{max_retries}, menunggu {wait_time:.1f} detik)[/yellow]")
                await asyncio.sleep(wait_time)

                # Coba lagi dengan retry_count yang ditingkatkan
                return await async_make_api_request(session, endpoint, params, semaphore, retry_count + 1, max_retries)

            elif e.status >= 500 and retry_count < max_retries:
                # Backoff untuk server error
                wait_time = 10 * (2 ** retry_count) * random.uniform(0.8, 1.2)
                logging.info(f"Server error retry {retry_count+1}/{max_retries}, waiting {wait_time:.1f}s")
                await asyncio.sleep(wait_time)
                return await async_make_api_request(session, endpoint, params, semaphore, retry_count + 1, max_retries)

        except aiohttp.ClientConnectorError as e:
            logging.error(f"Conn Error (async) {url}: {e}")
            if retry_count < max_retries:
                wait_time = 15 * (2 ** retry_count) * random.uniform(0.8, 1.2)
                logging.info(f"Connection error retry {retry_count+1}/{max_retries}, waiting {wait_time:.1f}s")
                await asyncio.sleep(wait_time)
                return await async_make_api_request(session, endpoint, params, semaphore, retry_count + 1, max_retries)

        except json.JSONDecodeError:
            logging.error(f"JSON Error (async) {url}")
            if retry_count < max_retries:
                wait_time = 5 * (1.5 ** retry_count) * random.uniform(0.8, 1.2)
                logging.info(f"JSON error retry {retry_count+1}/{max_retries}, waiting {wait_time:.1f}s")
                await asyncio.sleep(wait_time)
                return await async_make_api_request(session, endpoint, params, semaphore, retry_count + 1, max_retries)

        except Exception as e:
            logging.error(f"Unexpected Error (async) {url}: {e}", exc_info=False)
            if retry_count < max_retries:
                wait_time = 5 * (1.5 ** retry_count) * random.uniform(0.8, 1.2)
                logging.info(f"Unexpected error retry {retry_count+1}/{max_retries}, waiting {wait_time:.1f}s")
                await asyncio.sleep(wait_time)
                return await async_make_api_request(session, endpoint, params, semaphore, retry_count + 1, max_retries)

        return None

async def async_search_pairs_on_chains(chains: list, search_queries: list, limit_per_query: int):
    """
    Mencari pasangan trading di berbagai blockchain secara asinkron dengan optimasi.

    Perbaikan:
    1. Menghindari pembuatan fungsi bersarang dalam loop
    2. Menggunakan semaphore dengan lebih efisien
    3. Mengelompokkan task berdasarkan chain untuk pemrosesan yang lebih efisien
    4. Implementasi batching untuk menghindari rate limit
    5. Throttling permintaan API dengan delay antar batch
    """
    all_pairs_data = defaultdict(list)
    api_tasks_to_run = []

    # Persiapkan semua task yang akan dijalankan
    for chain in chains:
        for query in search_queries:
            search_param = f"{query} chain:{chain}"
            api_endpoint = "/search"
            params = {"q": search_param}
            api_tasks_to_run.append({'chain': chain, 'query': query, 'endpoint': api_endpoint, 'params': params})

    total_queries = len(api_tasks_to_run)
    logging.info(f"Total API search queries planned (async): {total_queries}")

    # Fungsi helper untuk fetch dan package hasil
    async def fetch_and_package(session, info, semaphore, progress, task_progress):
        result = await async_make_api_request(session, info['endpoint'], info['params'], semaphore)
        progress.update(task_progress, advance=1)  # Update progress setelah selesai
        return {'info': info, 'result': result}

    # Jalankan task secara asinkron dengan batching untuk menghindari rate limit
    all_results_with_info = []
    semaphore = asyncio.Semaphore(API_CONCURRENCY)

    async with aiohttp.ClientSession() as session:
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
            TimeElapsedColumn(),
            console=console,
            transient=True
        ) as progress:
            task_progress = progress.add_task("[cyan]Mengambil data pair (async)...", total=total_queries)

            # Bagi task menjadi batch-batch kecil untuk menghindari rate limit
            for i in range(0, len(api_tasks_to_run), BATCH_SIZE):
                if shutdown_event.is_set():
                    break

                # Ambil batch task berikutnya
                batch_tasks = api_tasks_to_run[i:i+BATCH_SIZE]

                # Buat task untuk batch ini
                batch_coroutines = [
                    fetch_and_package(session, task_info, semaphore, progress, task_progress)
                    for task_info in batch_tasks
                ]

                # Jalankan batch dan tunggu hasilnya
                batch_results = await asyncio.gather(*batch_coroutines)
                all_results_with_info.extend(batch_results)

                # Tunggu sebentar sebelum batch berikutnya untuk menghindari rate limit
                # Tapi hanya jika ini bukan batch terakhir
                if i + BATCH_SIZE < len(api_tasks_to_run) and not shutdown_event.is_set():
                    await asyncio.sleep(BATCH_DELAY)

                    # Log progress
                    completed = min(i + BATCH_SIZE, total_queries)
                    logging.info(f"Completed {completed}/{total_queries} queries ({completed/total_queries*100:.1f}%)")

            logging.info(f"All API queries completed. Processing results...")

    # Hitung jumlah respons API yang berhasil
    successful_responses = len([r for r in all_results_with_info if r and r.get('result')])
    logging.info(f"Processing {successful_responses} successful API responses (async).")

    # Gunakan defaultdict untuk mengelompokkan pasangan berdasarkan chain dan normalized_key
    chain_pair_candidates = defaultdict(lambda: defaultdict(list))  # {chain: {normalized_key: [listings]}}
    processed_pairs_count = 0

    # Proses semua hasil API
    for res_package in all_results_with_info:
        # Skip jika tidak ada hasil atau hasil kosong
        if not res_package or not res_package.get('result'):
            continue

        chain = res_package['info']['chain']
        data = res_package['result']

        # Skip jika tidak ada pasangan
        if not data.get('pairs'):
            continue

        # Batasi jumlah pasangan yang diproses
        limited_pairs = data['pairs'][:limit_per_query]

        # Proses setiap pasangan
        for pair in limited_pairs:
            # Validasi data pasangan
            base_token_addr = pair.get('baseToken', {}).get('address')
            quote_token_addr = pair.get('quoteToken', {}).get('address')

            # Skip jika data tidak lengkap
            if not (pair.get('chainId') == chain and pair.get('url') and base_token_addr and quote_token_addr):
                continue

            # Buat kunci normalisasi
            normalized_key = normalize_pair_key(base_token_addr, quote_token_addr)
            if not normalized_key:  # Skip jika kunci tidak valid
                continue

            # Ekstrak data yang diperlukan
            volume_h24 = pair.get('volume', {}).get('h24', 0)
            txns_h24_buys = pair.get('txns', {}).get('h24', {}).get('buys', 0)
            txns_h24_sells = pair.get('txns', {}).get('h24', {}).get('sells', 0)
            liquidity_usd = pair.get('liquidity', {}).get('usd', 0)

            # Buat data kandidat
            candidate_data = {
                'pair_address': pair.get('pairAddress'),
                'dex': pair.get('dexId'),
                'url': pair.get('url'),
                'priceUsd': pair.get('priceUsd'),
                'baseToken': pair.get('baseToken'),
                'quoteToken': pair.get('quoteToken'),
                'volume_h24': volume_h24,
                'txns_h24_buys': txns_h24_buys,
                'txns_h24_sells': txns_h24_sells,
                'total_txns_h24': txns_h24_buys + txns_h24_sells,
                'liquidity_usd': liquidity_usd,
                'chainId': chain
            }

            # Tambahkan ke daftar kandidat
            chain_pair_candidates[chain][normalized_key].append(candidate_data)
            processed_pairs_count += 1

    logging.info(f"Processed {processed_pairs_count} valid pair listings from API results.")

    # Konversi ke format {chain: [all_listings]} untuk kemudahan penggunaan
    for chain, normalized_pairs in chain_pair_candidates.items():
        # Gabungkan semua listing dalam satu chain
        all_listings_in_chain = []
        for listings in normalized_pairs.values():
            all_listings_in_chain.extend(listings)
        all_pairs_data[chain] = all_listings_in_chain

    # Peringatan jika tidak ada data yang valid
    if not all_pairs_data:
        console.print("[yellow]Peringatan (async):[/yellow] Tidak ada data pair valid yang ditemukan.")
    return dict(all_pairs_data)


# --- Fungsi Logika Arbitrase (Dengan Filter Baru) ---
def find_intra_chain_arbitrage_opportunities(
    pairs_by_chain: dict,
    min_diff_percent: float, max_diff_percent: float,
    dex_fee_percent: float, gas_fees_usd: dict,
    min_volume: float, min_txns: int, min_liquidity: float, max_txn_ratio: float,
    profit_gas_ratio_threshold: float, trusted_dexs: list, # Filter baru
    usd_capital: float, slippage_low: float, slippage_high: float
    ):
    """
    Menganalisis peluang dengan filter yang sangat ketat.
    """
    opportunities = []
    # Fee total adalah 2x fee DEX (beli dan jual)
    # Variabel ini tidak digunakan karena perhitungan fee sudah dilakukan langsung di est_dex_fee_usd
    # Counters for filtered opportunities
    filtered_liquidity_count = 0
    filtered_volume_txn_count = 0
    filtered_txn_ratio_count = 0
    filtered_dex_count = 0
    filtered_profit_gas_count = 0
    filtered_price_range_count = 0

    for chain_id, all_listings_in_chain in pairs_by_chain.items():
        grouped_by_normalized_key = defaultdict(list)
        for listing in all_listings_in_chain:
            base_addr = listing.get('baseToken', {}).get('address')
            quote_addr = listing.get('quoteToken', {}).get('address')
            if base_addr and quote_addr:
                normalized_key = normalize_pair_key(base_addr, quote_addr)
                if normalized_key: grouped_by_normalized_key[normalized_key].append(listing)

        for normalized_key, dex_listings in grouped_by_normalized_key.items():
            if len(dex_listings) < 2: continue

            # 1. Filter Likuiditas Awal
            listings_with_min_liq = [l for l in dex_listings if l.get('liquidity_usd', 0) >= min_liquidity]
            if len(listings_with_min_liq) < 2: filtered_liquidity_count += 1; continue

            # Urutkan berdasarkan harga (pastikan float)
            try:
                sorted_listings = sorted(listings_with_min_liq, key=lambda x: float(x.get('priceUsd', 0)))
            except (ValueError, TypeError):
                continue # Skip jika ada harga non-numerik

            buy_listing = sorted_listings[0]
            sell_listing = sorted_listings[-1]

            # Handle jika DEX sama (cari alternatif)
            if buy_listing['dex'] == sell_listing['dex']:
                 if len(sorted_listings) > 2:
                     try:
                         # Coba alternatif 1
                         sell_listing_alt1 = sorted_listings[-2]
                         price_diff_pct_alt1 = -1
                         if buy_listing['dex'] != sell_listing_alt1['dex']: price_diff_pct_alt1 = ((float(sell_listing_alt1['priceUsd']) - float(buy_listing['priceUsd'])) / float(buy_listing['priceUsd'])) * 100
                         # Coba alternatif 2
                         buy_listing_alt2 = sorted_listings[1]; sell_listing_alt2 = sorted_listings[-1]; price_diff_pct_alt2 = -1
                         if buy_listing_alt2['dex'] != sell_listing_alt2['dex']: price_diff_pct_alt2 = ((float(sell_listing_alt2['priceUsd']) - float(buy_listing_alt2['priceUsd'])) / float(buy_listing_alt2['priceUsd'])) * 100

                         if price_diff_pct_alt1 > price_diff_pct_alt2 and price_diff_pct_alt1 > 0: sell_listing = sell_listing_alt1
                         elif price_diff_pct_alt2 > 0: buy_listing = buy_listing_alt2; sell_listing = sell_listing_alt2
                         else: continue # Tidak ada alternatif valid
                     except (ValueError, TypeError, IndexError): continue # Gagal memproses alternatif
                 else: continue # Tidak bisa arbitrase

            # 2. Filter DEX Terpercaya
            if buy_listing['dex'] not in trusted_dexs or sell_listing['dex'] not in trusted_dexs:
                filtered_dex_count += 1; continue

            # 3. Filter Volume & Transaksi H24
            buy_volume_ok = buy_listing.get('volume_h24', 0) >= min_volume
            buy_txns_ok = buy_listing.get('total_txns_h24', 0) >= min_txns
            sell_volume_ok = sell_listing.get('volume_h24', 0) >= min_volume
            sell_txns_ok = sell_listing.get('total_txns_h24', 0) >= min_txns
            if not (buy_volume_ok and buy_txns_ok and sell_volume_ok and sell_txns_ok):
                filtered_volume_txn_count += 1; continue

            # 4. Filter Rasio Transaksi (Honeypot Heuristic)
            buy_txns_b = buy_listing.get('txns_h24_buys', 0); buy_txns_s = buy_listing.get('txns_h24_sells', 0)
            sell_txns_b = sell_listing.get('txns_h24_buys', 0); sell_txns_s = sell_listing.get('txns_h24_sells', 0)
            suspicious_ratio = False
            if buy_txns_s <= 1 and buy_txns_b > min_txns:
                 if buy_txns_b / max(1, buy_txns_s) > max_txn_ratio: suspicious_ratio = True
            if sell_txns_s <= 1 and sell_txns_b > min_txns:
                 if sell_txns_b / max(1, sell_txns_s) > max_txn_ratio: suspicious_ratio = True
            if buy_txns_b <= 1 and buy_txns_s > min_txns:
                 if buy_txns_s / max(1, buy_txns_b) > max_txn_ratio: suspicious_ratio = True
            if sell_txns_b <= 1 and sell_txns_s > min_txns:
                 if sell_txns_s / max(1, sell_txns_b) > max_txn_ratio: suspicious_ratio = True
            if suspicious_ratio: filtered_txn_ratio_count += 1; continue

            # 5. Perhitungan Harga & Profit Awal
            try:
                buy_price = float(buy_listing['priceUsd']); sell_price = float(sell_listing['priceUsd'])
            except (ValueError, TypeError, KeyError): continue
            if buy_price <= 0: continue
            price_diff_percent = ((sell_price - buy_price) / buy_price) * 100

            # 6. Filter Rentang Perbedaan Harga
            if not (min_diff_percent < price_diff_percent <= max_diff_percent):
                filtered_price_range_count += 1; continue

            # 7. Perhitungan Estimasi Biaya & Profit Bersih
            profit_before_fees_percent = price_diff_percent
            est_gas_cost_usd = gas_fees_usd.get(chain_id, 0) * 2
            est_dex_fee_usd = (dex_fee_percent / 100.0) * usd_capital * 2
            est_slippage_cost_usd_low = (slippage_low / 100.0) * usd_capital * 2
            est_slippage_cost_usd_high = (slippage_high / 100.0) * usd_capital * 2
            total_est_cost_usd_low = est_gas_cost_usd + est_dex_fee_usd + est_slippage_cost_usd_low
            total_est_cost_usd_high = est_gas_cost_usd + est_dex_fee_usd + est_slippage_cost_usd_high
            gross_profit_usd = (sell_price - buy_price) * (usd_capital / buy_price) if buy_price > 0 else 0
            net_profit_usd_low = gross_profit_usd - total_est_cost_usd_high
            net_profit_usd_high = gross_profit_usd - total_est_cost_usd_low
            net_profit_percent_low = (net_profit_usd_low / usd_capital) * 100 if usd_capital > 0 else 0
            net_profit_percent_high = (net_profit_usd_high / usd_capital) * 100 if usd_capital > 0 else 0

            # 8. Filter Profit vs Gas (Final Check)
            if net_profit_usd_low <= (est_gas_cost_usd * profit_gas_ratio_threshold):
                filtered_profit_gas_count += 1; continue

            # Jika lolos semua filter, tambahkan ke peluang
            base_sym = buy_listing.get('baseToken', {}).get('symbol', 'N/A')
            quote_sym = buy_listing.get('quoteToken', {}).get('symbol', 'N/A')
            opportunity = {
                "chain": chain_id, "symbol_pair": f"{base_sym}/{quote_sym}", "normalized_key": normalized_key,
                "buy_dex": buy_listing['dex'], "buy_price": buy_price, "buy_pair_address": buy_listing['pair_address'],
                "sell_dex": sell_listing['dex'], "sell_price": sell_price, "sell_pair_address": sell_listing['pair_address'],
                "price_diff_percent": profit_before_fees_percent,
                "buy_url": buy_listing['url'], "sell_url": sell_listing['url'], "timestamp": datetime.now(),
                "buy_volume_h24": buy_listing.get('volume_h24', 0), "buy_txns_h24": buy_listing.get('total_txns_h24', 0), "buy_liquidity_usd": buy_listing.get('liquidity_usd', 0),
                "sell_volume_h24": sell_listing.get('volume_h24', 0), "sell_txns_h24": sell_listing.get('total_txns_h24', 0), "sell_liquidity_usd": sell_listing.get('liquidity_usd', 0),
                "est_gas_cost_usd": est_gas_cost_usd, "est_dex_fee_usd": est_dex_fee_usd,
                "est_slippage_cost_usd_range": (est_slippage_cost_usd_low, est_slippage_cost_usd_high),
                "total_est_cost_usd_range": (total_est_cost_usd_low, total_est_cost_usd_high),
                "net_profit_usd_range": (net_profit_usd_low, net_profit_usd_high),
                "net_profit_percent_range": (net_profit_percent_low, net_profit_percent_high),
            }
            opportunities.append(opportunity)

    # Log jumlah yang difilter oleh setiap kriteria baru
    if filtered_liquidity_count > 0: logging.info(f"Filtered out {filtered_liquidity_count} pairs due to low liquidity (min ${min_liquidity:,.0f}).")
    if filtered_dex_count > 0: logging.info(f"Filtered out {filtered_dex_count} opportunities due to untrusted DEX.")
    if filtered_volume_txn_count > 0: logging.info(f"Filtered out {filtered_volume_txn_count} opportunities due to low volume/txns H24.")
    if filtered_txn_ratio_count > 0: logging.info(f"Filtered out {filtered_txn_ratio_count} opportunities due to suspicious txn ratio (>{max_txn_ratio:.1f}x).")
    if filtered_price_range_count > 0: logging.info(f"Filtered out {filtered_price_range_count} opportunities due to price diff outside range ({min_diff_percent}% - {max_diff_percent}%).")
    if filtered_profit_gas_count > 0: logging.info(f"Filtered out {filtered_profit_gas_count} opportunities due to insufficient profit vs gas (ratio < {profit_gas_ratio_threshold:.2f}).")

    opportunities.sort(key=lambda x: x['net_profit_percent_range'][1], reverse=True) # Urutkan berdasarkan profit tertinggi
    logging.info(f"Found {len(opportunities)} potential arbitrage opportunities matching ALL criteria.")
    return opportunities

# --- Fungsi Tampilan Konsol ---
def display_opportunities_console(opportunities: list, usd_capital: float, idr_capital: float, idr_usd_rate: float):
    """
    Menampilkan Top 10 peluang arbitrase ke konsol.

    Args:
        opportunities: Daftar peluang arbitrase
        usd_capital: Modal dalam USD (digunakan dalam perhitungan di peluang)
        idr_capital: Modal dalam IDR (digunakan untuk tampilan)
        idr_usd_rate: Kurs IDR ke USD (tidak digunakan langsung, tapi disimpan untuk konsistensi API)
    """
    # Parameter usd_capital dan idr_usd_rate tidak digunakan langsung dalam fungsi ini,
    # tetapi disimpan untuk konsistensi API dan kemungkinan penggunaan di masa depan.
    # Parameter idr_capital digunakan dalam Panel disclaimer.
    if not opportunities:
        console.print("[yellow]Tidak ditemukan peluang arbitrase signifikan (setelah filter ketat) saat ini.[/yellow]")
        return

    # Tampilkan disclaimer di konsol dengan filter baru
    console.print(Panel(
        f"[bold]Penting:[/bold] Profit (%) sebelum gas & slippage. "
        f"Filter: Vol>${MIN_VOLUME_USD_H24:,.0f}, Txn>{MIN_TRANSACTIONS_H24}, "
        f"Liq>${MIN_LIQUIDITY_USD:,.0f}, TxnRatio<{MAX_BUY_SELL_TXN_RATIO:.1f}, "
        f"Profit/Gas>{PROFIT_GAS_RATIO_THRESHOLD:.2f}, DEX Terpercaya. "
        f"Modal Est: Rp {idr_capital:,.0f}. DYOR!",
        title="[bold yellow]⚠️ Disclaimer & Filter Aktif[/bold yellow]",
        border_style="yellow",
        expand=False
    ))

    top_opportunities = opportunities[:10]
    total_found = len(opportunities); display_count = len(top_opportunities)
    console.print(f"\n[bold magenta]✨ Menampilkan Top {display_count} dari {total_found} Peluang Arbitrase Ditemukan ({datetime.now().strftime('%Y-%m-%d %H:%M:%S')}) ✨[/bold magenta]")

    for i, opp in enumerate(top_opportunities):
        # (Format harga dan pembuatan pesan sama seperti sebelumnya, termasuk info Liq/Vol/Txn)
        buy_price_str = f"{opp['buy_price']:,.8f}".rstrip('0').rstrip('.') if opp['buy_price'] < 0.01 else f"{opp['buy_price']:,.6f}".rstrip('0').rstrip('.') if opp['buy_price'] < 1 else f"{opp['buy_price']:,.4f}"
        sell_price_str = f"{opp['sell_price']:,.8f}".rstrip('0').rstrip('.') if opp['sell_price'] < 0.01 else f"{opp['sell_price']:,.6f}".rstrip('0').rstrip('.') if opp['sell_price'] < 1 else f"{opp['sell_price']:,.4f}"
        message = Text()
        message.append(f"🔵 Chain: ", style="bold cyan"); message.append(f"{opp['chain'].capitalize()}\n")
        message.append(f"💹 Pair: ", style="bold white"); message.append(f"{opp['symbol_pair']}\n\n")
        message.append(f"📉 Beli di: ", style="bold green"); message.append(f"{opp['buy_dex']} (Liq: ${opp.get('buy_liquidity_usd', 0):,.0f}, Vol: ${opp.get('buy_volume_h24', 0):,.0f}, Txn: {opp.get('buy_txns_h24', 0)})\n")
        message.append(f"   Harga: ", style="green"); message.append(f"${buy_price_str}\n")
        message.append(f"   🔗 Link: ", style="dim blue"); message.append(f"{opp['buy_url']}\n\n", style=f"link {opp['buy_url']}")
        message.append(f"📈 Jual di: ", style="bold red"); message.append(f"{opp['sell_dex']} (Liq: ${opp.get('sell_liquidity_usd', 0):,.0f}, Vol: ${opp.get('sell_volume_h24', 0):,.0f}, Txn: {opp.get('sell_txns_h24', 0)})\n")
        message.append(f"   Harga: ", style="red"); message.append(f"${sell_price_str}\n")
        message.append(f"   🔗 Link: ", style="dim blue"); message.append(f"{opp['sell_url']}\n\n", style=f"link {opp['sell_url']}")
        message.append(f"📊 Diff Harga: ", style="bold yellow"); message.append(f"{opp['price_diff_percent']:.2f}%\n\n")
        cost_low, cost_high = opp['total_est_cost_usd_range']
        slip_low, slip_high = opp['est_slippage_cost_usd_range']
        profit_usd_low, profit_usd_high = opp['net_profit_usd_range']
        profit_pct_low, profit_pct_high = opp['net_profit_percent_range']
        profit_color = "bright_green" if profit_usd_high > 0 else "red"
        message.append(f"💰 Estimasi Biaya (USD):\n", style="bold")
        message.append(f"    Gas: ${opp['est_gas_cost_usd']:.2f}\n")
        message.append(f"    DEX Fee: ${opp['est_dex_fee_usd']:.2f}\n")
        message.append(f"    Slippage: ~${slip_low:.2f} - ${slip_high:.2f}\n")
        message.append(f"    Total: ~${cost_low:.2f} - ${cost_high:.2f}\n\n")
        message.append(f"✅ Estimasi Profit Net (USD): ", style=f"bold {profit_color}")
        message.append(f"~${profit_usd_low:.2f} - ${profit_usd_high:.2f}\n", style=profit_color)
        message.append(f"✅ Estimasi Profit Net (%): ", style=f"bold {profit_color}")
        message.append(f"~{profit_pct_low:.2f}% - {profit_pct_high:.2f}%\n", style=profit_color)
        console.print(Panel(message, title=f"[bold]Peluang #{i+1}[/bold]", border_style="blue", expand=False, padding=(1, 2)))


# --- Loop Utama Asinkron ---
async def async_main_loop():
    """
    Loop utama asinkron dengan penanganan error yang lebih baik dan optimasi siklus.

    Perbaikan:
    1. Penanganan error yang lebih baik
    2. Penggunaan asyncio.shield untuk melindungi operasi penting
    3. Pengelolaan sumber daya yang lebih baik
    4. Logging yang lebih informatif
    """
    usd_capital = IDR_CAPITAL / APPROX_IDR_USD_RATE
    console.print(Panel(
        f"[bold green]🚀 Pencari Arbitrase Crypto (Async, Filter Sangat Ketat) 🚀[/bold green]\n"
        f"Memantau chains: {len(TARGET_CHAINS)}, Token: ~{len(SEARCH_QUERIES)}+\n"
        f"Filter: Vol>${MIN_VOLUME_USD_H24:,.0f}, Txn>{MIN_TRANSACTIONS_H24}, Liq>${MIN_LIQUIDITY_USD:,.0f}, "
        f"TxnRatio<{MAX_BUY_SELL_TXN_RATIO:.1f}, Profit/Gas>{PROFIT_GAS_RATIO_THRESHOLD:.2f}, DEX Terpercaya\n"
        f"Modal Est: Rp {IDR_CAPITAL:,.0f} (~${usd_capital:.2f})\n"
        f"Konkurensi API: {API_CONCURRENCY}\n"
        f"Delay Siklus: {DELAY_AFTER_OUTPUT_SECONDS}s",
        title="[bold blue]Status[/bold blue]",
        border_style="blue"
    ))

    cycle_count = 0
    consecutive_errors = 0
    max_consecutive_errors = 3

    while not shutdown_event.is_set():
        cycle_start_time = time.monotonic()
        cycle_count += 1

        try:
            logging.info(f"Memulai Siklus Pemindaian #{cycle_count}...")
            chains = get_available_chains()

            if not chains:
                logging.error("Tidak ada chain target.")
                break

            # Gunakan asyncio.shield untuk melindungi operasi penting dari pembatalan
            pairs_data = await asyncio.shield(
                async_search_pairs_on_chains(chains, SEARCH_QUERIES, PAIRS_LIMIT_PER_QUERY)
            )

            if shutdown_event.is_set():
                break

            if pairs_data:
                opportunities = find_intra_chain_arbitrage_opportunities(
                    pairs_data,
                    MIN_PERCENTAGE_DIFF_THRESHOLD, MAX_PERCENTAGE_DIFF_THRESHOLD,
                    ESTIMATED_DEX_FEE_PERCENT, ESTIMATED_GAS_FEE_USD,
                    MIN_VOLUME_USD_H24, MIN_TRANSACTIONS_H24, MIN_LIQUIDITY_USD, MAX_BUY_SELL_TXN_RATIO,
                    PROFIT_GAS_RATIO_THRESHOLD, TRUSTED_DEX_IDS,
                    usd_capital, SLIPPAGE_PERCENT_LOW, SLIPPAGE_PERCENT_HIGH
                )
            else:
                opportunities = []
                logging.warning("Tidak ada data pairs yang ditemukan pada siklus ini.")

            # Tampilkan peluang
            display_opportunities_console(opportunities, usd_capital, IDR_CAPITAL, APPROX_IDR_USD_RATE)

            # Reset counter error karena siklus berhasil
            consecutive_errors = 0

        except asyncio.CancelledError:
            logging.info("Operasi asyncio dibatalkan.")
            break
        except Exception as e:
            consecutive_errors += 1
            logging.error(f"Error pada siklus #{cycle_count}: {e}", exc_info=True)

            if consecutive_errors >= max_consecutive_errors:
                logging.critical(f"Terlalu banyak error berturut-turut ({consecutive_errors}). Menghentikan program.")
                break

            # Tunggu sebentar sebelum mencoba lagi setelah error
            await asyncio.sleep(min(30, DELAY_AFTER_OUTPUT_SECONDS))

        # Hitung dan log durasi siklus
        cycle_duration = time.monotonic() - cycle_start_time
        logging.info(f"Durasi Siklus #{cycle_count}: {cycle_duration:.2f}s")

        # Tunggu sampai siklus berikutnya
        logging.info(f"Siklus #{cycle_count} selesai. Menunggu {DELAY_AFTER_OUTPUT_SECONDS} detik...")
        try:
            # Gunakan wait_for dengan timeout untuk menunggu shutdown_event
            await asyncio.wait_for(shutdown_event.wait(), timeout=DELAY_AFTER_OUTPUT_SECONDS)
            logging.info("Shutdown event set during delay.")
            break
        except asyncio.TimeoutError:
            # Timeout normal, lanjutkan ke siklus berikutnya
            pass
        except Exception as e:
            logging.error(f"Error during delay: {e}", exc_info=True)
            if shutdown_event.is_set():
                break

# --- Eksekusi Utama ---
if __name__ == "__main__":
    # Cek dependensi aiohttp
    AIOHTTP_AVAILABLE = 'aiohttp' in sys.modules and 'asyncio' in sys.modules
    if not AIOHTTP_AVAILABLE:
        console.print("[bold red]Error:[/bold red] Skrip ini memerlukan 'aiohttp'. Instal: [cyan]pip install aiohttp[/cyan]")
        sys.exit(1)

    # Setup signal handlers
    if 'signal' in sys.modules and signal is not None:
        signal.signal(signal.SIGINT, handle_shutdown_signal)
        signal.signal(signal.SIGTERM, handle_shutdown_signal)
    else:
        logging.warning("Modul 'signal' tidak tersedia. Penanganan Ctrl+C mungkin tidak optimal.")

    try:
        asyncio.run(async_main_loop())
    except KeyboardInterrupt: console.print("\n[bold yellow]KeyboardInterrupt diterima di level atas.[/bold yellow]")
    except Exception as e:
        console.print(f"\n[bold red]FATAL ERROR:[/bold red] Terjadi kesalahan tak terduga di level atas: {e}")
        logging.exception("Fatal error in main execution")
    finally:
        console.print("[bold blue]Program selesai.[/bold blue]")

