"""
Program untuk menjalankan main.py dengan timeout.
"""
import asyncio
import sys
import time
import signal
from concurrent.futures import ThreadPoolExecutor

async def run_main():
    """
    Menjalankan main.py dengan asyncio.
    """
    print("Menjalankan main.py...")
    
    # Impor main dan jalankan
    try:
        import main
        await main.main()
    except Exception as e:
        print(f"Error saat menjalankan main.py: {e}")
        import traceback
        traceback.print_exc()

def signal_handler(sig, frame):
    """
    Menangani sinyal interupsi (Ctrl+C).
    """
    print("\nMenerima sinyal interupsi. Menghentikan program dengan aman...")
    sys.exit(0)

async def main_with_timeout():
    """
    Menjalankan main.py dengan timeout.
    """
    # Tangani sinyal interupsi
    signal.signal(signal.SIGINT, signal_handler)
    
    # Jalankan main.py dengan timeout
    try:
        # Jalankan dengan timeout 60 detik
        await asyncio.wait_for(run_main(), timeout=60)
    except asyncio.TimeoutError:
        print("Timeout! Program berjalan terlalu lama.")
    except Exception as e:
        print(f"Error tidak terduga: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # Jalankan dengan asyncio
    asyncio.run(main_with_timeout())
