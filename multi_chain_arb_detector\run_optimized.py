"""
File untuk menjalankan program dengan pengaturan yang dioptimalkan.
"""
import os
import sys
import time
import asyncio
import traceback
import signal
import logging

# Variabel global untuk menangani sinyal interupsi
should_exit = False

def signal_handler(sig, frame):
    """
    Menangani sinyal interupsi (Ctrl+C).
    """
    global should_exit
    print("\nMenerima sinyal interupsi. Menghentikan program dengan aman...")
    should_exit = True

async def main():
    """
    Fungsi utama untuk menjalankan program.
    """
    # Tangani sinyal interupsi
    signal.signal(signal.SIGINT, signal_handler)
    
    print("Menjalankan program dengan pengaturan yang dioptimalkan...")
    
    # Dapatkan direktori script
    script_dir = os.path.dirname(os.path.abspath(__file__))
    
    # Tambahkan direktori saat ini ke sys.path
    sys.path.append(script_dir)
    
    # Impor modul-modul
    from src.utils import load_config, setup_logging
    from src.network import Network
    from src.arbitrage import ArbitrageDetector
    
    # Muat konfigurasi
    config_path = os.path.join(script_dir, "config.yaml")
    config = load_config(config_path)
    
    # Dapatkan pengaturan
    settings = config.get('settings', {})
    
    # Setup logging
    logs_dir = os.path.join(script_dir, "logs")
    os.makedirs(logs_dir, exist_ok=True)
    logger = setup_logging(settings.get('log_level', 'INFO'), logs_dir)
    
    # Simpan semua peluang arbitrase yang ditemukan
    all_opportunities = []
    
    # Waktu mulai
    start_time = time.time()
    
    # Loop utama
    iteration = 0
    try:
        while not should_exit:
            iteration += 1
            print(f"Iterasi #{iteration}: Memulai pemeriksaan peluang arbitrase...")
            
            # Buat tugas untuk setiap jaringan
            tasks = []
            detectors = []
            
            # Daftar jaringan yang akan dianalisis
            # Fokus pada jaringan dengan biaya gas rendah terlebih dahulu
            networks_to_analyze = ['polygon', 'bsc', 'avalanche', 'fantom', 'arbitrum', 'optimism', 'ethereum']
            
            for network_name in networks_to_analyze:
                if network_name in config['networks']:
                    try:
                        # Inisialisasi objek Network dengan pengaturan
                        network = Network(network_name, config['networks'][network_name], settings)
                        
                        # Inisialisasi detektor arbitrase
                        detector = ArbitrageDetector(network, config['networks'][network_name], settings)
                        detectors.append(detector)
                        
                        # Buat tugas untuk deteksi arbitrase
                        task = asyncio.create_task(detector.detect_arbitrage_opportunities())
                        tasks.append(task)
                    except Exception as e:
                        logger.error(f"Error saat menginisialisasi jaringan {network_name}: {e}")
            
            # Jalankan semua tugas secara bersamaan dengan penanganan error
            try:
                # Gunakan timeout untuk menghindari program hang
                timeout = settings.get('network_timeout_seconds', 180)
                results = await asyncio.wait_for(
                    asyncio.gather(*tasks, return_exceptions=True),
                    timeout=timeout
                )
                
                # Periksa hasil untuk error
                for i, result in enumerate(results):
                    if isinstance(result, Exception):
                        network_name = networks_to_analyze[i]
                        logger.error(f"Error saat mendeteksi arbitrase di jaringan {network_name}: {result}")
                        
                        # Jika error koneksi, tambahkan jeda lebih lama
                        if "connection" in str(result).lower() or "timeout" in str(result).lower():
                            logger.warning(f"Terdeteksi error koneksi di jaringan {network_name}. Menambahkan jeda lebih lama.")
                            # Jeda tambahan untuk menghindari rate limiting
                            await asyncio.sleep(settings.get('connection_error_pause_seconds', 15))
            except asyncio.TimeoutError:
                logger.warning(f"Timeout saat mendeteksi arbitrase. Melanjutkan ke iterasi berikutnya.")
            except Exception as e:
                logger.error(f"Error tidak terduga saat mendeteksi arbitrase: {e}")
                traceback.print_exc()
            
            # Kumpulkan peluang arbitrase dari semua detektor
            for detector in detectors:
                try:
                    if hasattr(detector, 'arbitrage_opportunities') and detector.arbitrage_opportunities:
                        all_opportunities.extend(detector.arbitrage_opportunities)
                        
                        # Tampilkan peluang arbitrase yang baru ditemukan
                        for opportunity in detector.arbitrage_opportunities:
                            print(f"\n[!] Peluang arbitrase ditemukan di {opportunity['network']}!")
                            print(f"    Jalur: {' -> '.join(opportunity['token_path'])}")
                            print(f"    DEX: {' -> '.join(opportunity['dex_path'])}")
                            print(f"    Profit: ${opportunity['net_profit_usd']:.2f} ({opportunity['profit_percentage']:.2f}%)")
                        
                        # Reset daftar peluang arbitrase di detektor
                        detector.arbitrage_opportunities = []
                except Exception as e:
                    logger.error(f"Error saat mengumpulkan peluang arbitrase: {e}")
            
            # Tampilkan ringkasan
            elapsed_time = time.time() - start_time
            hours, remainder = divmod(elapsed_time, 3600)
            minutes, seconds = divmod(remainder, 60)
            
            print(f"\nRingkasan setelah iterasi #{iteration}:")
            print(f"Waktu berjalan: {int(hours):02}:{int(minutes):02}:{int(seconds):02}")
            print(f"Total peluang arbitrase ditemukan: {len(all_opportunities)}")
            
            # Jika perlu keluar, keluar dari loop
            if should_exit:
                break
            
            # Tunggu interval yang dikonfigurasi
            interval = settings.get('check_interval_seconds', 15)
            print(f"Menunggu {interval} detik sebelum pemeriksaan berikutnya...")
            
            # Gunakan asyncio.sleep dengan pengecekan should_exit
            for i in range(interval):
                if should_exit:
                    break
                # Tampilkan countdown
                if (interval - i) % 5 == 0 and (interval - i) > 0:
                    print(f"  {interval - i} detik tersisa...")
                await asyncio.sleep(1)
    
    finally:
        # Tampilkan ringkasan akhir
        print("\n" + "=" * 70)
        print("                   RINGKASAN AKHIR")
        print("=" * 70)
        
        elapsed_time = time.time() - start_time
        hours, remainder = divmod(elapsed_time, 3600)
        minutes, seconds = divmod(remainder, 60)
        
        print(f"Total waktu berjalan: {int(hours):02}:{int(minutes):02}:{int(seconds):02}")
        print(f"Total iterasi: {iteration}")
        print(f"Total peluang arbitrase ditemukan: {len(all_opportunities)}")
        
        # Tampilkan peluang arbitrase terbaik
        if all_opportunities:
            # Urutkan berdasarkan profit bersih
            sorted_opportunities = sorted(all_opportunities, key=lambda x: x['net_profit_usd'], reverse=True)
            
            print("\nPeluang Arbitrase Terbaik:")
            for i, opportunity in enumerate(sorted_opportunities[:5]):  # Tampilkan 5 peluang terbaik
                print(f"\n{i+1}. Jaringan: {opportunity['network']}")
                print(f"   Jalur: {' -> '.join(opportunity['token_path'])}")
                print(f"   DEX: {' -> '.join(opportunity['dex_path'])}")
                print(f"   Profit: ${opportunity['net_profit_usd']:.2f} ({opportunity['profit_percentage']:.2f}%)")
        
        print("\nProgram berhenti dengan aman.")

if __name__ == "__main__":
    # Jalankan dengan asyncio
    asyncio.run(main())
