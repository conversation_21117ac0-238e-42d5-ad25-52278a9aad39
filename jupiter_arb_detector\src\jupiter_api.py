"""
Modul untuk berinteraksi dengan Jupiter API.
"""
import aiohttp
import asyncio
import logging
import time
import json
from typing import Dict, Any, List, Optional, Tuple

from .utils import exponential_backoff

logger = logging.getLogger("jupiter_arb_detector")

class JupiterAPI:
    """
    Kelas untuk berinteraksi dengan Jupiter API.
    """

    def __init__(self, settings: Dict[str, Any]):
        """
        Inisialisasi objek JupiterAPI.

        Args:
            settings: Pengaturan dari konfigurasi
        """
        self.settings = settings
        self.base_url = settings.get('api_base_url', 'https://quote-api.jup.ag')
        self.token_api_url = settings.get('token_api_url', '/v6/tokens')
        self.price_api_url = settings.get('price_api_url', '/v6/price')
        self.quote_api_url = settings.get('quote_api_url', '/v6/quote')

        self.base_token = settings.get('base_token', 'So11111111111111111111111111111111111111112')  # SOL
        self.usdc_token = settings.get('usdc_token', 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v')  # USDC

        self.request_timeout = settings.get('request_timeout_seconds', 15)
        self.max_retries = settings.get('max_retries', 5)
        self.retry_delay = settings.get('retry_delay_seconds', 2)

        # Pengaturan swap
        self.max_slippage_bps = settings.get('max_slippage_bps', 50)  # 0.5%
        self.fee_buffer_percentage = settings.get('fee_buffer_percentage', 0.3)  # 0.3%
        self.gas_buffer_percentage = settings.get('gas_buffer_percentage', 0.2)  # 0.2%

        # Pengaturan validasi
        self.validate_liquidity = settings.get('validate_liquidity', True)
        self.validate_price_impact = settings.get('validate_price_impact', True)
        self.max_price_impact_percentage = settings.get('max_price_impact_percentage', 1.0)  # 1%

        # Pengaturan rute
        self.only_direct_routes = settings.get('only_direct_routes', False)
        self.max_route_hops = settings.get('max_route_hops', 2)

        # Cache untuk mengurangi jumlah permintaan API
        self.token_cache = {}
        self.price_cache = {}
        self.quote_cache = {}
        self.cache_ttl = settings.get('cache_ttl_seconds', 60)  # 1 menit

        logger.info(f"JupiterAPI diinisialisasi dengan base URL: {self.base_url}")

    async def _make_request(self, url: str, params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Membuat permintaan HTTP ke Jupiter API dengan retry.

        Args:
            url: URL untuk permintaan
            params: Parameter untuk permintaan

        Returns:
            Response dari API
        """
        full_url = f"{self.base_url}{url}"

        for attempt in range(self.max_retries):
            try:
                async with aiohttp.ClientSession() as session:
                    async with session.get(
                        full_url,
                        params=params,
                        timeout=self.request_timeout
                    ) as response:
                        if response.status == 200:
                            return await response.json()
                        else:
                            logger.warning(f"API mengembalikan status {response.status} untuk {full_url}")
                            text = await response.text()
                            logger.debug(f"Response: {text}")

                            # Jika rate limit, tunggu lebih lama
                            if response.status == 429:
                                delay = exponential_backoff(
                                    attempt,
                                    base_delay=self.retry_delay * 2,
                                    max_delay=30
                                )
                                logger.warning(f"Rate limit terdeteksi. Menunggu {delay:.2f} detik.")
                                await asyncio.sleep(delay)
                                continue

                            # Jika error server, coba lagi
                            if response.status >= 500:
                                delay = exponential_backoff(
                                    attempt,
                                    base_delay=self.retry_delay,
                                    max_delay=10
                                )
                                logger.warning(f"Error server terdeteksi. Menunggu {delay:.2f} detik.")
                                await asyncio.sleep(delay)
                                continue

                            # Error lain
                            raise Exception(f"API mengembalikan status {response.status}: {text}")
            except asyncio.TimeoutError:
                logger.warning(f"Timeout saat mengakses {full_url}")

                # Jika ini adalah percobaan terakhir, raise exception
                if attempt == self.max_retries - 1:
                    raise

                # Tunggu sebelum mencoba lagi
                delay = exponential_backoff(
                    attempt,
                    base_delay=self.retry_delay,
                    max_delay=10
                )
                logger.warning(f"Menunggu {delay:.2f} detik sebelum mencoba lagi.")
                await asyncio.sleep(delay)
            except Exception as e:
                logger.warning(f"Error saat mengakses {full_url}: {e}")

                # Jika ini adalah percobaan terakhir, raise exception
                if attempt == self.max_retries - 1:
                    raise

                # Tunggu sebelum mencoba lagi
                delay = exponential_backoff(
                    attempt,
                    base_delay=self.retry_delay,
                    max_delay=10
                )
                logger.warning(f"Menunggu {delay:.2f} detik sebelum mencoba lagi.")
                await asyncio.sleep(delay)

        # Jika semua percobaan gagal
        raise Exception(f"Semua percobaan gagal untuk {full_url}")

    async def get_tradable_tokens(self) -> List[Dict[str, Any]]:
        """
        Mendapatkan daftar token yang dapat diperdagangkan di Jupiter.

        Returns:
            Daftar token
        """
        # Cek cache
        cache_key = "tradable_tokens"
        if cache_key in self.token_cache:
            cache_time, cache_data = self.token_cache[cache_key]
            if time.time() - cache_time < self.cache_ttl:
                logger.debug(f"Menggunakan cache untuk {cache_key}")
                return cache_data

        logger.info("Mendapatkan daftar token yang dapat diperdagangkan di Jupiter")

        try:
            # Dapatkan daftar token yang dapat diperdagangkan
            # API v6 mengembalikan semua token dalam satu panggilan
            all_tokens_response = await self._make_request(self.token_api_url)

            if not isinstance(all_tokens_response, list):
                logger.warning(f"Respons token tidak valid: {all_tokens_response}")
                return []

            # Filter token berdasarkan kriteria
            filtered_tokens = []
            min_daily_volume = self.settings.get('min_daily_volume', 10000)
            min_liquidity = self.settings.get('min_liquidity', 5000)
            exclude_tags = self.settings.get('exclude_tags', ["meme", "scam", "test", "deprecated"])
            include_only_verified = self.settings.get('include_only_verified', True)

            for token in all_tokens_response:
                # Lewati token yang tidak memiliki informasi yang diperlukan
                if not all(key in token for key in ["address", "symbol", "name", "decimals"]):
                    continue

                # Filter berdasarkan tag
                if "tags" in token and any(tag in token["tags"] for tag in exclude_tags):
                    continue

                # Filter berdasarkan verifikasi
                if include_only_verified and not token.get("verified", False):
                    continue

                # Filter berdasarkan volume
                if "volume24h" in token and token["volume24h"] < min_daily_volume:
                    continue

                # Filter berdasarkan likuiditas
                if "liquidity" in token and token["liquidity"] < min_liquidity:
                    continue

                filtered_tokens.append(token)

            # Batasi jumlah token jika terlalu banyak
            max_tokens = self.settings.get('max_tokens', 1000)
            if len(filtered_tokens) > max_tokens:
                logger.info(f"Membatasi jumlah token dari {len(filtered_tokens)} menjadi {max_tokens}")
                filtered_tokens = filtered_tokens[:max_tokens]

            # Simpan ke cache
            self.token_cache[cache_key] = (time.time(), filtered_tokens)

            logger.info(f"Berhasil mendapatkan {len(filtered_tokens)} token yang dapat diperdagangkan")
            return filtered_tokens
        except Exception as e:
            logger.error(f"Gagal mendapatkan daftar token yang dapat diperdagangkan: {e}")
            # Kembalikan daftar kosong jika gagal
            return []

    async def get_token_info(self, token_address: str) -> Dict[str, Any]:
        """
        Mendapatkan informasi token.

        Args:
            token_address: Alamat token

        Returns:
            Informasi token
        """
        # Cek cache
        cache_key = f"token_info_{token_address}"
        if cache_key in self.token_cache:
            cache_time, cache_data = self.token_cache[cache_key]
            if time.time() - cache_time < self.cache_ttl:
                logger.debug(f"Menggunakan cache untuk {cache_key}")
                return cache_data

        logger.debug(f"Mendapatkan informasi untuk token {token_address}")

        # Dapatkan informasi token dari API v6
        # Dalam API v6, kita perlu mendapatkan semua token dan mencari yang sesuai
        all_tokens = await self.get_tradable_tokens()

        # Cari token yang sesuai
        token_info = None
        for token in all_tokens:
            if token.get("address") == token_address:
                token_info = token
                break

        if not token_info:
            raise Exception(f"Token tidak ditemukan: {token_address}")

        # Simpan ke cache
        self.token_cache[cache_key] = (time.time(), token_info)

        return token_info

    async def get_token_price(self, token_address: str) -> float:
        """
        Mendapatkan harga token dalam USD.

        Args:
            token_address: Alamat token

        Returns:
            Harga token dalam USD
        """
        # Cek cache
        cache_key = f"token_price_{token_address}"
        if cache_key in self.price_cache:
            cache_time, cache_data = self.price_cache[cache_key]
            if time.time() - cache_time < self.cache_ttl:
                logger.debug(f"Menggunakan cache untuk {cache_key}")
                return cache_data

        logger.debug(f"Mendapatkan harga untuk token {token_address}")

        try:
            # Dapatkan harga token dari API v6
            # Format parameter untuk API v6
            params = {
                "ids": token_address
            }

            price_data = await self._make_request(self.price_api_url, params)

            # Ekstrak harga dari response
            if isinstance(price_data, dict) and "data" in price_data and token_address in price_data["data"]:
                price = float(price_data["data"][token_address]["price"])

                # Simpan ke cache
                self.price_cache[cache_key] = (time.time(), price)

                return price
            else:
                # Jika tidak dapat mendapatkan harga dari API, coba dapatkan dari informasi token
                token_info = await self.get_token_info(token_address)
                if "price" in token_info:
                    price = float(token_info["price"])

                    # Simpan ke cache
                    self.price_cache[cache_key] = (time.time(), price)

                    return price

                # Jika masih tidak dapat mendapatkan harga, coba dapatkan dari USDC
                if token_address == self.usdc_token:
                    # USDC selalu 1 USD
                    return 1.0

                # Jika token adalah SOL, gunakan harga default
                if token_address == self.base_token:
                    # Harga SOL default
                    return 150.0

                raise Exception(f"Tidak dapat mendapatkan harga untuk token {token_address}")
        except Exception as e:
            logger.warning(f"Error saat mendapatkan harga untuk token {token_address}: {e}")

            # Jika token adalah USDC, kembalikan 1.0
            if token_address == self.usdc_token:
                return 1.0

            # Jika token adalah SOL, kembalikan harga default
            if token_address == self.base_token:
                return 150.0

            # Coba dapatkan harga dari informasi token
            try:
                token_info = await self.get_token_info(token_address)
                if "price" in token_info:
                    return float(token_info["price"])
            except Exception:
                pass

            # Jika semua cara gagal, raise exception
            raise Exception(f"Tidak dapat mendapatkan harga untuk token {token_address}")

    async def get_quote(
        self,
        input_mint: str,
        output_mint: str,
        amount: int,
        slippage_bps: int = 50
    ) -> Dict[str, Any]:
        """
        Mendapatkan quote untuk swap.

        Args:
            input_mint: Alamat token input
            output_mint: Alamat token output
            amount: Jumlah token input (dalam satuan terkecil)
            slippage_bps: Slippage dalam basis poin (1% = 100)

        Returns:
            Quote untuk swap
        """
        # Cek cache
        cache_key = f"quote_{input_mint}_{output_mint}_{amount}_{slippage_bps}"
        if cache_key in self.quote_cache:
            cache_time, cache_data = self.quote_cache[cache_key]
            if time.time() - cache_time < self.cache_ttl:
                logger.debug(f"Menggunakan cache untuk {cache_key}")
                return cache_data

        logger.debug(f"Mendapatkan quote untuk swap {input_mint} -> {output_mint} dengan jumlah {amount}")

        try:
            # Parameter untuk API v6
            params = {
                "inputMint": input_mint,
                "outputMint": output_mint,
                "amount": str(amount),  # API v6 mengharapkan string
                "slippageBps": slippage_bps,
                "onlyDirectRoutes": self.only_direct_routes,
                "maxAccounts": 10,  # Batasi jumlah akun untuk kecepatan
                "asLegacyTransaction": False,  # Gunakan transaksi versi baru
                "maxRoutes": 3,  # Batasi jumlah rute untuk kecepatan
                "swapMode": "ExactIn"  # Jumlah input tetap
            }

            # Dapatkan quote dari API v6
            quote = await self._make_request(self.quote_api_url, params)

            # Validasi quote
            if not quote or "outAmount" not in quote:
                raise Exception(f"Quote tidak valid: {quote}")

            # Validasi dampak harga jika diperlukan
            if self.validate_price_impact and "priceImpactPct" in quote:
                price_impact_pct = float(quote["priceImpactPct"])
                if price_impact_pct > self.max_price_impact_percentage:
                    logger.warning(f"Dampak harga terlalu tinggi: {price_impact_pct}% > {self.max_price_impact_percentage}%")
                    raise Exception(f"Dampak harga terlalu tinggi: {price_impact_pct}%")

            # Simpan ke cache
            self.quote_cache[cache_key] = (time.time(), quote)

            return quote
        except Exception as e:
            logger.warning(f"Error saat mendapatkan quote untuk swap {input_mint} -> {output_mint}: {e}")
            raise

    async def validate_swap_opportunity(
        self,
        input_mint: str,
        output_mint: str,
        amount: int,
        input_price_usd: float,
        output_price_usd: float
    ) -> Dict[str, Any]:
        """
        Memvalidasi peluang swap.

        Args:
            input_mint: Alamat token input
            output_mint: Alamat token output
            amount: Jumlah token input (dalam satuan terkecil)
            input_price_usd: Harga token input dalam USD
            output_price_usd: Harga token output dalam USD

        Returns:
            Hasil validasi
        """
        try:
            # Dapatkan quote untuk swap
            quote = await self.get_quote(
                input_mint,
                output_mint,
                amount,
                self.max_slippage_bps
            )

            # Dapatkan informasi token input dan output
            input_token = await self.get_token_info(input_mint)
            output_token = await self.get_token_info(output_mint)

            # Hitung nilai input dalam USD
            input_amount_decimal = amount / (10 ** input_token["decimals"])
            input_value_usd = input_amount_decimal * input_price_usd

            # Hitung nilai output dalam USD
            output_amount = int(quote["outAmount"])
            output_amount_decimal = output_amount / (10 ** output_token["decimals"])
            output_value_usd = output_amount_decimal * output_price_usd

            # Hitung profit
            profit_usd = output_value_usd - input_value_usd
            profit_percentage = (profit_usd / input_value_usd) * 100

            # Hitung biaya transaksi
            fee_usd = input_value_usd * self.fee_buffer_percentage / 100
            gas_usd = input_value_usd * self.gas_buffer_percentage / 100
            total_cost_usd = fee_usd + gas_usd

            # Hitung profit bersih
            net_profit_usd = profit_usd - total_cost_usd
            net_profit_percentage = (net_profit_usd / input_value_usd) * 100

            # Hasil validasi
            result = {
                "valid": net_profit_percentage > 0,
                "input_mint": input_mint,
                "output_mint": output_mint,
                "input_symbol": input_token["symbol"],
                "output_symbol": output_token["symbol"],
                "input_amount": input_amount_decimal,
                "output_amount": output_amount_decimal,
                "input_value_usd": input_value_usd,
                "output_value_usd": output_value_usd,
                "profit_usd": profit_usd,
                "profit_percentage": profit_percentage,
                "fee_usd": fee_usd,
                "gas_usd": gas_usd,
                "total_cost_usd": total_cost_usd,
                "net_profit_usd": net_profit_usd,
                "net_profit_percentage": net_profit_percentage,
                "quote": quote
            }

            return result
        except Exception as e:
            logger.warning(f"Error saat memvalidasi peluang swap {input_mint} -> {output_mint}: {e}")
            return {
                "valid": False,
                "error": str(e)
            }
