# 🎛️ Panduan Penggunaan <PERSON>aman Konfigurasi IntelliTrader X

## 🚀 Pengenalan

Halaman konfigurasi IntelliTrader X memungkinkan Anda untuk menyesuaikan semua parameter trading sesuai dengan strategi dan preferensi Anda. Interface yang user-friendly ini memberikan kontrol penuh atas algoritma trading advanced.

## 📖 Cara Mengakses Halaman Konfigurasi

### Dari Halaman Utama
1. Klik tombol **"⚙️ Pengaturan"** di halaman utama
2. Atau dari halaman hasil sinyal, klik **"⚙️ Pengaturan"**

### Navigasi
- Gunakan **tab** di bagian atas untuk berpindah antar kategori
- Klik **"🔙 Kembali"** untuk kembali ke halaman utama
- Semua perubahan disimpan secara otomatis

## 🏠 Tab Umum - Pengaturan Dasar

### Timeframe Analysis
**Fungsi**: Pilih timeframe mana yang akan dianalisis oleh sistem

**Cara <PERSON>**:
- ✅ Centang timeframe yang ingin dianalisis
- ⚠️ Minimal 1 timeframe harus dipilih
- 💡 **Rekomendasi**: Gunakan 3-5 timeframe untuk analisis optimal

**Contoh Setting**:
- **Conservative**: 4h, 1h, 30m
- **Aggressive**: 1h, 30m, 15m, 5m
- **Comprehensive**: 4h, 1h, 30m, 15m, 5m

### Mode Operasi
- **Discovery Mode**: Mode pencarian sinyal dengan threshold permisif
- **Emergency Mode**: Mode darurat untuk deteksi sinyal maksimum
- **Debug Filtering**: Aktifkan logging detail untuk troubleshooting
- **Demo Mode**: Mode testing dengan data terbatas

## ⚡ Tab Performance - Optimasi Kinerja

### Threading Settings
**Max Workers Analysis**: Jumlah thread untuk analisis paralel
- **Range**: 1-32
- **Rekomendasi**: 2x jumlah CPU cores
- **Impact**: Lebih tinggi = analisis lebih cepat, tapi konsumsi RAM lebih besar

**Max Concurrent Downloads**: Jumlah download simultan
- **Range**: 10-200
- **Rekomendasi**: 50-100 untuk koneksi stabil
- **Impact**: Lebih tinggi = download lebih cepat, tapi beban server lebih besar

### API Settings
**API Request Delay**: Delay antar request (ms)
- **Range**: 0-1000ms
- **Rekomendasi**: 50ms untuk menghindari rate limit
- **Impact**: Lebih rendah = lebih cepat, tapi risiko rate limit

## 🎯 Tab Thresholds - Pengaturan Ambang Batas

### Confidence Settings
**Min Confidence Threshold**: Ambang batas minimum untuk sinyal
- **Range**: 0.1-1.0 (10%-100%)
- **Default**: 0.30 (30%)
- **Rekomendasi**: 
  - **Conservative**: 0.60-0.80
  - **Moderate**: 0.40-0.60
  - **Aggressive**: 0.20-0.40

**Advanced Signal Threshold**: Threshold untuk sinyal berkualitas tinggi
- **Range**: 0.5-2.0
- **Default**: 0.75
- **Impact**: Lebih tinggi = sinyal lebih selektif tapi lebih akurat

## ⚖️ Tab Indikator - Bobot Indikator

### Kategori Indikator

#### Smart Money Concepts (Bobot Tinggi)
- **Market Structure Trend**: 4.5 (Sangat Penting)
- **SMC Zone Reaction**: 4.0 (Penting)
- **Liquidity Event**: 3.0 (Sedang)

#### Volume & Confluence
- **Multi-TF Alignment**: 5.0 (Maksimum - Paling Penting)
- **Volume Confirmation**: 3.5 (Tinggi)
- **Volume Profile**: 2.8 (Sedang-Tinggi)

#### Technical Analysis
- **Momentum Divergence Strong**: 3.5 (Tinggi)
- **HTF Trend Clarity**: 2.5 (Sedang)
- **General TA Score**: 1.5 (Rendah)

### Tips Pengaturan Bobot
- **Untuk Scalping**: Tingkatkan bobot "Price Action Patterns" dan "Momentum Oscillators"
- **Untuk Swing Trading**: Tingkatkan bobot "HTF Trend Clarity" dan "Support Resistance"
- **Untuk SMC Strategy**: Maksimalkan bobot kategori "Smart Money Concepts"

## 🔍 Tab Filtering - Filter Pasar

### Stablecoin Filtering
**Enable Stablecoin Filtering**: Hilangkan pasangan stablecoin dari analisis
- **Rekomendasi**: ✅ Aktifkan untuk fokus pada crypto volatil
- **Impact**: Mengurangi noise dari pasangan stablecoin yang kurang volatil

### Volume & Volatility Filter
**Min Volume 24H (USD)**: Volume minimum untuk dianalisis
- **Default**: 100,000 USD
- **Rekomendasi**: 
  - **High Liquidity**: 1,000,000+ USD
  - **Medium Liquidity**: 500,000 USD
  - **Low Liquidity**: 100,000 USD

**ATR Percentage Range**: Filter berdasarkan volatilitas
- **Min ATR**: 0.01% (sangat rendah)
- **Max ATR**: 50% (sangat tinggi)
- **Rekomendasi**: 0.5% - 10% untuk trading normal

## 🔧 Tab Advanced - Pengaturan Teknikal

### Per-Timeframe Settings
Setiap timeframe memiliki pengaturan indikator yang dapat disesuaikan:

#### RSI Settings
- **RSI Period**: Periode kalkulasi RSI
- **RSI Oversold/Overbought**: Level oversold dan overbought

#### MACD Settings
- **MACD Fast/Slow/Signal**: Periode untuk kalkulasi MACD

#### EMA Settings
- **EMA Short/Medium/Long**: Periode untuk moving averages

### Rekomendasi per Timeframe

#### 4H (Long-term)
- RSI Period: 14, Oversold: 25, Overbought: 75
- MACD: 12/26/9
- EMA: 34/89/200

#### 1H (Medium-term)
- RSI Period: 14, Oversold: 28, Overbought: 72
- MACD: 12/26/9
- EMA: 21/55/100

#### 15M (Short-term)
- RSI Period: 10, Oversold: 20, Overbought: 80
- MACD: 9/18/6
- EMA: 9/21/34

## 💾 Manajemen Konfigurasi

### Save Configuration
1. Klik **"💾 Simpan Konfigurasi"**
2. Pilih lokasi dan nama file
3. File disimpan dalam format JSON

### Load Configuration
1. Klik **"📂 Muat Konfigurasi"**
2. Pilih file konfigurasi (.json)
3. Konfigurasi akan diterapkan otomatis

### Reset to Defaults
1. Klik **"🔄 Reset ke Default"**
2. Konfirmasi reset
3. Semua pengaturan kembali ke nilai default

### Apply Configuration
1. Klik **"✅ Terapkan Konfigurasi"**
2. Konfigurasi diterapkan ke sistem
3. Perubahan efektif pada analisis berikutnya

## 🎯 Strategi Konfigurasi Berdasarkan Trading Style

### Scalping (1-5 menit)
```
Timeframes: 5m, 15m, 30m
Confidence: 0.40-0.60
Bobot Tinggi: Price Action Patterns, Momentum Oscillators
Volume Filter: 500K+ USD
```

### Day Trading (15 menit - 4 jam)
```
Timeframes: 15m, 30m, 1h, 4h
Confidence: 0.50-0.70
Bobot Tinggi: Multi-TF Alignment, Volume Confirmation
Volume Filter: 1M+ USD
```

### Swing Trading (4 jam - 1 hari)
```
Timeframes: 4h, 1d
Confidence: 0.60-0.80
Bobot Tinggi: HTF Trend Clarity, Support Resistance
Volume Filter: 2M+ USD
```

### SMC Strategy
```
Timeframes: 1h, 4h, 1d
Confidence: 0.70+
Bobot Tinggi: Market Structure Trend, SMC Zone Reaction
Emergency Mode: OFF (untuk presisi tinggi)
```

## ⚠️ Tips & Best Practices

### Do's ✅
- **Test Incremental**: Ubah satu parameter pada satu waktu
- **Backup Configs**: Simpan konfigurasi yang bekerja baik
- **Monitor Results**: Pantau hasil setelah perubahan konfigurasi
- **Document Changes**: Catat alasan perubahan konfigurasi

### Don'ts ❌
- **Jangan ubah semua parameter sekaligus**
- **Jangan set confidence terlalu rendah (<20%)**
- **Jangan gunakan terlalu banyak timeframe (>6)**
- **Jangan abaikan volume filter**

### Troubleshooting
- **Tidak ada sinyal**: Turunkan confidence threshold
- **Terlalu banyak sinyal**: Naikkan confidence threshold
- **Analisis lambat**: Kurangi jumlah timeframe atau workers
- **Error rate limit**: Naikkan API request delay

---

**💡 Pro Tip**: Mulai dengan konfigurasi default, lalu sesuaikan secara bertahap berdasarkan hasil trading Anda. Setiap perubahan konfigurasi sebaiknya ditest minimal 1 minggu sebelum dievaluasi efektivitasnya.

**🔗 Support**: Jika mengalami kesulitan, dokumentasikan pengaturan Anda dan hubungi support dengan detail konfigurasi yang digunakan.
