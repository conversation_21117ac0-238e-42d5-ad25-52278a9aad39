"""
Modul utilitas untuk program deteksi arbitrase multi-chain.
"""
import os
import yaml
import json
import logging
import time
from typing import Dict, Any, List, Optional, Union, Tuple
from pathlib import Path
from rich.console import Console
from rich.logging import RichHandler
from rich.table import Table
from rich.panel import Panel
from rich.text import Text
from web3 import Web3

# Inisialisasi Rich console
console = Console()

def setup_logging(log_level: str = "INFO", log_dir: str = "logs") -> logging.Logger:
    """
    Mengatur logging dengan format yang bagus menggunakan Rich.

    Args:
        log_level: Level logging (DEBUG, INFO, WARNING, ERROR)
        log_dir: Direktori untuk menyimpan file log

    Returns:
        Logger yang telah dikonfigurasi
    """
    # Pastikan direktori log ada
    os.makedirs(log_dir, exist_ok=True)

    # Konversi string log level ke konstanta logging
    numeric_level = getattr(logging, log_level.upper(), None)
    if not isinstance(numeric_level, int):
        raise ValueError(f"Level log tidak valid: {log_level}")

    # Konfigurasi logging
    logging.basicConfig(
        level=numeric_level,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[
            RichHandler(rich_tracebacks=True, markup=True),
            logging.FileHandler(f"{log_dir}/arbitrage_detector.log")
        ]
    )

    return logging.getLogger("arbitrage_detector")

def load_config(config_path: str) -> Dict[str, Any]:
    """
    Memuat konfigurasi dari file YAML.

    Args:
        config_path: Path ke file konfigurasi

    Returns:
        Dictionary berisi konfigurasi
    """
    try:
        with open(config_path, 'r') as file:
            config = yaml.safe_load(file)
        return config
    except Exception as e:
        console.print(f"[bold red]Error saat memuat konfigurasi: {e}[/bold red]")
        raise

def load_abi(abi_path: str) -> List[Dict[str, Any]]:
    """
    Memuat ABI dari file JSON.

    Args:
        abi_path: Path ke file ABI

    Returns:
        ABI dalam format list dictionary
    """
    try:
        # Gunakan path relatif terhadap file main.py
        script_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        full_path = os.path.join(script_dir, abi_path)

        with open(full_path, 'r') as file:
            abi = json.load(file)
        return abi
    except Exception as e:
        console.print(f"[bold red]Error saat memuat ABI: {e}[/bold red]")
        raise

def wei_to_ether(wei_value: int) -> float:
    """
    Mengkonversi nilai Wei ke Ether.

    Args:
        wei_value: Nilai dalam Wei

    Returns:
        Nilai dalam Ether
    """
    return Web3.from_wei(wei_value, 'ether')

def ether_to_wei(ether_value: float) -> int:
    """
    Mengkonversi nilai Ether ke Wei.

    Args:
        ether_value: Nilai dalam Ether

    Returns:
        Nilai dalam Wei
    """
    return Web3.to_wei(ether_value, 'ether')

def format_amount_with_decimals(amount: int, decimals: int) -> float:
    """
    Memformat jumlah token dengan mempertimbangkan desimal.

    Args:
        amount: Jumlah token dalam satuan terkecil (misalnya wei)
        decimals: Jumlah desimal token

    Returns:
        Jumlah token yang diformat
    """
    return amount / (10 ** decimals)

def calculate_price_impact(amount_in: int, amount_out: int, decimals_in: int, decimals_out: int) -> float:
    """
    Menghitung dampak harga dari sebuah perdagangan.

    Args:
        amount_in: Jumlah token masuk dalam satuan terkecil
        amount_out: Jumlah token keluar dalam satuan terkecil
        decimals_in: Jumlah desimal token masuk
        decimals_out: Jumlah desimal token keluar

    Returns:
        Dampak harga dalam persentase
    """
    amount_in_formatted = format_amount_with_decimals(amount_in, decimals_in)
    amount_out_formatted = format_amount_with_decimals(amount_out, decimals_out)

    # Hitung dampak harga
    price = amount_out_formatted / amount_in_formatted
    return price

def calculate_gas_cost_usd(gas_used: int, gas_price: int, eth_price_usd: float) -> float:
    """
    Menghitung biaya gas dalam USD.

    Args:
        gas_used: Jumlah gas yang digunakan
        gas_price: Harga gas dalam wei
        eth_price_usd: Harga ETH dalam USD

    Returns:
        Biaya gas dalam USD
    """
    gas_cost_eth = wei_to_ether(gas_used * gas_price)
    return gas_cost_eth * eth_price_usd

def apply_slippage(amount: int, slippage_percentage: float, is_input: bool = False) -> int:
    """
    Menerapkan slippage ke jumlah token.

    Args:
        amount: Jumlah token
        slippage_percentage: Persentase slippage
        is_input: True jika jumlah adalah input, False jika output

    Returns:
        Jumlah token setelah slippage diterapkan
    """
    slippage_factor = slippage_percentage / 100

    if is_input:
        # Untuk input, kita meningkatkan jumlah
        return int(amount * (1 + slippage_factor))
    else:
        # Untuk output, kita mengurangi jumlah
        return int(amount * (1 - slippage_factor))

def format_token_amount(amount: int, decimals: int, symbol: str) -> str:
    """
    Memformat jumlah token untuk ditampilkan.

    Args:
        amount: Jumlah token dalam satuan terkecil
        decimals: Jumlah desimal token
        symbol: Simbol token

    Returns:
        String yang diformat
    """
    formatted_amount = format_amount_with_decimals(amount, decimals)
    return f"{formatted_amount:.6f} {symbol}"

def display_arbitrage_opportunity(
    network: str,
    token_path: List[str],
    dex_path: List[str],
    amounts: List[int],
    token_symbols: List[str],
    token_decimals: List[int],
    profit_usd: float,
    gas_cost_usd: float,
    net_profit_usd: float,
    profit_percentage: float
) -> None:
    """
    Menampilkan peluang arbitrase dengan format yang bagus menggunakan Rich.

    Args:
        network: Nama jaringan
        token_path: Jalur token
        dex_path: Jalur DEX
        amounts: Jumlah token di setiap langkah
        token_symbols: Simbol token
        token_decimals: Desimal token
        profit_usd: Profit dalam USD
        gas_cost_usd: Biaya gas dalam USD
        net_profit_usd: Profit bersih dalam USD
        profit_percentage: Persentase profit
    """
    # Buat tabel untuk jalur arbitrase
    table = Table(title=f"Peluang Arbitrase di {network}")

    table.add_column("Langkah", justify="center", style="cyan")
    table.add_column("DEX", justify="center", style="green")
    table.add_column("Token Masuk", justify="right", style="yellow")
    table.add_column("Token Keluar", justify="right", style="yellow")

    for i in range(len(dex_path)):
        in_token = token_symbols[i]
        out_token = token_symbols[i+1] if i < len(token_symbols) - 1 else token_symbols[0]

        in_amount = format_token_amount(amounts[i], token_decimals[i], in_token)
        out_amount = format_token_amount(amounts[i+1] if i < len(amounts) - 1 else amounts[0],
                                        token_decimals[i+1] if i < len(token_decimals) - 1 else token_decimals[0],
                                        out_token)

        table.add_row(
            f"{i+1}",
            dex_path[i],
            in_amount,
            out_amount
        )

    # Buat panel untuk informasi profit
    profit_text = Text()
    profit_text.append("💰 Profit Kotor: ", style="bold")
    profit_text.append(f"${profit_usd:.2f}\n", style="green")

    profit_text.append("⛽ Biaya Gas: ", style="bold")
    profit_text.append(f"${gas_cost_usd:.2f}\n", style="red")

    profit_text.append("💵 Profit Bersih: ", style="bold")
    if net_profit_usd > 0:
        profit_text.append(f"${net_profit_usd:.2f}\n", style="green bold")
    else:
        profit_text.append(f"${net_profit_usd:.2f}\n", style="red bold")

    profit_text.append("📈 Persentase Profit: ", style="bold")
    if profit_percentage > 0:
        profit_text.append(f"{profit_percentage:.2f}%", style="green bold")
    else:
        profit_text.append(f"{profit_percentage:.2f}%", style="red bold")

    profit_panel = Panel(profit_text, title="Analisis Profit", border_style="green")

    # Tampilkan tabel dan panel
    console.print(table)
    console.print(profit_panel)
    console.print("\n")

def exponential_backoff(attempt: int, base_delay: float = 1.0, max_delay: float = 30.0, jitter_factor: float = 0.1) -> float:
    """
    Menghitung waktu tunggu dengan backoff eksponensial.

    Args:
        attempt: Nomor percobaan (dimulai dari 0)
        base_delay: Waktu tunggu dasar dalam detik
        max_delay: Waktu tunggu maksimum dalam detik
        jitter_factor: Faktor jitter (0.0 - 1.0) untuk menambahkan keacakan

    Returns:
        Waktu tunggu dalam detik
    """
    # Hitung delay eksponensial
    delay = min(base_delay * (2 ** attempt), max_delay)

    # Tambahkan jitter untuk menghindari thundering herd problem
    if jitter_factor > 0:
        jitter = delay * jitter_factor * (time.time() % 1)
    else:
        jitter = 0

    return delay + jitter
