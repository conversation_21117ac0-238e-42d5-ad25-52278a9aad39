"""
File untuk menjalankan program dengan mode debug.
"""
import os
import sys
import time
import asyncio
import traceback

async def main():
    """
    Fungsi utama untuk menjalankan program.
    """
    print("Menjalankan program dengan mode debug...")

    # Dapatkan direktori script
    script_dir = os.path.dirname(os.path.abspath(__file__))
    print(f"Direktori script: {script_dir}")

    # Cek apakah file main.py ada
    main_path = os.path.join(script_dir, "main.py")
    print(f"Path main.py: {main_path}")

    if os.path.exists(main_path):
        print("File main.py ditemukan!")

        # Cek apakah file config.yaml ada
        config_path = os.path.join(script_dir, "config.yaml")
        print(f"Path config.yaml: {config_path}")

        if os.path.exists(config_path):
            print("File config.yaml ditemukan!")

            # Cek apakah direktori logs ada
            logs_dir = os.path.join(script_dir, "logs")
            print(f"Path direktori logs: {logs_dir}")

            if not os.path.exists(logs_dir):
                print("Direktori logs tidak ditemukan! Membuat direktori...")
                os.makedirs(logs_dir, exist_ok=True)

            # Cek apakah direktori abis ada
            abis_dir = os.path.join(script_dir, "abis")
            print(f"Path direktori abis: {abis_dir}")

            if not os.path.exists(abis_dir):
                print("Direktori abis tidak ditemukan!")
                return

            # Cek apakah file ABI ada
            abi_files = ["UniswapV2Router.json", "UniswapV3Quoter.json", "ERC20.json"]
            for abi_file in abi_files:
                abi_path = os.path.join(abis_dir, abi_file)
                if not os.path.exists(abi_path):
                    print(f"File ABI {abi_file} tidak ditemukan!")
                    return

            print("Semua file ABI ditemukan!")

            # Cek apakah direktori src ada
            src_dir = os.path.join(script_dir, "src")
            print(f"Path direktori src: {src_dir}")

            if not os.path.exists(src_dir):
                print("Direktori src tidak ditemukan!")
                return

            # Cek apakah file-file src ada
            src_files = ["__init__.py", "network.py", "dex.py", "arbitrage.py", "utils.py", "optimizations.py", "token_lists.py"]
            for src_file in src_files:
                src_path = os.path.join(src_dir, src_file)
                if not os.path.exists(src_path):
                    print(f"File {src_file} tidak ditemukan!")
                    return

            print("Semua file src ditemukan!")

            # Coba impor modul-modul
            try:
                print("\nMencoba mengimpor modul-modul...")

                # Tambahkan direktori saat ini ke sys.path
                sys.path.append(script_dir)

                # Impor modul-modul
                from src.utils import load_config, setup_logging
                from src.network import Network
                from src.dex import DEX
                from src.arbitrage import ArbitrageDetector
                from src.optimizations import BatchRequestManager, TokenPairCache, TokenFilter
                from src.token_lists import get_default_tokens

                print("Semua modul berhasil diimpor!")

                # Muat konfigurasi
                print("\nMemuat konfigurasi...")
                config = load_config(config_path)
                print("Konfigurasi berhasil dimuat!")

                # Dapatkan pengaturan
                settings = config.get('settings', {})

                # Setup logging
                print("\nMengatur logging...")
                logger = setup_logging(settings.get('log_level', 'INFO'), logs_dir)
                print("Logging berhasil diatur!")

                # Coba terhubung ke jaringan Ethereum
                print("\nMencoba terhubung ke jaringan Ethereum...")

                # Dapatkan konfigurasi Ethereum
                ethereum_config = config['networks']['ethereum']

                # Inisialisasi Network
                network = Network('ethereum', ethereum_config, settings)

                # Cek koneksi
                if network.w3.is_connected():
                    print("Berhasil terhubung ke jaringan Ethereum!")

                    # Dapatkan nomor blok terbaru
                    latest_block = network.w3.eth.block_number
                    print(f"Nomor blok terbaru: {latest_block}")

                    # Inisialisasi detektor arbitrase
                    print("\nMenginisialisasi detektor arbitrase...")
                    detector = ArbitrageDetector(network, ethereum_config, settings)
                    print("Detektor arbitrase berhasil diinisialisasi!")

                    # Jalankan deteksi arbitrase
                    print("\nMenjalankan deteksi arbitrase...")
                    print("Ini akan memakan waktu beberapa saat...")

                    # Modifikasi pengaturan untuk menggunakan lebih sedikit token
                    settings['max_tokens_per_network'] = 50  # Batasi jumlah token
                    settings['max_token_pairs'] = 500  # Batasi jumlah pasangan token
                    settings['max_token_triplets'] = 100  # Batasi jumlah triplet token
                    settings['token_pair_batch_size'] = 20  # Ukuran batch lebih kecil
                    settings['token_triplet_batch_size'] = 5  # Ukuran batch lebih kecil

                    # Jalankan dengan timeout
                    try:
                        await asyncio.wait_for(detector.detect_arbitrage_opportunities(), timeout=120)
                        print("Deteksi arbitrase selesai!")

                        # Cek apakah ada peluang arbitrase
                        if detector.arbitrage_opportunities:
                            print(f"\nDitemukan {len(detector.arbitrage_opportunities)} peluang arbitrase!")

                            # Tampilkan peluang arbitrase
                            for i, opportunity in enumerate(detector.arbitrage_opportunities):
                                print(f"\n{i+1}. Jalur: {' -> '.join(opportunity['token_path'])}")
                                print(f"   DEX: {' -> '.join(opportunity['dex_path'])}")
                                print(f"   Profit: ${opportunity['net_profit_usd']:.2f} ({opportunity['profit_percentage']:.2f}%)")
                        else:
                            print("\nTidak ditemukan peluang arbitrase.")
                    except asyncio.TimeoutError:
                        print("Timeout! Deteksi arbitrase berjalan terlalu lama.")
                else:
                    print("Gagal terhubung ke jaringan Ethereum.")
            except Exception as e:
                print(f"Error: {e}")
                traceback.print_exc()
        else:
            print("File config.yaml tidak ditemukan!")
    else:
        print("File main.py tidak ditemukan!")

if __name__ == "__main__":
    # Jalankan dengan asyncio
    asyncio.run(main())
