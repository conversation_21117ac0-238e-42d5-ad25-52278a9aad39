"""
Program utama untuk analisis arbitrase DEX.
"""

import logging
import sys
import time
import argparse
from typing import Dict, List, Any, Optional, Tuple
import traceback
import async<PERSON>

from dexscreener_api import <PERSON>screenerAP<PERSON>
from dexscreener_async_api import <PERSON>screen<PERSON><PERSON>ync<PERSON><PERSON>
from arbitrage_analyzer import ArbitrageAnalyzer
from output_formatter import (
    console, print_header, print_mode_selection, create_progress_bar,
    print_opportunities, print_statistics, print_error, print_warning,
    print_info, print_success
)
from whatsapp_formatter import (
    format_opportunities_for_whatsapp, save_whatsapp_message_to_file
)
from utils import (
    save_opportunities_to_file, load_opportunities_from_file,
    generate_timestamp, calculate_statistics
)
from config import (
    DEFAULT_CAPITAL, TOP_OPPORTUNITIES, TOKEN_QUERIES, USE_ASYNC
)

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("dex_arbitrage.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("main")

def parse_arguments():
    """
    Parse command line arguments.

    Returns:
        Parsed arguments.
    """
    parser = argparse.ArgumentParser(description="Program Analisis Arbitrase DEX")
    parser.add_argument(
        "--capital", type=float, default=DEFAULT_CAPITAL,
        help=f"Modal dalam USD (default: {DEFAULT_CAPITAL})"
    )
    parser.add_argument(
        "--mode", type=int, choices=[1, 2, 3], default=None,
        help="Mode operasi: 1=Same-Chain, 2=Cross-Chain, 3=Keduanya"
    )
    parser.add_argument(
        "--top", type=int, default=TOP_OPPORTUNITIES,
        help=f"Jumlah peluang teratas yang ditampilkan (default: {TOP_OPPORTUNITIES})"
    )
    parser.add_argument(
        "--save", action="store_true",
        help="Simpan hasil analisis ke file"
    )
    parser.add_argument(
        "--load", type=str,
        help="Muat hasil analisis dari file"
    )
    parser.add_argument(
        "--async", dest="use_async", action="store_true",
        help="Gunakan pengambilan data asinkron (lebih cepat)"
    )
    parser.add_argument(
        "--sync", dest="use_async", action="store_false",
        help="Gunakan pengambilan data sinkron (lebih lambat tapi lebih stabil)"
    )
    parser.add_argument(
        "--whatsapp", action="store_true",
        help="Hasilkan pesan format WhatsApp untuk grup trading"
    )
    parser.add_argument(
        "--whatsapp-file", type=str, default="arbitrage_signal.txt",
        help="Nama file untuk menyimpan pesan WhatsApp (default: arbitrage_signal.txt)"
    )
    parser.add_argument(
        "--whatsapp-limit", type=int, default=5,
        help="Jumlah peluang yang ditampilkan dalam pesan WhatsApp (default: 5)"
    )
    parser.set_defaults(use_async=USE_ASYNC)

    return parser.parse_args()

def fetch_data(api: DexscreenerAPI, queries: List[str]) -> List[Dict[str, Any]]:
    """
    Mengambil data dari API Dexscreener.

    Args:
        api: Instance DexscreenerAPI.
        queries: List query pencarian.

    Returns:
        List pasangan token yang ditemukan.
    """
    if USE_ASYNC:
        return asyncio.run(fetch_data_async(queries))
    else:
        return fetch_data_sync(api, queries)

def fetch_data_sync(api: DexscreenerAPI, queries: List[str]) -> List[Dict[str, Any]]:
    """
    Mengambil data dari API Dexscreener secara sinkron.

    Args:
        api: Instance DexscreenerAPI.
        queries: List query pencarian.

    Returns:
        List pasangan token yang ditemukan.
    """
    print_info(f"Mengambil data untuk {len(queries)} query token secara sinkron...")

    progress = create_progress_bar("Mengambil data")
    task_id = progress.add_task("Mengambil data...", total=len(queries))

    progress.start()
    all_pairs = []

    try:
        for i, query in enumerate(queries):
            try:
                pairs = api.search_pairs(query)
                all_pairs.extend(pairs)
                progress.update(task_id, advance=1, description=f"Mengambil data untuk '{query}'")
            except Exception as e:
                logger.error(f"Error saat mengambil data untuk query '{query}': {e}")
                print_warning(f"Gagal mengambil data untuk query '{query}': {e}")
    except KeyboardInterrupt:
        progress.stop()
        print_warning("Pengambilan data dibatalkan oleh pengguna.")
        return all_pairs
    except Exception as e:
        progress.stop()
        logger.error(f"Error saat mengambil data: {e}")
        print_error(f"Error saat mengambil data: {e}")
        return all_pairs

    progress.stop()

    # Menghapus duplikat berdasarkan pairAddress dan chainId
    unique_pairs = {}
    for pair in all_pairs:
        key = f"{pair.get('chainId', '')}-{pair.get('pairAddress', '')}"
        if key not in unique_pairs:
            unique_pairs[key] = pair

    unique_pairs_list = list(unique_pairs.values())
    print_success(f"Berhasil mengambil {len(unique_pairs_list)} pasangan token unik.")

    return unique_pairs_list

async def fetch_data_async(queries: List[str]) -> List[Dict[str, Any]]:
    """
    Mengambil data dari API Dexscreener secara asinkron.

    Args:
        queries: List query pencarian.

    Returns:
        List pasangan token yang ditemukan.
    """
    print_info(f"Mengambil data untuk {len(queries)} query token secara asinkron...")

    progress = create_progress_bar("Mengambil data")
    task_id = progress.add_task("Mengambil data...", total=len(queries))
    progress.start()

    async_api = DexscreenerAsyncAPI()

    try:
        def update_progress(index, total, query, count, error=None):
            progress.update(task_id, advance=1, description=f"Mengambil data untuk '{query}' ({count} pairs)")
            if error:
                print_warning(f"Gagal mengambil data untuk query '{query}': {error}")

        all_pairs = await async_api.fetch_all_pairs_for_queries(queries, update_progress)

    except KeyboardInterrupt:
        progress.stop()
        print_warning("Pengambilan data dibatalkan oleh pengguna.")
        await async_api.close()
        return []
    except Exception as e:
        progress.stop()
        logger.error(f"Error saat mengambil data: {e}")
        print_error(f"Error saat mengambil data: {e}")
        await async_api.close()
        return []
    finally:
        progress.stop()
        await async_api.close()

    print_success(f"Berhasil mengambil {len(all_pairs)} pasangan token unik.")

    return all_pairs

def add_volatility_data(
    api: DexscreenerAPI,
    opportunities: List[Dict[str, Any]],
    limit: int = TOP_OPPORTUNITIES
) -> List[Dict[str, Any]]:
    """
    Menambahkan data volatilitas ke peluang arbitrase.

    Args:
        api: Instance DexscreenerAPI.
        opportunities: List peluang arbitrase.
        limit: Jumlah peluang yang diproses.

    Returns:
        List peluang dengan data volatilitas.
    """
    if not opportunities:
        return opportunities

    if USE_ASYNC:
        return asyncio.run(add_volatility_data_async(opportunities, limit))
    else:
        return add_volatility_data_sync(api, opportunities, limit)

def add_volatility_data_sync(
    api: DexscreenerAPI,
    opportunities: List[Dict[str, Any]],
    limit: int = TOP_OPPORTUNITIES
) -> List[Dict[str, Any]]:
    """
    Menambahkan data volatilitas ke peluang arbitrase secara sinkron.

    Args:
        api: Instance DexscreenerAPI.
        opportunities: List peluang arbitrase.
        limit: Jumlah peluang yang diproses.

    Returns:
        List peluang dengan data volatilitas.
    """
    if not opportunities:
        return opportunities

    # Batasi jumlah peluang yang diproses
    opportunities_to_process = opportunities[:limit]

    print_info(f"Mengambil data volatilitas untuk {len(opportunities_to_process)} peluang teratas secara sinkron...")

    progress = create_progress_bar("Mengambil data volatilitas")
    task_id = progress.add_task("Mengambil data volatilitas...", total=len(opportunities_to_process))

    progress.start()

    try:
        for i, opportunity in enumerate(opportunities_to_process):
            try:
                if opportunity["type"] == "same_chain":
                    chain_id = opportunity["chain_id"]
                    pair_address = opportunity["buy_pair"]["pairAddress"]
                else:  # cross_chain
                    chain_id = opportunity["source_chain"]
                    pair_address = opportunity["buy_pair"]["pairAddress"]

                volatility = api.fetch_pair_volatility(chain_id, pair_address)
                opportunity["volatility"] = volatility

                progress.update(task_id, advance=1, description=f"Mengambil data volatilitas untuk peluang #{i+1}")
            except Exception as e:
                logger.error(f"Error saat mengambil data volatilitas untuk peluang #{i+1}: {e}")
                print_warning(f"Gagal mengambil data volatilitas untuk peluang #{i+1}: {e}")
    except KeyboardInterrupt:
        progress.stop()
        print_warning("Pengambilan data volatilitas dibatalkan oleh pengguna.")
        return opportunities
    except Exception as e:
        progress.stop()
        logger.error(f"Error saat mengambil data volatilitas: {e}")
        print_error(f"Error saat mengambil data volatilitas: {e}")
        return opportunities

    progress.stop()
    print_success(f"Berhasil mengambil data volatilitas untuk {len(opportunities_to_process)} peluang.")

    return opportunities

async def add_volatility_data_async(
    opportunities: List[Dict[str, Any]],
    limit: int = TOP_OPPORTUNITIES
) -> List[Dict[str, Any]]:
    """
    Menambahkan data volatilitas ke peluang arbitrase secara asinkron.

    Args:
        opportunities: List peluang arbitrase.
        limit: Jumlah peluang yang diproses.

    Returns:
        List peluang dengan data volatilitas.
    """
    if not opportunities:
        return opportunities

    # Batasi jumlah peluang yang diproses
    opportunities_to_process = opportunities[:limit]

    print_info(f"Mengambil data volatilitas untuk {len(opportunities_to_process)} peluang teratas secara asinkron...")

    progress = create_progress_bar("Mengambil data volatilitas")
    task_id = progress.add_task("Mengambil data volatilitas...", total=len(opportunities_to_process))
    progress.start()

    async_api = DexscreenerAsyncAPI()

    try:
        def update_progress(index, total, description, success, error=None):
            progress.update(task_id, advance=1, description=f"Mengambil data volatilitas untuk {description}")
            if error:
                print_warning(f"Gagal mengambil data volatilitas untuk {description}: {error}")

        await async_api.fetch_volatility_for_opportunities(opportunities_to_process, limit, update_progress)

    except KeyboardInterrupt:
        progress.stop()
        print_warning("Pengambilan data volatilitas dibatalkan oleh pengguna.")
        await async_api.close()
        return opportunities
    except Exception as e:
        progress.stop()
        logger.error(f"Error saat mengambil data volatilitas: {e}")
        print_error(f"Error saat mengambil data volatilitas: {e}")
        await async_api.close()
        return opportunities
    finally:
        progress.stop()
        await async_api.close()

    print_success(f"Berhasil mengambil data volatilitas untuk {len(opportunities_to_process)} peluang.")

    return opportunities

def analyze_opportunities(
    analyzer: ArbitrageAnalyzer,
    pairs: List[Dict[str, Any]],
    mode: int
) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
    """
    Menganalisis peluang arbitrase.

    Args:
        analyzer: Instance ArbitrageAnalyzer.
        pairs: List pasangan token.
        mode: Mode operasi (1=Same-Chain, 2=Cross-Chain, 3=Keduanya).

    Returns:
        Tuple berisi list peluang same-chain dan cross-chain.
    """
    same_chain_opportunities = []
    cross_chain_opportunities = []

    if mode in [1, 3]:
        print_info("Menganalisis peluang arbitrase sesama jaringan...")
        same_chain_opportunities = analyzer.analyze_same_chain_opportunities(pairs)
        print_success(f"Ditemukan {len(same_chain_opportunities)} peluang arbitrase sesama jaringan.")

    if mode in [2, 3]:
        print_info("Menganalisis peluang arbitrase beda jaringan...")
        cross_chain_opportunities = analyzer.analyze_cross_chain_opportunities(pairs)
        print_success(f"Ditemukan {len(cross_chain_opportunities)} peluang arbitrase beda jaringan.")

    return same_chain_opportunities, cross_chain_opportunities

def main():
    """Fungsi utama program."""
    try:
        # Parse command line arguments
        args = parse_arguments()

        # Tampilkan header
        print_header()

        # Jika mode tidak ditentukan melalui command line, minta input dari pengguna
        mode = args.mode
        if mode is None:
            mode_input = print_mode_selection()
            try:
                mode = int(mode_input)
                if mode not in [1, 2, 3]:
                    print_error("Mode tidak valid. Menggunakan mode default (3).")
                    mode = 3
            except ValueError:
                print_error("Input tidak valid. Menggunakan mode default (3).")
                mode = 3

        # Gunakan parameter use_async dari command line
        global USE_ASYNC
        USE_ASYNC = args.use_async

        # Inisialisasi API dan analyzer
        api = DexscreenerAPI() if not USE_ASYNC else None
        analyzer = ArbitrageAnalyzer(capital=args.capital)

        # Jika diminta untuk memuat dari file
        if args.load:
            try:
                opportunities = load_opportunities_from_file(args.load)
                if not opportunities:
                    print_error(f"Tidak dapat memuat peluang dari file: {args.load}")
                    return

                # Pisahkan peluang berdasarkan tipe
                same_chain_opportunities = [opp for opp in opportunities if opp["type"] == "same_chain"]
                cross_chain_opportunities = [opp for opp in opportunities if opp["type"] == "cross_chain"]

                # Urutkan peluang
                same_chain_opportunities = analyzer.sort_opportunities(same_chain_opportunities)
                cross_chain_opportunities = analyzer.sort_opportunities(cross_chain_opportunities)

                # Gabungkan peluang berdasarkan mode
                if mode == 1:
                    all_opportunities = same_chain_opportunities
                elif mode == 2:
                    all_opportunities = cross_chain_opportunities
                else:  # mode == 3
                    all_opportunities = analyzer.sort_opportunities(same_chain_opportunities + cross_chain_opportunities)

                # Tampilkan peluang
                print_opportunities(all_opportunities, limit=args.top)

                return
            except Exception as e:
                logger.error(f"Error saat memuat dari file: {e}")
                print_error(f"Error saat memuat dari file: {e}")
                return

        # Ambil data dari API
        all_pairs = fetch_data(api, TOKEN_QUERIES)

        if not all_pairs:
            print_error("Tidak ada data yang ditemukan. Program berhenti.")
            return

        # Analisis peluang
        same_chain_opportunities, cross_chain_opportunities = analyze_opportunities(analyzer, all_pairs, mode)

        # Gabungkan peluang berdasarkan mode
        if mode == 1:
            all_opportunities = same_chain_opportunities
        elif mode == 2:
            all_opportunities = cross_chain_opportunities
        else:  # mode == 3
            # Gabungkan peluang tanpa mengurutkan atau menghapus duplikasi dulu
            all_opportunities = same_chain_opportunities + cross_chain_opportunities

        # Tambahkan data volatilitas
        all_opportunities = add_volatility_data(api, all_opportunities, limit=args.top)

        # Urutkan peluang dan hapus duplikasi
        logger.info("Menghapus duplikasi dan mengurutkan peluang...")
        all_opportunities = analyzer.sort_opportunities(all_opportunities)

        # Tampilkan peluang
        print_opportunities(all_opportunities, limit=args.top)

        # Tampilkan statistik
        statistics = calculate_statistics(all_pairs, same_chain_opportunities, cross_chain_opportunities)
        print_statistics(statistics)

        # Simpan hasil ke file jika diminta
        if args.save and all_opportunities:
            filename = f"arbitrage_opportunities_{generate_timestamp()}.json"
            save_opportunities_to_file(all_opportunities, filename)
            print_success(f"Hasil analisis berhasil disimpan ke file: {filename}")

        # Hasilkan pesan WhatsApp jika diminta
        if args.whatsapp and all_opportunities:
            whatsapp_message = format_opportunities_for_whatsapp(all_opportunities, limit=args.whatsapp_limit)
            save_whatsapp_message_to_file(whatsapp_message, args.whatsapp_file)
            print_success(f"Pesan WhatsApp berhasil disimpan ke file: {args.whatsapp_file}")
            print_info("Anda dapat menyalin pesan dari file tersebut dan mengirimkannya ke grup WhatsApp.")

    except KeyboardInterrupt:
        print_warning("Program dibatalkan oleh pengguna.")
    except Exception as e:
        logger.error(f"Error tidak terduga: {e}")
        logger.error(traceback.format_exc())
        print_error(f"Error tidak terduga: {e}")

if __name__ == "__main__":
    main()
