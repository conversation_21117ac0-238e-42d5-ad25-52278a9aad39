#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
KuCoin WebSocket client for cryptocurrency exchange data.
"""

import json
import logging
import time
import aiohttp
import asyncio
from datetime import datetime
import websockets
from .base_client import BaseClient

class KuCoinClient(BaseClient):
    """KuCoin WebSocket client implementation."""

    def __init__(self, websocket_url, logger=None):
        """
        Initialize the KuCoin client.

        Args:
            websocket_url (str): The WebSocket URL for KuCoin
            logger (logging.Logger, optional): Logger instance
        """
        super().__init__('kucoin', websocket_url, logger)
        self.rest_api_url = "https://api.kucoin.com"
        self.token = None
        self.ping_interval = 30  # KuCoin requires ping every 30 seconds
        self.last_ping_time = 0
        self.real_endpoint = None
        self.token_expiry = 0
        self.reconnect_required = False

    async def connect(self):
        """Establish WebSocket connection to the exchange."""
        try:
            # Get WebSocket token and endpoint from REST API
            await self._get_websocket_token()

            if not self.real_endpoint:
                self.logger.error("Failed to get WebSocket endpoint from KuCoin")
                return False

            self.logger.info(f"Connecting to {self.exchange_id} WebSocket at {self.real_endpoint}")
            self.ws = await websockets.connect(self.real_endpoint)
            self.connected = True
            self.reconnect_delay = 1  # Reset reconnect delay on successful connection
            self.last_heartbeat = time.time()
            self.last_ping_time = time.time()
            self.logger.info(f"Connected to {self.exchange_id} WebSocket")

            # Perform any exchange-specific initialization
            await self.on_connect()

            return True
        except Exception as e:
            self.logger.error(f"Failed to connect to {self.exchange_id} WebSocket: {e}")
            self.connected = False
            return False

    async def _get_websocket_token(self):
        """Get WebSocket token and endpoint from KuCoin REST API."""
        current_time = time.time()

        # If token is still valid, reuse it
        if self.token and self.real_endpoint and current_time < self.token_expiry - 300:  # 5 minutes buffer
            return

        try:
            async with aiohttp.ClientSession() as session:
                url = f"{self.rest_api_url}/api/v1/bullet-public"
                async with session.post(url) as response:
                    if response.status != 200:
                        self.logger.error(f"Failed to get WebSocket token from KuCoin: {response.status}")
                        return

                    data = await response.json()

                    if data['code'] != '200000':
                        self.logger.error(f"Failed to get WebSocket token from KuCoin: {data}")
                        return

                    token_data = data['data']
                    self.token = token_data['token']

                    # Set token expiry to 24 hours (KuCoin tokens are valid for 24 hours)
                    # The pingInterval is just for ping/pong, not token expiry
                    self.token_expiry = current_time + 24 * 60 * 60  # 24 hours in seconds

                    # Construct the real endpoint with token
                    endpoint = token_data['instanceServers'][0]['endpoint']
                    self.real_endpoint = f"{endpoint}?token={self.token}&connectId={int(current_time * 1000)}"

                    # Set ping interval
                    self.ping_interval = token_data['instanceServers'][0]['pingInterval'] / 1000

                    self.logger.info(f"Got WebSocket token from KuCoin, expires in 24 hours")
        except Exception as e:
            self.logger.error(f"Error getting WebSocket token from KuCoin: {e}")

    async def _send_heartbeat(self):
        """Send heartbeat to keep the connection alive."""
        if not self.connected:
            return

        current_time = time.time()

        # Check if we need to send a ping
        if current_time - self.last_ping_time >= self.ping_interval:
            ping_message = {
                "id": int(current_time * 1000),
                "type": "ping"
            }

            success = await self.send_message(json.dumps(ping_message))
            if success:
                self.last_ping_time = current_time
                self.logger.debug(f"Sent ping to {self.exchange_id}")
            else:
                self.logger.warning(f"Failed to send ping to {self.exchange_id}")
                self.reconnect_required = True

    async def _process_message(self, message):
        """
        Process incoming WebSocket message from KuCoin.

        Args:
            message (str): The raw message from the WebSocket
        """
        try:
            data = json.loads(message)

            # Check if it's a pong response
            if data.get('type') == 'pong':
                self.logger.debug(f"Received pong from {self.exchange_id}")
                return

            # Check if it's a welcome message
            if data.get('type') == 'welcome':
                self.logger.info(f"Received welcome message from {self.exchange_id}")
                return

            # Check if it's an error message
            if data.get('type') == 'error':
                self.logger.error(f"Error from {self.exchange_id} WebSocket: {data}")
                return

            # Process market data
            if data.get('type') == 'message':
                topic = data.get('topic', '')

                # Process orderbook data
                if '/market/level2' in topic:
                    await self._process_orderbook(data)
                # Process ticker data
                elif '/market/ticker' in topic:
                    await self._process_ticker(data)

        except json.JSONDecodeError:
            self.logger.error(f"Failed to parse message from {self.exchange_id}: {message}")
        except Exception as e:
            self.logger.error(f"Error processing message from {self.exchange_id}: {e}")

    async def _process_orderbook(self, data):
        """
        Process orderbook data from KuCoin.

        Args:
            data (dict): Orderbook data from KuCoin
        """
        try:
            subject = data.get('subject', '')
            if subject != 'trade.l2update':
                return

            # Extract symbol from topic
            topic = data.get('topic', '')
            symbol_raw = topic.split(':')[-1]
            symbol = self._normalize_symbol(symbol_raw)

            # Get data
            orderbook_data = data.get('data', {})

            # Extract timestamp
            timestamp = orderbook_data.get('timestamp', int(time.time() * 1000))

            # Extract bids and asks
            changes = orderbook_data.get('changes', {})
            bids = changes.get('bids', [])
            asks = changes.get('asks', [])

            # Get current orderbook or create new one
            if symbol not in self.market_data:
                self.market_data[symbol] = {}

            if 'orderbook' not in self.market_data[symbol]:
                self.market_data[symbol]['orderbook'] = {
                    'bids': {},
                    'asks': {},
                    'timestamp': timestamp,
                    'datetime': datetime.fromtimestamp(timestamp / 1000).isoformat(),
                    'symbol': symbol,
                    'exchange': self.exchange_id
                }

            orderbook = self.market_data[symbol]['orderbook']

            # Update bids
            for bid in bids:
                price = float(bid[0])
                size = float(bid[1])

                if size == 0:
                    if price in orderbook['bids']:
                        del orderbook['bids'][price]
                else:
                    orderbook['bids'][price] = size

            # Update asks
            for ask in asks:
                price = float(ask[0])
                size = float(ask[1])

                if size == 0:
                    if price in orderbook['asks']:
                        del orderbook['asks'][price]
                else:
                    orderbook['asks'][price] = size

            # Sort bids and asks
            sorted_bids = sorted(orderbook['bids'].items(), key=lambda x: x[0], reverse=True)
            sorted_asks = sorted(orderbook['asks'].items(), key=lambda x: x[0])

            # Get best bid and ask
            if sorted_bids:
                best_bid = sorted_bids[0]
                orderbook['bid'] = best_bid[0]
                orderbook['bid_size'] = best_bid[1]

            if sorted_asks:
                best_ask = sorted_asks[0]
                orderbook['ask'] = best_ask[0]
                orderbook['ask_size'] = best_ask[1]

            # Update timestamp
            orderbook['timestamp'] = timestamp
            orderbook['datetime'] = datetime.fromtimestamp(timestamp / 1000).isoformat()

            # Convert to array format for compatibility
            orderbook['bids'] = [[price, size] for price, size in sorted_bids[:20]]
            orderbook['asks'] = [[price, size] for price, size in sorted_asks[:20]]

        except Exception as e:
            self.logger.error(f"Error processing orderbook data from {self.exchange_id}: {e}")

    async def _process_ticker(self, data):
        """
        Process ticker data from KuCoin.

        Args:
            data (dict): Ticker data from KuCoin
        """
        try:
            subject = data.get('subject', '')
            if subject != 'trade.ticker':
                return

            # Extract symbol from topic
            topic = data.get('topic', '')
            symbol_raw = topic.split(':')[-1]
            symbol = self._normalize_symbol(symbol_raw)

            # Get data
            ticker_data = data.get('data', {})

            # Extract timestamp
            timestamp = ticker_data.get('time', int(time.time() * 1000))

            # Create ticker object
            ticker = {
                'symbol': symbol,
                'bid': float(ticker_data.get('bestBid', 0)),
                'ask': float(ticker_data.get('bestAsk', 0)),
                'bid_size': float(ticker_data.get('bestBidSize', 0)),
                'ask_size': float(ticker_data.get('bestAskSize', 0)),
                'last': float(ticker_data.get('price', 0)),
                'volume': float(ticker_data.get('vol', 0)),
                'timestamp': timestamp,
                'datetime': datetime.fromtimestamp(timestamp / 1000).isoformat(),
                'high': float(ticker_data.get('high', 0)),
                'low': float(ticker_data.get('low', 0)),
                'change': float(ticker_data.get('changePrice', 0)),
                'percentage': float(ticker_data.get('changeRate', 0)) * 100,
                'exchange': self.exchange_id
            }

            # Update market data
            if symbol not in self.market_data:
                self.market_data[symbol] = {}

            self.market_data[symbol]['ticker'] = ticker

        except Exception as e:
            self.logger.error(f"Error processing ticker data from {self.exchange_id}: {e}")

    async def _subscribe_orderbook(self, symbol):
        """
        Subscribe to orderbook updates for a symbol.

        Args:
            symbol (str): The trading pair symbol (e.g., 'BTC/USDT')

        Returns:
            bool: True if subscription was successful, False otherwise
        """
        if not self.connected:
            self.logger.warning(f"Cannot subscribe to {symbol} orderbook on {self.exchange_id}: Not connected")
            return False

        try:
            # Convert symbol to KuCoin format
            kucoin_symbol = self._format_symbol(symbol)

            # Create subscription message
            subscription = {
                "id": int(time.time() * 1000),
                "type": "subscribe",
                "topic": f"/market/level2:{kucoin_symbol}",
                "privateChannel": False,
                "response": True
            }

            # Send subscription request
            success = await self.send_message(json.dumps(subscription))
            if success:
                self.logger.info(f"Subscribed to {symbol} orderbook on {self.exchange_id}")
                self.subscribed_pairs.add(symbol)
                return True
            else:
                self.logger.error(f"Failed to subscribe to {symbol} orderbook on {self.exchange_id}")
                return False

        except Exception as e:
            self.logger.error(f"Error subscribing to {symbol} orderbook on {self.exchange_id}: {e}")
            return False

    def _normalize_symbol(self, symbol):
        """
        Convert exchange-specific symbol to standard format.

        Args:
            symbol (str): Exchange-specific symbol (e.g., 'BTC-USDT')

        Returns:
            str: Normalized symbol (e.g., 'BTC/USDT')
        """
        # KuCoin uses '-' as separator
        parts = symbol.split('-')
        if len(parts) == 2:
            return f"{parts[0]}/{parts[1]}"
        return symbol

    def _format_symbol(self, symbol):
        """
        Convert standard symbol to exchange-specific format.

        Args:
            symbol (str): Standard symbol (e.g., 'BTC/USDT')

        Returns:
            str: Exchange-specific symbol (e.g., 'BTC-USDT')
        """
        # KuCoin uses '-' as separator
        parts = symbol.split('/')
        if len(parts) == 2:
            return f"{parts[0]}-{parts[1]}"
        return symbol

    async def get_all_symbols(self):
        """
        Get all available trading pairs from the exchange.

        Returns:
            list: List of trading pair symbols (e.g., ['BTC/USDT', 'ETH/USDT'])
        """
        try:
            async with aiohttp.ClientSession() as session:
                url = f"{self.rest_api_url}/api/v1/symbols"
                async with session.get(url) as response:
                    if response.status != 200:
                        self.logger.error(f"Failed to get symbols from KuCoin: {response.status}")
                        return []

                    data = await response.json()

                    if data['code'] != '200000':
                        self.logger.error(f"Failed to get symbols from KuCoin: {data}")
                        return []

                    symbols = []
                    for symbol_data in data['data']:
                        if symbol_data['enableTrading']:
                            base = symbol_data['baseCurrency']
                            quote = symbol_data['quoteCurrency']
                            symbols.append(f"{base}/{quote}")

                    self.logger.info(f"Retrieved {len(symbols)} symbols from KuCoin")
                    return symbols
        except Exception as e:
            self.logger.error(f"Error getting symbols from KuCoin: {e}")
            return []

    async def on_connect(self):
        """
        Perform initialization after WebSocket connection is established.
        """
        self.logger.info(f"Connected to {self.exchange_id} WebSocket")

        # KuCoin doesn't require any initialization after connection
        # The connection is already authenticated during the token acquisition

        return True

    async def _subscribe_ticker(self, symbol):
        """
        Subscribe to ticker updates for a symbol.

        Args:
            symbol (str): The trading pair symbol (e.g., 'BTC/USDT')

        Returns:
            bool: True if subscription was successful, False otherwise
        """
        if not self.connected:
            self.logger.warning(f"Cannot subscribe to {symbol} ticker on {self.exchange_id}: Not connected")
            return False

        try:
            # Convert symbol to KuCoin format
            kucoin_symbol = self._format_symbol(symbol)

            # Create subscription message
            subscription = {
                "id": int(time.time() * 1000),
                "type": "subscribe",
                "topic": f"/market/ticker:{kucoin_symbol}",
                "privateChannel": False,
                "response": True
            }

            # Send subscription request
            success = await self.send_message(json.dumps(subscription))
            if success:
                self.logger.info(f"Subscribed to {symbol} ticker on {self.exchange_id}")
                return True
            else:
                self.logger.error(f"Failed to subscribe to {symbol} ticker on {self.exchange_id}")
                return False

        except Exception as e:
            self.logger.error(f"Error subscribing to {symbol} ticker on {self.exchange_id}: {e}")
            return False

    async def heartbeat(self):
        """Send heartbeat to keep the connection alive."""
        if not self.connected:
            return

        # Send ping if needed
        await self._send_heartbeat()

        # Check if reconnect is required
        if self.reconnect_required:
            self.logger.info(f"Reconnect required for {self.exchange_id}")
            self.connected = False
            await self.disconnect()
            self.reconnect_required = False
