"""
Modul untuk optimasi kinerja program.
"""
import asyncio
from typing import List, Dict, Any, Set, Tuple
import logging
import time
from web3 import Web3

logger = logging.getLogger("arbitrage_detector")

class BatchRequestManager:
    """
    Kelas untuk mengelola permintaan batch ke RPC.
    """
    
    def __init__(self, max_batch_size: int = 5, cooldown_seconds: float = 0.5):
        """
        Inisialisasi BatchRequestManager.
        
        Args:
            max_batch_size: Ukuran maksimum batch
            cooldown_seconds: W<PERSON>tu jeda antara batch dalam detik
        """
        self.max_batch_size = max_batch_size
        self.cooldown_seconds = cooldown_seconds
        self.current_batch = []
        self.results = {}
        self.semaphore = asyncio.Semaphore(max_batch_size)
    
    async def add_task(self, key: str, coro):
        """
        Menambahkan tugas ke batch.
        
        Args:
            key: Ku<PERSON><PERSON> untuk mengidentifikasi tugas
            coro: Coroutine yang akan dijalankan
        """
        async with self.semaphore:
            try:
                start_time = time.time()
                result = await coro
                duration = time.time() - start_time
                
                # Log jika permintaan terlalu lama
                if duration > 2.0:
                    logger.debug(f"Permintaan {key} membutuhkan waktu {duration:.2f} detik")
                
                self.results[key] = result
                return result
            except Exception as e:
                logger.warning(f"Error saat menjalankan tugas {key}: {e}")
                self.results[key] = None
                raise
    
    async def execute_batch(self, tasks: List[Tuple[str, Any]]):
        """
        Menjalankan batch tugas.
        
        Args:
            tasks: Daftar tugas (key, coroutine)
            
        Returns:
            Dictionary hasil
        """
        self.results = {}
        
        # Bagi tugas menjadi batch-batch kecil
        batches = [tasks[i:i + self.max_batch_size] for i in range(0, len(tasks), self.max_batch_size)]
        
        for batch_idx, batch in enumerate(batches):
            # Jalankan batch secara paralel
            batch_tasks = [self.add_task(key, coro) for key, coro in batch]
            await asyncio.gather(*batch_tasks, return_exceptions=True)
            
            # Jeda antara batch untuk menghindari rate limiting
            if batch_idx < len(batches) - 1:  # Jangan jeda setelah batch terakhir
                await asyncio.sleep(self.cooldown_seconds)
        
        return self.results

class TokenPairCache:
    """
    Kelas untuk menyimpan cache pasangan token.
    """
    
    def __init__(self, ttl_seconds: int = 300):
        """
        Inisialisasi TokenPairCache.
        
        Args:
            ttl_seconds: Time-to-live dalam detik
        """
        self.cache = {}
        self.ttl_seconds = ttl_seconds
        self.last_access = {}
    
    def get(self, network: str, dex: str, token_a: str, token_b: str):
        """
        Mendapatkan data dari cache.
        
        Args:
            network: Nama jaringan
            dex: Nama DEX
            token_a: Alamat token A
            token_b: Alamat token B
            
        Returns:
            Data dari cache atau None jika tidak ada
        """
        key = f"{network}:{dex}:{token_a}:{token_b}"
        
        if key in self.cache:
            # Cek apakah cache masih valid
            if time.time() - self.last_access[key] < self.ttl_seconds:
                # Perbarui waktu akses terakhir
                self.last_access[key] = time.time()
                return self.cache[key]
            else:
                # Cache sudah kedaluwarsa
                del self.cache[key]
                del self.last_access[key]
        
        return None
    
    def set(self, network: str, dex: str, token_a: str, token_b: str, data):
        """
        Menyimpan data ke cache.
        
        Args:
            network: Nama jaringan
            dex: Nama DEX
            token_a: Alamat token A
            token_b: Alamat token B
            data: Data yang akan disimpan
        """
        key = f"{network}:{dex}:{token_a}:{token_b}"
        self.cache[key] = data
        self.last_access[key] = time.time()
    
    def invalidate(self, network: str = None, dex: str = None, token_a: str = None, token_b: str = None):
        """
        Menginvalidasi cache.
        
        Args:
            network: Nama jaringan (opsional)
            dex: Nama DEX (opsional)
            token_a: Alamat token A (opsional)
            token_b: Alamat token B (opsional)
        """
        keys_to_delete = []
        
        for key in self.cache.keys():
            parts = key.split(":")
            
            if network and parts[0] != network:
                continue
            
            if dex and parts[1] != dex:
                continue
            
            if token_a and parts[2] != token_a:
                continue
            
            if token_b and parts[3] != token_b:
                continue
            
            keys_to_delete.append(key)
        
        for key in keys_to_delete:
            del self.cache[key]
            del self.last_access[key]

class TokenFilter:
    """
    Kelas untuk memfilter token yang valid.
    """
    
    def __init__(self):
        """
        Inisialisasi TokenFilter.
        """
        self.stablecoin_symbols = {'USDT', 'USDC', 'DAI', 'BUSD', 'TUSD', 'USDP', 'GUSD', 'FRAX', 'LUSD', 'SUSD', 'USDD', 'USDN'}
        self.blacklisted_tokens = set()
        self.valid_tokens = set()
    
    def is_stablecoin(self, symbol: str) -> bool:
        """
        Memeriksa apakah token adalah stablecoin.
        
        Args:
            symbol: Simbol token
            
        Returns:
            True jika token adalah stablecoin, False jika tidak
        """
        return symbol.upper() in self.stablecoin_symbols
    
    def is_valid_token(self, address: str, symbol: str, decimals: int) -> bool:
        """
        Memeriksa apakah token valid.
        
        Args:
            address: Alamat token
            symbol: Simbol token
            decimals: Jumlah desimal token
            
        Returns:
            True jika token valid, False jika tidak
        """
        # Cek apakah token ada dalam blacklist
        if address in self.blacklisted_tokens:
            return False
        
        # Cek apakah token sudah divalidasi sebelumnya
        if address in self.valid_tokens:
            return True
        
        # Cek apakah token memiliki simbol yang valid
        if not symbol or len(symbol) == 0:
            self.blacklisted_tokens.add(address)
            return False
        
        # Cek apakah token memiliki desimal yang valid
        if decimals <= 0 or decimals > 18:
            self.blacklisted_tokens.add(address)
            return False
        
        # Token valid
        self.valid_tokens.add(address)
        return True
    
    def blacklist_token(self, address: str):
        """
        Menambahkan token ke blacklist.
        
        Args:
            address: Alamat token
        """
        self.blacklisted_tokens.add(address)
        if address in self.valid_tokens:
            self.valid_tokens.remove(address)

# Inisialisasi objek global
batch_manager = BatchRequestManager()
token_pair_cache = TokenPairCache()
token_filter = TokenFilter()
