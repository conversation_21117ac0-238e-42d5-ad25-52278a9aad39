"""
Modul untuk mengelola koneksi jaringan blockchain.
"""
import asyncio
import logging
import time
import random
import socket
from typing import Dict, Any, List, Optional, Union, Tuple
from web3 import Web3, AsyncHTTPProvider
from web3.types import Wei
from requests.exceptions import ConnectionError, Timeout, ReadTimeout, RequestException

from .utils import exponential_backoff

logger = logging.getLogger("arbitrage_detector")

class Network:
    """
    Kelas untuk mengelola koneksi ke jaringan blockchain.
    """

    def __init__(self, name: str, config: Dict[str, Any], settings: Dict[str, Any] = None):
        """
        Inisialisasi objek Network.

        Args:
            name: Nama jaringan
            config: Konfigurasi jaringan
            settings: Pengaturan global (opsional)
        """
        self.name = name
        self.config = config
        self.settings = settings or {}

        # Simpan URL RPC utama dan alternatif
        self.rpc_url = config['rpc_url']
        self.rpc_alternatives = config.get('rpc_alternatives', [])
        self.current_rpc_index = 0  # Indeks RPC yang sedang digunakan

        self.chain_id = config['chain_id']
        self.gas_price_strategy = config['gas_price_strategy']
        self.min_profit_usd = config['min_profit_usd']

        # Simpan daftar DEX dan token
        self.dexs = config['dexs']
        self.tokens = config['tokens']

        # Simpan cache untuk kontrak
        self.contract_cache = {}

        # Simpan daftar token dan DEX yang bermasalah
        self.problematic_tokens = set()
        self.problematic_dexs = set()

        # Inisialisasi Web3 dengan RPC utama
        self._initialize_web3()

    def _initialize_web3(self):
        """
        Inisialisasi Web3 dengan RPC saat ini.
        """
        # Dapatkan URL RPC saat ini
        if self.current_rpc_index == 0:
            current_rpc = self.rpc_url
        else:
            current_rpc = self.rpc_alternatives[self.current_rpc_index - 1]

        # Inisialisasi Web3 dengan timeout dan penanganan error yang lebih baik
        timeout = self.settings.get('rpc_timeout_seconds', 10)

        # Tambahkan header User-Agent untuk menghindari pemblokiran
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'application/json',
            'Content-Type': 'application/json'
        }

        # Inisialisasi Web3 dengan opsi yang lebih baik
        self.w3 = Web3(Web3.HTTPProvider(
            current_rpc,
            request_kwargs={
                'timeout': timeout,
                'headers': headers
            }
        ))

        # Cek koneksi dengan penanganan error yang lebih baik
        try:
            if not self.w3.is_connected():
                logger.warning(f"Tidak dapat terhubung ke jaringan {self.name} dengan RPC {current_rpc}")
                # Coba RPC alternatif
                if not self._try_alternative_rpc():
                    raise ConnectionError(f"Tidak dapat terhubung ke jaringan {self.name} dengan semua RPC yang tersedia")
            else:
                logger.info(f"Berhasil terhubung ke jaringan {self.name} dengan RPC {current_rpc}")
        except (ConnectionError, Timeout, ReadTimeout, RequestException, socket.error) as e:
            logger.warning(f"Error koneksi saat menginisialisasi Web3 untuk {self.name} dengan RPC {current_rpc}: {e}")
            # Coba RPC alternatif
            if not self._try_alternative_rpc():
                raise ConnectionError(f"Tidak dapat terhubung ke jaringan {self.name} dengan semua RPC yang tersedia")
        except Exception as e:
            logger.warning(f"Error tidak terduga saat menginisialisasi Web3 untuk {self.name} dengan RPC {current_rpc}: {e}")
            # Coba RPC alternatif
            if not self._try_alternative_rpc():
                raise ConnectionError(f"Tidak dapat terhubung ke jaringan {self.name} dengan semua RPC yang tersedia")

    def _try_alternative_rpc(self) -> bool:
        """
        Mencoba RPC alternatif jika RPC saat ini gagal.

        Returns:
            True jika berhasil terhubung dengan RPC alternatif, False jika semua RPC gagal
        """
        # Hitung total RPC yang tersedia (utama + alternatif)
        total_rpc = 1 + len(self.rpc_alternatives)

        # Jika tidak ada RPC alternatif, coba lagi dengan RPC yang sama setelah jeda
        if total_rpc == 1:
            logger.warning(f"Tidak ada RPC alternatif untuk {self.name}. Mencoba lagi dengan RPC yang sama setelah jeda.")
            time.sleep(self.settings.get('connection_error_pause_seconds', 15))

            # Inisialisasi Web3 dengan RPC yang sama
            timeout = self.settings.get('rpc_timeout_seconds', 10)
            self.w3 = Web3(Web3.HTTPProvider(self.rpc_url, request_kwargs={'timeout': timeout}))

            # Cek koneksi
            if self.w3.is_connected():
                logger.info(f"Berhasil terhubung kembali ke jaringan {self.name} dengan RPC: {self.rpc_url}")
                return True

            logger.error(f"Tidak dapat terhubung kembali ke jaringan {self.name} dengan RPC: {self.rpc_url}")
            return False

        # Acak urutan RPC alternatif untuk menghindari overload pada satu RPC
        rpc_indices = list(range(1, total_rpc))
        random.shuffle(rpc_indices)

        # Coba semua RPC alternatif
        for idx in rpc_indices:
            # Dapatkan URL RPC alternatif
            if idx == 0:
                alternative_rpc = self.rpc_url
            else:
                alternative_rpc = self.rpc_alternatives[idx - 1]

            logger.info(f"Mencoba RPC alternatif untuk {self.name}: {alternative_rpc}")

            try:
                # Inisialisasi Web3 dengan RPC alternatif
                timeout = self.settings.get('rpc_timeout_seconds', 10)
                self.w3 = Web3(Web3.HTTPProvider(alternative_rpc, request_kwargs={'timeout': timeout}))

                # Cek koneksi
                if self.w3.is_connected():
                    logger.info(f"Berhasil terhubung ke jaringan {self.name} dengan RPC alternatif: {alternative_rpc}")
                    self.current_rpc_index = idx
                    return True
            except (ConnectionError, Timeout, ReadTimeout, RequestException, socket.error) as e:
                logger.warning(f"Error koneksi saat mencoba RPC alternatif {alternative_rpc} untuk {self.name}: {e}")
            except Exception as e:
                logger.warning(f"Error tidak terduga saat mencoba RPC alternatif {alternative_rpc} untuk {self.name}: {e}")

            # Jeda sebentar sebelum mencoba RPC berikutnya
            time.sleep(1)

        # Semua RPC gagal, coba lagi dengan RPC utama setelah jeda lebih lama
        logger.warning(f"Semua RPC alternatif gagal untuk {self.name}. Mencoba lagi dengan RPC utama setelah jeda.")
        time.sleep(self.settings.get('connection_error_pause_seconds', 15))

        # Reset ke RPC utama
        self.current_rpc_index = 0
        timeout = self.settings.get('rpc_timeout_seconds', 10)
        self.w3 = Web3(Web3.HTTPProvider(self.rpc_url, request_kwargs={'timeout': timeout}))

        # Cek koneksi
        if self.w3.is_connected():
            logger.info(f"Berhasil terhubung kembali ke jaringan {self.name} dengan RPC utama: {self.rpc_url}")
            return True

        logger.error(f"Tidak dapat terhubung ke jaringan {self.name} dengan semua RPC yang tersedia")
        return False

    async def get_gas_price(self) -> Wei:
        """
        Mendapatkan harga gas berdasarkan strategi yang dikonfigurasi.

        Returns:
            Harga gas dalam Wei
        """
        # Jumlah percobaan maksimum
        max_attempts = self.settings.get('retry_attempts', 5)

        for attempt in range(max_attempts):
            try:
                # Dapatkan harga gas dasar
                base_gas_price = self.w3.eth.gas_price

                # Sesuaikan berdasarkan strategi
                if self.gas_price_strategy == 'fast':
                    return int(base_gas_price * 1.2)  # 20% lebih tinggi
                elif self.gas_price_strategy == 'slow':
                    return int(base_gas_price * 0.8)  # 20% lebih rendah
                else:  # 'medium' atau default
                    return base_gas_price
            except (ConnectionError, Timeout, ReadTimeout, RequestException, socket.error) as e:
                # Error koneksi, coba ganti RPC jika ini adalah percobaan terakhir
                if attempt == max_attempts - 1:
                    logger.warning(f"Error koneksi saat mendapatkan harga gas di {self.name}: {e}")
                    # Coba ganti RPC
                    if self._try_alternative_rpc():
                        # Jika berhasil ganti RPC, coba lagi
                        try:
                            base_gas_price = self.w3.eth.gas_price

                            # Sesuaikan berdasarkan strategi
                            if self.gas_price_strategy == 'fast':
                                return int(base_gas_price * 1.2)  # 20% lebih tinggi
                            elif self.gas_price_strategy == 'slow':
                                return int(base_gas_price * 0.8)  # 20% lebih rendah
                            else:  # 'medium' atau default
                                return base_gas_price
                        except Exception as inner_e:
                            logger.error(f"Error saat mendapatkan harga gas di {self.name} setelah ganti RPC: {inner_e}")
                            # Gunakan nilai default jika gagal
                            return Web3.to_wei(5, 'gwei')
                    else:
                        # Jika semua RPC gagal, gunakan nilai default
                        logger.error(f"Semua RPC gagal saat mendapatkan harga gas di {self.name}")
                        return Web3.to_wei(5, 'gwei')

                # Tunggu sebelum mencoba lagi
                delay = exponential_backoff(
                    attempt,
                    base_delay=self.settings.get('retry_delay_seconds', 2),
                    max_delay=self.settings.get('max_retry_delay_seconds', 30)
                )
                logger.warning(f"Error koneksi saat mendapatkan harga gas di {self.name}. Mencoba lagi dalam {delay:.2f} detik.")
                await asyncio.sleep(delay)
            except Exception as e:
                # Error lain, coba lagi jika bukan percobaan terakhir
                if attempt == max_attempts - 1:
                    logger.error(f"Error saat mendapatkan harga gas di {self.name}: {e}")
                    # Gunakan nilai default jika gagal
                    return Web3.to_wei(5, 'gwei')

                # Tunggu sebelum mencoba lagi
                delay = exponential_backoff(
                    attempt,
                    base_delay=self.settings.get('retry_delay_seconds', 2),
                    max_delay=self.settings.get('max_retry_delay_seconds', 30)
                )
                logger.warning(f"Error saat mendapatkan harga gas di {self.name}. Mencoba lagi dalam {delay:.2f} detik.")
                await asyncio.sleep(delay)

        # Jika semua percobaan gagal, gunakan nilai default
        return Web3.to_wei(5, 'gwei')

    async def get_token_contract(self, token_address: str) -> Any:
        """
        Mendapatkan kontrak token ERC20.

        Args:
            token_address: Alamat kontrak token

        Returns:
            Objek kontrak Web3
        """
        # Cek cache
        if token_address in self.contract_cache:
            return self.contract_cache[token_address]

        # Muat ABI
        from .utils import load_abi
        abi = load_abi("abis/ERC20.json")

        # Buat kontrak
        contract = self.w3.eth.contract(address=Web3.to_checksum_address(token_address), abi=abi)

        # Simpan ke cache
        self.contract_cache[token_address] = contract

        return contract

    async def get_token_info(self, token_address: str) -> Tuple[str, str, int]:
        """
        Mendapatkan informasi token (simbol, nama, desimal).

        Args:
            token_address: Alamat kontrak token

        Returns:
            Tuple berisi (simbol, nama, desimal)
        """
        # Cek apakah token ada dalam daftar token bermasalah
        if token_address in self.problematic_tokens and self.settings.get('skip_problematic_tokens', True):
            logger.warning(f"Melewati token bermasalah {token_address} di {self.name}")
            raise ValueError(f"Token {token_address} dilewati karena bermasalah")

        try:
            contract = await self.get_token_contract(token_address)

            # Dapatkan jumlah percobaan dari pengaturan
            retry_attempts = self.settings.get('retry_attempts', 5)

            # Gunakan retry dengan exponential backoff
            for attempt in range(retry_attempts):
                try:
                    # Coba dapatkan informasi token
                    symbol = contract.functions.symbol().call()
                    name = contract.functions.name().call()
                    decimals = contract.functions.decimals().call()
                    return symbol, name, decimals
                except (ConnectionError, Timeout, ReadTimeout, RequestException, socket.error) as e:
                    # Error koneksi, coba ganti RPC
                    if attempt == retry_attempts - 1:
                        # Coba ganti RPC pada percobaan terakhir
                        if self._try_alternative_rpc():
                            # Jika berhasil ganti RPC, coba lagi dari awal
                            contract = await self.get_token_contract(token_address)
                            try:
                                symbol = contract.functions.symbol().call()
                                name = contract.functions.name().call()
                                decimals = contract.functions.decimals().call()
                                return symbol, name, decimals
                            except Exception as inner_e:
                                # Jika masih gagal, tandai token sebagai bermasalah
                                self.problematic_tokens.add(token_address)
                                logger.error(f"Gagal mendapatkan info token {token_address} di {self.name} setelah ganti RPC: {inner_e}")
                                raise
                        else:
                            # Jika semua RPC gagal, tandai token sebagai bermasalah
                            self.problematic_tokens.add(token_address)
                            logger.error(f"Gagal mendapatkan info token {token_address} di {self.name} karena error koneksi: {e}")
                            raise

                    # Tunggu sebelum mencoba lagi
                    delay = exponential_backoff(
                        attempt,
                        base_delay=self.settings.get('retry_delay_seconds', 2),
                        max_delay=self.settings.get('max_retry_delay_seconds', 30)
                    )
                    logger.warning(f"Error koneksi saat mendapatkan info token {token_address}. Mencoba lagi dalam {delay:.2f} detik.")
                    await asyncio.sleep(delay)
                except Exception as e:
                    # Cek apakah ini adalah error rate limit
                    if "Too Many Requests" in str(e) or "429" in str(e):
                        # Jika ini adalah percobaan terakhir, coba ganti RPC
                        if attempt == retry_attempts - 1:
                            # Coba ganti RPC
                            if self._try_alternative_rpc():
                                # Jika berhasil ganti RPC, coba lagi dari awal
                                return await self.get_token_info(token_address)
                            else:
                                # Jika semua RPC gagal, tandai token sebagai bermasalah
                                self.problematic_tokens.add(token_address)
                                logger.error(f"Gagal mendapatkan info token {token_address} di {self.name} karena rate limit: {e}")
                                raise

                        # Tunggu lebih lama untuk rate limit
                        rate_limit_pause = self.settings.get('rate_limit_pause_seconds', 5)
                        logger.warning(f"Terkena rate limit untuk token {token_address}. Menunggu {rate_limit_pause} detik.")
                        await asyncio.sleep(rate_limit_pause)
                    # Cek apakah ini adalah error kontrak (execution reverted)
                    elif "execution reverted" in str(e) or "revert" in str(e):
                        # Tandai token sebagai bermasalah
                        self.problematic_tokens.add(token_address)
                        logger.warning(f"Token {token_address} di {self.name} memiliki masalah kontrak: {e}")
                        raise ValueError(f"Token {token_address} memiliki masalah kontrak")
                    elif attempt == retry_attempts - 1:  # Percobaan terakhir
                        # Tandai token sebagai bermasalah
                        self.problematic_tokens.add(token_address)
                        logger.error(f"Gagal mendapatkan info token {token_address} di {self.name}: {e}")
                        raise
                    else:
                        # Tunggu sebelum mencoba lagi
                        delay = exponential_backoff(
                            attempt,
                            base_delay=self.settings.get('retry_delay_seconds', 2),
                            max_delay=self.settings.get('max_retry_delay_seconds', 30)
                        )
                        logger.warning(f"Percobaan {attempt+1} gagal untuk token {token_address}. Mencoba lagi dalam {delay:.2f} detik.")
                        await asyncio.sleep(delay)
        except Exception as e:
            # Tangani error yang tidak tertangkap
            self.problematic_tokens.add(token_address)
            logger.error(f"Error tidak terduga saat mendapatkan info token {token_address} di {self.name}: {e}")
            raise

    async def get_token_balance(self, token_address: str, wallet_address: str) -> int:
        """
        Mendapatkan saldo token untuk alamat dompet tertentu.

        Args:
            token_address: Alamat kontrak token
            wallet_address: Alamat dompet

        Returns:
            Saldo token dalam satuan terkecil
        """
        contract = await self.get_token_contract(token_address)

        # Gunakan retry dengan exponential backoff
        for attempt in range(3):
            try:
                balance = contract.functions.balanceOf(Web3.to_checksum_address(wallet_address)).call()
                return balance
            except Exception as e:
                if attempt == 2:  # Percobaan terakhir
                    logger.error(f"Gagal mendapatkan saldo token {token_address} untuk {wallet_address} di {self.name}: {e}")
                    raise

                # Tunggu sebelum mencoba lagi
                delay = exponential_backoff(attempt)
                logger.warning(f"Percobaan {attempt+1} gagal. Mencoba lagi dalam {delay:.2f} detik.")
                await asyncio.sleep(delay)

    async def estimate_gas_cost(self, from_address: str, to_address: str, data: str) -> Tuple[int, int]:
        """
        Memperkirakan biaya gas untuk transaksi.

        Args:
            from_address: Alamat pengirim
            to_address: Alamat penerima
            data: Data transaksi

        Returns:
            Tuple berisi (gas_used, gas_price)
        """
        try:
            # Perkirakan gas yang digunakan
            gas_estimate = self.w3.eth.estimate_gas({
                'from': Web3.to_checksum_address(from_address),
                'to': Web3.to_checksum_address(to_address),
                'data': data
            })

            # Dapatkan harga gas
            gas_price = await self.get_gas_price()

            return gas_estimate, gas_price
        except Exception as e:
            logger.error(f"Error saat memperkirakan biaya gas di {self.name}: {e}")
            # Gunakan nilai default jika gagal
            return 200000, Web3.to_wei(5, 'gwei')

    async def get_native_token_price_usd(self) -> float:
        """
        Mendapatkan harga token native (ETH, MATIC, BNB, dll.) dalam USD.

        Catatan: Dalam implementasi nyata, ini akan menggunakan oracle harga atau API.
        Untuk kesederhanaan, kita gunakan nilai hardcoded.

        Returns:
            Harga token native dalam USD
        """
        # Nilai hardcoded untuk demo
        # Dalam implementasi nyata, gunakan oracle harga atau API
        prices = {
            1: 3500.0,      # ETH
            137: 0.55,      # MATIC
            56: 220.0,      # BNB
            43114: 10.0,    # AVAX
            250: 0.2,       # FTM
            42161: 3500.0,  # ETH (Arbitrum)
            10: 3500.0      # ETH (Optimism)
        }

        return prices.get(self.chain_id, 1000.0)  # Default jika tidak ditemukan

    def get_dex_contract(self, dex_name: str) -> Any:
        """
        Mendapatkan kontrak DEX.

        Args:
            dex_name: Nama DEX

        Returns:
            Objek kontrak Web3
        """
        # Cek apakah DEX ada dalam konfigurasi
        if dex_name not in self.dexs:
            raise ValueError(f"DEX {dex_name} tidak ditemukan dalam konfigurasi jaringan {self.name}")

        dex_config = self.dexs[dex_name]

        # Cek cache
        cache_key = f"{dex_name}_router"
        if cache_key in self.contract_cache:
            return self.contract_cache[cache_key]

        # Muat ABI
        from .utils import load_abi

        # Cek apakah ini Uniswap V3
        if 'quoter_address' in dex_config:
            # Ini adalah Uniswap V3
            abi = load_abi(dex_config['abi_quoter_path'])
            address = dex_config['quoter_address']
        else:
            # Ini adalah DEX berbasis Uniswap V2
            abi = load_abi(dex_config['abi_path'])
            address = dex_config['router_address']

        # Buat kontrak
        contract = self.w3.eth.contract(address=Web3.to_checksum_address(address), abi=abi)

        # Simpan ke cache
        self.contract_cache[cache_key] = contract

        return contract
