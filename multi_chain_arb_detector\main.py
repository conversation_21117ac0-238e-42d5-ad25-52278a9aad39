"""
Program utama untuk deteksi arbitrase multi-chain.
"""
import os
import sys
import asyncio
import signal
import logging
import time
from typing import Dict, Any, List, Optional, Set
from pathlib import Path
from datetime import datetime

from src.network import Network
from src.arbitrage import ArbitrageDetector
from src.utils import load_config, setup_logging, console, display_arbitrage_opportunity

# Variabel global untuk menangani sinyal
should_exit = False

def signal_handler(sig, frame):
    """
    Menangani sinyal interupsi (Ctrl+C).
    """
    global should_exit
    console.print("\n[bold yellow]Menerima sinyal interupsi. Menghentikan program dengan aman...[/bold yellow]")
    should_exit = True

async def check_network(network_name: str, network_config: Dict[str, Any], settings: Dict[str, Any], logger: logging.Logger) -> None:
    """
    Memeriksa peluang arbitrase di satu jaringan.

    Args:
        network_name: Nama jaringan
        network_config: Konfigurasi jaringan
        settings: Pengaturan global
        logger: Logger
    """
    try:
        # Inisialisasi objek Network dengan pengaturan
        network = Network(network_name, network_config, settings)

        # Inisialisasi detektor arbitrase
        detector = ArbitrageDetector(network, network_config, settings)

        # Deteksi peluang arbitrase
        await detector.detect_arbitrage_opportunities()
    except Exception as e:
        logger.error(f"Error saat memeriksa jaringan {network_name}: {e}")
        # Tampilkan traceback untuk debugging
        import traceback
        logger.debug(f"Traceback: {traceback.format_exc()}")

async def main():
    """
    Fungsi utama program.
    """
    # Tangani sinyal interupsi
    signal.signal(signal.SIGINT, signal_handler)

    # Muat konfigurasi
    # Gunakan path relatif terhadap file main.py
    script_dir = os.path.dirname(os.path.abspath(__file__))
    config_path = os.path.join(script_dir, "config.yaml")
    config = load_config(config_path)

    # Dapatkan pengaturan
    settings = config.get('settings', {})

    # Setup logging
    log_level = settings.get('log_level', 'INFO')
    log_dir = os.path.join(script_dir, "logs")
    logger = setup_logging(log_level, log_dir)

    # Tampilkan banner (menggunakan teks ASCII sederhana untuk menghindari masalah encoding)
    print("\n")
    print("=" * 70)
    print("                   MULTI-CHAIN ARBITRAGE DETECTOR")
    print("      Mendeteksi peluang arbitrase real-time di berbagai jaringan EVM")
    print("=" * 70)
    print("\n")

    # Tampilkan informasi konfigurasi (menggunakan print biasa untuk menghindari masalah encoding)
    print("Jaringan yang Didukung:")
    for network_name in config['networks'].keys():
        print(f"  • {network_name}")

    print(f"\nInterval Pemeriksaan: {settings.get('check_interval_seconds', 15)} detik")
    print(f"Slippage: {settings.get('slippage_percentage', 0.5)}%")
    print(f"Level Log: {log_level}\n")

    # Simpan semua peluang arbitrase yang ditemukan
    all_opportunities = []

    # Waktu mulai
    start_time = time.time()

    # Loop utama
    iteration = 0
    try:
        while not should_exit:
            iteration += 1
            print(f"Iterasi #{iteration}: Memulai pemeriksaan peluang arbitrase...")

            # Buat tugas untuk setiap jaringan
            tasks = []
            detectors = []

            for network_name, network_config in config['networks'].items():
                try:
                    # Inisialisasi objek Network dengan pengaturan
                    network = Network(network_name, network_config, settings)

                    # Inisialisasi detektor arbitrase
                    detector = ArbitrageDetector(network, network_config, settings)
                    detectors.append(detector)

                    # Buat tugas untuk deteksi arbitrase
                    task = asyncio.create_task(detector.detect_arbitrage_opportunities())
                    tasks.append(task)
                except Exception as e:
                    logger.error(f"Error saat menginisialisasi jaringan {network_name}: {e}")

            # Jalankan semua tugas secara bersamaan dengan penanganan error
            try:
                # Gunakan timeout untuk menghindari program hang
                timeout = settings.get('network_timeout_seconds', 120)
                results = await asyncio.wait_for(
                    asyncio.gather(*tasks, return_exceptions=True),
                    timeout=timeout
                )

                # Periksa hasil untuk error
                for i, result in enumerate(results):
                    if isinstance(result, Exception):
                        network_name = list(config['networks'].keys())[i]
                        logger.error(f"Error saat mendeteksi arbitrase di jaringan {network_name}: {result}")

                        # Jika error koneksi, tambahkan jeda lebih lama
                        if isinstance(result, (ConnectionError, ConnectionResetError, TimeoutError)):
                            logger.warning(f"Terdeteksi error koneksi di jaringan {network_name}. Menambahkan jeda lebih lama.")
                            # Jeda tambahan untuk menghindari rate limiting
                            await asyncio.sleep(settings.get('connection_error_pause_seconds', 10))
            except asyncio.TimeoutError:
                logger.warning(f"Timeout saat mendeteksi arbitrase. Melanjutkan ke iterasi berikutnya.")
            except Exception as e:
                logger.error(f"Error tidak terduga saat mendeteksi arbitrase: {e}")
                import traceback
                logger.debug(f"Traceback: {traceback.format_exc()}")

            # Kumpulkan peluang arbitrase dari semua detektor
            for detector in detectors:
                try:
                    if hasattr(detector, 'arbitrage_opportunities') and detector.arbitrage_opportunities:
                        all_opportunities.extend(detector.arbitrage_opportunities)

                        # Tampilkan peluang arbitrase yang baru ditemukan
                        for opportunity in detector.arbitrage_opportunities:
                            print(f"\n[!] Peluang arbitrase ditemukan di {opportunity['network']}!")
                            print(f"    Jalur: {' -> '.join(opportunity['token_path'])}")
                            print(f"    DEX: {' -> '.join(opportunity['dex_path'])}")
                            print(f"    Profit: ${opportunity['net_profit_usd']:.2f} ({opportunity['profit_percentage']:.2f}%)")
                except Exception as e:
                    logger.error(f"Error saat mengumpulkan peluang arbitrase: {e}")

                    # Reset daftar peluang arbitrase di detektor
                    detector.arbitrage_opportunities = []

            # Tampilkan ringkasan
            elapsed_time = time.time() - start_time
            hours, remainder = divmod(elapsed_time, 3600)
            minutes, seconds = divmod(remainder, 60)

            print(f"\nRingkasan setelah iterasi #{iteration}:")
            print(f"Waktu berjalan: {int(hours):02}:{int(minutes):02}:{int(seconds):02}")
            print(f"Total peluang arbitrase ditemukan: {len(all_opportunities)}")

            # Jika perlu keluar, keluar dari loop
            if should_exit:
                break

            # Tunggu interval yang dikonfigurasi
            interval = settings.get('check_interval_seconds', 15)
            print(f"Menunggu {interval} detik sebelum pemeriksaan berikutnya...")

            # Gunakan asyncio.sleep dengan pengecekan should_exit
            for i in range(interval):
                if should_exit:
                    break
                # Tampilkan countdown
                if (interval - i) % 5 == 0 and (interval - i) > 0:
                    print(f"  {interval - i} detik tersisa...")
                await asyncio.sleep(1)

    finally:
        # Tampilkan ringkasan akhir
        print("\n" + "=" * 70)
        print("                   RINGKASAN AKHIR")
        print("=" * 70)

        elapsed_time = time.time() - start_time
        hours, remainder = divmod(elapsed_time, 3600)
        minutes, seconds = divmod(remainder, 60)

        print(f"Total waktu berjalan: {int(hours):02}:{int(minutes):02}:{int(seconds):02}")
        print(f"Total iterasi: {iteration}")
        print(f"Total peluang arbitrase ditemukan: {len(all_opportunities)}")

        # Tampilkan peluang arbitrase terbaik
        if all_opportunities:
            # Urutkan berdasarkan profit bersih
            sorted_opportunities = sorted(all_opportunities, key=lambda x: x['net_profit_usd'], reverse=True)

            print("\nPeluang Arbitrase Terbaik:")
            for i, opportunity in enumerate(sorted_opportunities[:5]):  # Tampilkan 5 peluang terbaik
                print(f"\n{i+1}. Jaringan: {opportunity['network']}")
                print(f"   Jalur: {' -> '.join(opportunity['token_path'])}")
                print(f"   DEX: {' -> '.join(opportunity['dex_path'])}")
                print(f"   Profit: ${opportunity['net_profit_usd']:.2f} ({opportunity['profit_percentage']:.2f}%)")

        print("\nProgram berhenti dengan aman.")

if __name__ == "__main__":
    try:
        # Jalankan loop asyncio
        asyncio.run(main())
    except KeyboardInterrupt:
        print("Program dihentikan oleh pengguna.")
    except Exception as e:
        print(f"Error tidak terduga: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
