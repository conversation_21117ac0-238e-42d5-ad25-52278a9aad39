"""
Modul untuk mendapatkan daftar token populer dari berbagai jaringan.
"""
import json
import logging
import aiohttp
import asyncio
from typing import Dict, List, Any, Set
from web3 import Web3

logger = logging.getLogger("arbitrage_detector")

# Daftar token populer untuk setiap jaringan
POPULAR_TOKENS = {
    "ethereum": {
        "WETH": "******************************************",
        "USDC": "******************************************",
        "USDT": "******************************************",
        "DAI": "******************************************",
        "WBTC": "******************************************",
        "LINK": "******************************************",
        "UNI": "******************************************",
        "AAVE": "******************************************",
        "CRV": "******************************************",
        "COMP": "******************************************",
        "SNX": "******************************************",
        "MKR": "******************************************",
        "SUSHI": "******************************************",
        "YFI": "******************************************",
        "BAT": "******************************************",
        "MATIC": "******************************************",
        "GRT": "******************************************",
        "1INCH": "******************************************",
        "ENJ": "******************************************",
        "LRC": "******************************************"
    },
    "polygon": {
        "WMATIC": "******************************************",
        "USDC": "******************************************",
        "USDT": "******************************************",
        "DAI": "******************************************",
        "WETH": "******************************************",
        "WBTC": "******************************************",
        "AAVE": "******************************************",
        "LINK": "******************************************",
        "CRV": "******************************************",
        "SUSHI": "******************************************",
        "QUICK": "******************************************",
        "GHST": "******************************************",
        "SAND": "******************************************",
        "MANA": "******************************************",
        "BAL": "******************************************",
        "FRAX": "******************************************",
        "FXS": "******************************************",
        "DINO": "******************************************",
        "KLIMA": "******************************************",
        "AVAX": "******************************************"
    },
    "bsc": {
        "WBNB": "******************************************",
        "USDC": "******************************************",
        "USDT": "******************************************",
        "BUSD": "******************************************",
        "BTCB": "******************************************",
        "ETH": "******************************************",
        "CAKE": "******************************************",
        "XVS": "******************************************",
        "ALPACA": "******************************************",
        "AUTO": "******************************************",
        "BAKE": "******************************************",
        "BANANA": "******************************************",
        "BELT": "******************************************",
        "BUNNY": "******************************************",
        "EPS": "******************************************",
        "TWT": "******************************************",
        "SAFEMOON": "******************************************",
        "DODO": "******************************************",
        "BIFI": "******************************************",
        "SFUND": "******************************************"
    },
    "avalanche": {
        "WAVAX": "******************************************",
        "USDC": "******************************************",
        "USDT": "******************************************",
        "DAI": "******************************************",
        "WETH": "******************************************",
        "WBTC": "******************************************",
        "LINK": "******************************************",
        "JOE": "******************************************",
        "QI": "******************************************",
        "XAVA": "******************************************",
        "PNG": "******************************************",
        "SPELL": "******************************************",
        "TIME": "******************************************",
        "MIM": "******************************************",
        "AVME": "******************************************",
        "YAK": "******************************************",
        "PEFI": "******************************************",
        "SNOB": "******************************************",
        "FRAX": "******************************************",
        "FXS": "******************************************"
    },
    "fantom": {
        "WFTM": "******************************************",
        "USDC": "******************************************",
        "fUSDT": "******************************************",
        "DAI": "******************************************",
        "WETH": "******************************************",
        "WBTC": "******************************************",
        "SPIRIT": "******************************************",
        "BOO": "******************************************",
        "TOMB": "******************************************",
        "TSHARE": "******************************************",
        "BEETS": "******************************************",
        "SCREAM": "******************************************",
        "TAROT": "******************************************",
        "GEIST": "******************************************",
        "SPELL": "******************************************",
        "ICE": "******************************************",
        "LQDR": "******************************************",
        "CRV": "******************************************",
        "LINK": "******************************************",
        "FRAX": "******************************************"
    },
    "arbitrum": {
        "WETH": "0x82aF49447D8a07e3bd95BD0d56f35241523fBab1",
        "USDC": "0xFF970A61A04b1cA14834A43f5dE4533eBDDB5CC8",
        "USDT": "0xFd086bC7CD5C481DCC9C85ebE478A1C0b69FCbb9",
        "DAI": "0xDA10009cBd5D07dd0CeCc66161FC93D7c9000da1",
        "WBTC": "0x2f2a2543B76A4166549F7aaB2e75Bef0aefC5B0f",
        "ARB": "0x912CE59144191C1204E64559FE8253a0e49E6548",
        "GMX": "0xfc5A1A6EB076a2C7aD06eD22C90d7E710E35ad0a",
        "LINK": "0xf97f4df75117a78c1A5a0DBb814Af92458539FB4",
        "UNI": "0xFa7F8980b0f1E64A2062791cc3b0871572f1F7f0",
        "CRV": "0x11cDb42B0EB46D95f990BeDD4695A6e3fA034978",
        "BAL": "0x040d1EdC9569d4Bab2D15287Dc5A4F10F56a56B8",
        "MAGIC": "0x539bdE0d7Dbd336b79148AA742883198BBF60342",
        "RDNT": "0x3082CC23568eA640225c2467653dB90e9250AaA0",
        "DPX": "0x6C2C06790b3E3E3c38e12Ee22F8183b37a13EE55",
        "JONES": "0x10393c20975cF177a3513071bC110f7962CD67da",
        "SUSHI": "0xd4d42F0b6DEF4CE0383636770eF773390d85c61A",
        "VELA": "0x088cd8f5eF3652623c22D48b1605DCfE860Cd704",
        "STG": "0x6694340fc020c5E6B96567843da2df01b2CE1eb6",
        "FRAX": "0x17FC002b466eEc40DaE837Fc4bE5c67993ddBd6F",
        "FXS": "0x9d2F299715D94d8A7E6F5eaa8E654E8c74a988A7"
    },
    "optimism": {
        "WETH": "0x4200000000000000000000000000000000000006",
        "USDC": "0x7F5c764cBc14f9669B88837ca1490cCa17c31607",
        "USDT": "0x94b008aA00579c1307B0EF2c499aD98a8ce58e58",
        "DAI": "0xDA10009cBd5D07dd0CeCc66161FC93D7c9000da1",
        "WBTC": "0x68f180fcCe6836688e9084f035309E29Bf0A2095",
        "OP": "0x4200000000000000000000000000000000000042",
        "SNX": "0x8700dAec35aF8Ff88c16BdF0418774CB3D7599B4",
        "LINK": "0x350a791Bfc2C21F9Ed5d10980Dad2e2638ffa7f6",
        "UNI": "0x6fd9d7AD17242c41f7131d257212c54A0e816691",
        "AAVE": "0x76FB31fb4af56892A25e32cFC43De717950c9278",
        "FRAX": "0x2E3D870790dC77A83DD1d18184Acc7439A53f475",
        "FXS": "0x67CCEA5bb16181E7b4109c9c2143c24a1c2205Be",
        "LYRA": "0x50c5725949A6F0c72E6C4a641F24049A917DB0Cb",
        "PERP": "0x9e1028F5F1D5eDE59748FFceE5532509976840E0",
        "THALES": "0x217D47011b23BB961eB6D93cA9945B7501a5BB11",
        "VELO": "0x3c8B650257cFb5f272f799F5e2b4e65093a11a05",
        "sUSD": "0x8c6f28f2F1A3C87F0f938b96d27520d9751ec8d9",
        "QI": "0x3F56e0c36d275367b8C502090EDF38289b3dEa0d",
        "BIFI": "0x4E720DD3Ac5CFe1e1fbDE4935f386Bb1C66F4642",
        "DOLA": "0x8aE125E8653821E851F12A49F7765db9a9ce7384"
    }
}

async def fetch_token_list(url: str) -> List[Dict[str, Any]]:
    """
    Mengambil daftar token dari URL.

    Args:
        url: URL daftar token

    Returns:
        Daftar token
    """
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(url, timeout=10) as response:
                if response.status == 200:
                    data = await response.json()
                    return data.get('tokens', [])
                else:
                    logger.warning(f"Gagal mengambil daftar token dari {url}: {response.status}")
                    return []
    except Exception as e:
        logger.error(f"Error saat mengambil daftar token dari {url}: {e}")
        return []

async def get_token_lists() -> Dict[str, Dict[str, str]]:
    """
    Mendapatkan daftar token dari berbagai sumber.

    Returns:
        Dictionary berisi daftar token untuk setiap jaringan
    """
    # URL daftar token
    token_list_urls = {
        "ethereum": [
            "https://tokens.coingecko.com/ethereum/all.json",
            "https://tokens.1inch.eth.link",
            "https://gateway.ipfs.io/ipns/tokens.uniswap.org"
        ],
        "polygon": [
            "https://tokens.coingecko.com/polygon-pos/all.json",
            "https://unpkg.com/quickswap-default-token-list@1.0.71/build/quickswap-default.tokenlist.json"
        ],
        "bsc": [
            "https://tokens.coingecko.com/binance-smart-chain/all.json",
            "https://tokens.pancakeswap.finance/pancakeswap-extended.json"
        ],
        "avalanche": [
            "https://tokens.coingecko.com/avalanche/all.json",
            "https://raw.githubusercontent.com/traderjoe-xyz/joe-tokenlists/main/joe.tokenlist.json"
        ],
        "fantom": [
            "https://tokens.coingecko.com/fantom/all.json",
            "https://raw.githubusercontent.com/SpookySwap/spooky-info/master/src/constants/token/spookyswap.json"
        ],
        "arbitrum": [
            "https://tokens.coingecko.com/arbitrum-one/all.json",
            "https://bridge.arbitrum.io/token-list-42161.json"
        ],
        "optimism": [
            "https://tokens.coingecko.com/optimistic-ethereum/all.json",
            "https://static.optimism.io/optimism.tokenlist.json"
        ]
    }

    # Hasil
    result = {}

    # Ambil daftar token dari setiap sumber
    for network, urls in token_list_urls.items():
        result[network] = {}

        # Tambahkan token populer
        result[network].update(POPULAR_TOKENS.get(network, {}))

        # Ambil daftar token dari setiap URL
        tasks = [fetch_token_list(url) for url in urls]
        token_lists = await asyncio.gather(*tasks)

        # Gabungkan semua daftar token
        for token_list in token_lists:
            for token in token_list:
                if 'address' in token and 'symbol' in token:
                    # Normalisasi alamat
                    address = Web3.to_checksum_address(token['address'])
                    symbol = token['symbol']

                    # Tambahkan ke hasil
                    if address not in result[network]:
                        result[network][symbol] = address

        # Batasi jumlah token
        if len(result[network]) > 500:
            # Ambil 500 token pertama
            result[network] = dict(list(result[network].items())[:500])

        logger.info(f"Berhasil mengambil {len(result[network])} token untuk jaringan {network}")

    return result

def get_default_tokens() -> Dict[str, Dict[str, str]]:
    """
    Mendapatkan daftar token default.

    Returns:
        Dictionary berisi daftar token untuk setiap jaringan
    """
    return POPULAR_TOKENS
