#!/usr/bin/env python3
"""
Test Event Loop Fix - Binance Hybrid Signal AI
===============================================

Script untuk testing perbaikan event loop conflict dalam threading.
"""

import asyncio
import threading
import time
import logging
from concurrent.futures import ThreadPoolExecutor

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(threadName)s - %(message)s'
)

def test_event_loop_in_thread(thread_id, pair_name):
    """Test event loop management dalam thread"""
    print(f"🧵 Thread {thread_id}: Testing {pair_name}")
    
    try:
        # Coba dapatkan loop yang ada
        try:
            loop = asyncio.get_event_loop()
            if loop.is_closed():
                raise RuntimeError("Loop is closed")
            print(f"✅ Thread {thread_id}: Using existing loop")
        except RuntimeError:
            # Buat loop baru untuk thread ini
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            print(f"🆕 Thread {thread_id}: Created new loop")
        
        # Test async operation
        async def mock_fetch_data():
            await asyncio.sleep(0.1)  # Simulate network delay
            return f"Mock data for {pair_name}"
        
        try:
            result = loop.run_until_complete(mock_fetch_data())
            print(f"✅ Thread {thread_id}: Async operation successful - {result}")
            return True
        finally:
            # Cleanup loop dengan aman
            try:
                if loop.is_running():
                    loop.stop()
                time.sleep(0.1)  # Wait a bit
                if not loop.is_closed():
                    loop.close()
                print(f"🧹 Thread {thread_id}: Loop cleaned up")
            except Exception as cleanup_e:
                print(f"⚠️ Thread {thread_id}: Loop cleanup warning: {cleanup_e}")
            finally:
                try:
                    asyncio.set_event_loop(None)
                except:
                    pass
                    
    except Exception as e:
        print(f"❌ Thread {thread_id}: Error - {e}")
        return False

def test_sync_fallback(thread_id, pair_name):
    """Test sync fallback method"""
    print(f"🔄 Thread {thread_id}: Testing sync fallback for {pair_name}")
    
    try:
        # Simulate sync operation
        time.sleep(0.1)  # Simulate processing time
        result = f"Sync fallback data for {pair_name}"
        print(f"✅ Thread {thread_id}: Sync fallback successful - {result}")
        return True
    except Exception as e:
        print(f"❌ Thread {thread_id}: Sync fallback error - {e}")
        return False

def test_concurrent_processing():
    """Test concurrent processing dengan multiple threads"""
    print("=" * 60)
    print("🧪 TESTING CONCURRENT EVENT LOOP MANAGEMENT")
    print("=" * 60)
    
    # Test pairs
    test_pairs = [
        'BTCUSDT', 'ETHUSDT', 'ADAUSDT', 'BNBUSDT', 'SOLUSDT',
        'DOGEUSDT', 'XRPUSDT', 'LTCUSDT', 'BCHUSDT', 'LINKUSDT'
    ]
    
    max_workers = 5  # Same as in the fixed program
    
    print(f"📊 Testing {len(test_pairs)} pairs with {max_workers} workers")
    print()
    
    # Test async method
    print("🔄 Testing Async Method with Event Loop Fix:")
    async_results = []
    
    with ThreadPoolExecutor(max_workers=max_workers, thread_name_prefix="AsyncTest") as executor:
        futures = []
        for i, pair in enumerate(test_pairs):
            future = executor.submit(test_event_loop_in_thread, i+1, pair)
            futures.append(future)
        
        for i, future in enumerate(futures):
            try:
                result = future.result(timeout=10)
                async_results.append(result)
                print(f"   ✅ Pair {i+1}: {'Success' if result else 'Failed'}")
            except Exception as e:
                async_results.append(False)
                print(f"   ❌ Pair {i+1}: Exception - {e}")
    
    print()
    print("🔄 Testing Sync Fallback Method:")
    sync_results = []
    
    with ThreadPoolExecutor(max_workers=max_workers, thread_name_prefix="SyncTest") as executor:
        futures = []
        for i, pair in enumerate(test_pairs):
            future = executor.submit(test_sync_fallback, i+1, pair)
            futures.append(future)
        
        for i, future in enumerate(futures):
            try:
                result = future.result(timeout=10)
                sync_results.append(result)
                print(f"   ✅ Pair {i+1}: {'Success' if result else 'Failed'}")
            except Exception as e:
                sync_results.append(False)
                print(f"   ❌ Pair {i+1}: Exception - {e}")
    
    # Results summary
    print()
    print("=" * 60)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    async_success = sum(async_results)
    sync_success = sum(sync_results)
    total_tests = len(test_pairs)
    
    print(f"🔄 Async Method:")
    print(f"   ✅ Success: {async_success}/{total_tests} ({async_success/total_tests*100:.1f}%)")
    print(f"   ❌ Failed:  {total_tests-async_success}/{total_tests} ({(total_tests-async_success)/total_tests*100:.1f}%)")
    
    print(f"🔄 Sync Fallback:")
    print(f"   ✅ Success: {sync_success}/{total_tests} ({sync_success/total_tests*100:.1f}%)")
    print(f"   ❌ Failed:  {total_tests-sync_success}/{total_tests} ({(total_tests-sync_success)/total_tests*100:.1f}%)")
    
    print()
    if async_success >= total_tests * 0.8:  # 80% success rate
        print("✅ ASYNC METHOD: PASSED (≥80% success rate)")
    else:
        print("❌ ASYNC METHOD: FAILED (<80% success rate)")
    
    if sync_success >= total_tests * 0.9:  # 90% success rate for sync
        print("✅ SYNC FALLBACK: PASSED (≥90% success rate)")
    else:
        print("❌ SYNC FALLBACK: FAILED (<90% success rate)")
    
    print()
    print("💡 RECOMMENDATIONS:")
    if async_success < total_tests * 0.8:
        print("   • Consider using sync fallback as primary method")
        print("   • Reduce number of concurrent workers")
        print("   • Implement better event loop isolation")
    else:
        print("   • Async method is working well with fixes")
        print("   • Sync fallback provides good backup")
        print("   • Current configuration is optimal")
    
    print("=" * 60)

def test_single_thread_async():
    """Test async operation dalam single thread"""
    print("🧪 Testing Single Thread Async Operation:")
    
    try:
        async def simple_async_test():
            await asyncio.sleep(0.1)
            return "Single thread async success"
        
        result = asyncio.run(simple_async_test())
        print(f"   ✅ Single thread: {result}")
        return True
    except Exception as e:
        print(f"   ❌ Single thread error: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 EVENT LOOP FIX TESTING - BINANCE HYBRID SIGNAL AI")
    print("=" * 60)
    
    # Test 1: Single thread async
    test_single_thread_async()
    print()
    
    # Test 2: Concurrent processing
    test_concurrent_processing()
    
    print()
    print("🎉 Testing completed!")
    print("📝 Check results above to verify event loop fixes are working")

if __name__ == "__main__":
    main()
