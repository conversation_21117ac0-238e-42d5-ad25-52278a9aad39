networks:
  ethereum:
    rpc_url: "https://eth.llamarpc.com"  # RPC publik gratis untuk Ethereum
    rpc_alternatives: [
      "https://ethereum.publicnode.com",
      "https://rpc.ankr.com/eth",
      "https://eth.meowrpc.com",
      "https://eth.rpc.blxrbdn.com",
      "https://ethereum.blockpi.network/v1/rpc/public",
      "https://rpc.flashbots.net",
      "https://1rpc.io/eth",
      "https://api.securerpc.com/v1"
    ]
    chain_id: 1
    gas_price_strategy: 'medium'  # 'fast', 'medium', 'slow'
    min_profit_usd: 5.0  # Ambang profit minimum dalam USD
    dexs:
      uniswap_v2:
        router_address: "******************************************"
        factory_address: "******************************************"
        abi_path: "abis/UniswapV2Router.json"
      sushiswap:
        router_address: "******************************************"
        factory_address: "******************************************"
        abi_path: "abis/UniswapV2Router.json"
      uniswap_v3:
        quoter_address: "******************************************"
        factory_address: "******************************************"
        abi_quoter_path: "abis/UniswapV3Quoter.json"
    tokens:
      WETH: "******************************************"
      USDC: "******************************************"
      USDT: "******************************************"
      DAI: "******************************************"
      WBTC: "******************************************"

  polygon:
    rpc_url: "https://polygon.llamarpc.com"  # RPC publik gratis untuk Polygon
    rpc_alternatives: [
      "https://polygon.publicnode.com",
      "https://polygon-rpc.com",
      "https://rpc.ankr.com/polygon"
    ]
    chain_id: 137
    gas_price_strategy: 'medium'
    min_profit_usd: 2.0
    dexs:
      quickswap:
        router_address: "******************************************"
        factory_address: "******************************************"
        abi_path: "abis/UniswapV2Router.json"
      sushiswap_polygon:
        router_address: "******************************************"
        factory_address: "******************************************"
        abi_path: "abis/UniswapV2Router.json"
    tokens:
      WMATIC: "******************************************"
      USDC: "******************************************"
      USDT: "******************************************"
      DAI: "******************************************"
      WETH: "******************************************"
      WBTC: "******************************************"

  bsc:
    rpc_url: "https://bsc.publicnode.com"  # RPC publik gratis untuk BSC
    rpc_alternatives: [
      "https://bsc-dataseed.binance.org",
      "https://bsc-dataseed1.defibit.io",
      "https://rpc.ankr.com/bsc"
    ]
    chain_id: 56
    gas_price_strategy: 'medium'
    min_profit_usd: 2.0
    dexs:
      pancakeswap:
        router_address: "******************************************"
        factory_address: "******************************************"
        abi_path: "abis/UniswapV2Router.json"
      sushiswap_bsc:
        router_address: "******************************************"
        factory_address: "******************************************"
        abi_path: "abis/UniswapV2Router.json"
    tokens:
      WBNB: "******************************************"
      USDC: "******************************************"
      USDT: "******************************************"
      BUSD: "******************************************"
      BTCB: "******************************************"
      ETH: "******************************************"

  avalanche:
    rpc_url: "https://api.avax.network/ext/bc/C/rpc"  # RPC publik gratis untuk Avalanche
    rpc_alternatives: [
      "https://avalanche-c-chain.publicnode.com",
      "https://rpc.ankr.com/avalanche",
      "https://avax.meowrpc.com"
    ]
    chain_id: 43114
    gas_price_strategy: 'medium'
    min_profit_usd: 2.0
    dexs:
      traderjoe:
        router_address: "******************************************"
        factory_address: "******************************************"
        abi_path: "abis/UniswapV2Router.json"
      sushiswap_avax:
        router_address: "******************************************"
        factory_address: "******************************************"
        abi_path: "abis/UniswapV2Router.json"
    tokens:
      WAVAX: "******************************************"
      USDC: "******************************************"
      USDT: "******************************************"
      DAI: "******************************************"
      WETH: "******************************************"
      WBTC: "******************************************"

  fantom:
    rpc_url: "https://rpc.ftm.tools"  # RPC publik gratis untuk Fantom
    rpc_alternatives: [
      "https://fantom.publicnode.com",
      "https://rpc.ankr.com/fantom",
      "https://rpcapi.fantom.network"
    ]
    chain_id: 250
    gas_price_strategy: 'medium'
    min_profit_usd: 2.0
    dexs:
      spookyswap:
        router_address: "******************************************"
        factory_address: "******************************************"
        abi_path: "abis/UniswapV2Router.json"
      spiritswap:
        router_address: "******************************************"
        factory_address: "******************************************"
        abi_path: "abis/UniswapV2Router.json"
    tokens:
      WFTM: "******************************************"
      USDC: "******************************************"
      fUSDT: "******************************************"
      DAI: "******************************************"
      WETH: "******************************************"
      WBTC: "******************************************"

  arbitrum:
    rpc_url: "https://arb1.arbitrum.io/rpc"  # RPC publik gratis untuk Arbitrum
    rpc_alternatives: [
      "https://arbitrum.publicnode.com",
      "https://rpc.ankr.com/arbitrum",
      "https://arbitrum-one.publicnode.com"
    ]
    chain_id: 42161
    gas_price_strategy: 'medium'
    min_profit_usd: 2.0
    dexs:
      sushiswap_arb:
        router_address: "******************************************"
        factory_address: "******************************************"
        abi_path: "abis/UniswapV2Router.json"
      camelot:
        router_address: "******************************************"
        factory_address: "******************************************"
        abi_path: "abis/UniswapV2Router.json"
    tokens:
      WETH: "******************************************"
      USDC: "******************************************"
      USDT: "******************************************"
      DAI: "******************************************"
      WBTC: "******************************************"

  optimism:
    rpc_url: "https://mainnet.optimism.io"  # RPC publik gratis untuk Optimism
    rpc_alternatives: [
      "https://optimism.publicnode.com",
      "https://rpc.ankr.com/optimism",
      "https://op-mainnet.public.blastapi.io"
    ]
    chain_id: 10
    gas_price_strategy: 'medium'
    min_profit_usd: 2.0
    dexs:
      uniswap_v3_op:
        quoter_address: "******************************************"
        factory_address: "******************************************"
        abi_quoter_path: "abis/UniswapV3Quoter.json"
      velodrome:
        router_address: "******************************************"
        factory_address: "******************************************"
        abi_path: "abis/UniswapV2Router.json"
    tokens:
      WETH: "******************************************"
      USDC: "******************************************"
      USDT: "******************************************"
      DAI: "******************************************"
      WBTC: "******************************************"

settings:
  # Pengaturan umum
  check_interval_seconds: 15  # Seberapa sering memeriksa peluang
  log_level: "INFO"  # DEBUG, INFO, WARNING, ERROR

  # Pengaturan arbitrase
  slippage_percentage: 5.0  # Persentase slippage yang digunakan dalam perhitungan
  max_price_difference_percentage: 50.0  # Perbedaan harga maksimum yang dianggap valid
  min_liquidity_usd: 10000.0  # Likuiditas minimum dalam USD

  # Pengaturan RPC
  retry_attempts: 5  # Jumlah percobaan ulang jika RPC gagal
  retry_delay_seconds: 3  # Waktu tunggu dasar antara percobaan ulang (ditingkatkan dari 2)
  max_retry_delay_seconds: 45  # Waktu tunggu maksimum antara percobaan ulang (ditingkatkan dari 30)
  rpc_timeout_seconds: 15  # Timeout untuk panggilan RPC (ditingkatkan dari 10)
  rate_limit_pause_seconds: 10  # Waktu jeda jika terkena rate limit (ditingkatkan dari 5)

  # Pengaturan penanganan error koneksi
  network_timeout_seconds: 180  # Timeout untuk seluruh proses deteksi arbitrase
  connection_error_pause_seconds: 15  # Waktu jeda jika terjadi error koneksi
  max_consecutive_errors: 3  # Jumlah maksimum error berturut-turut sebelum melewati jaringan

  # Pengaturan token dan DEX
  skip_problematic_tokens: true  # Lewati token yang bermasalah
  skip_problematic_dexs: true  # Lewati DEX yang bermasalah
  filter_stablecoins: true  # Filter pasangan stablecoin-stablecoin

  # Pengaturan optimasi
  use_extended_token_list: true  # Gunakan daftar token yang lebih besar
  max_tokens_per_network: 100  # Jumlah maksimum token per jaringan (dikurangi dari 500)
  max_tokens_for_triplet: 10  # Jumlah maksimum token untuk triplet (3-DEX) (dikurangi dari 20)
  max_token_pairs: 1000  # Jumlah maksimum pasangan token untuk 2-DEX (dikurangi dari 10000)
  max_token_triplets: 100  # Jumlah maksimum triplet token untuk 3-DEX (dikurangi dari 1000)
  token_info_batch_size: 5  # Ukuran batch untuk mendapatkan informasi token (dikurangi dari 10)
  token_pair_batch_size: 20  # Ukuran batch untuk pasangan token (dikurangi dari 50)
  token_triplet_batch_size: 5  # Ukuran batch untuk triplet token (dikurangi dari 10)
  max_batch_size: 3  # Ukuran maksimum batch untuk permintaan paralel (dikurangi dari 5)
  batch_cooldown_seconds: 1.0  # Waktu jeda antara batch (ditingkatkan dari 0.5)
  cache_ttl_seconds: 600  # Time-to-live untuk cache (10 menit) (ditingkatkan dari 5 menit)

  # Pengaturan deteksi arbitrase
  enable_three_dex_arbitrage: true  # Aktifkan deteksi arbitrase 3-DEX
