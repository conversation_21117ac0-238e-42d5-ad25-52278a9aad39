"""
File untuk menjalankan program dengan penanganan error yang lebih baik.
"""
import os
import sys
import time
import asyncio
import traceback

async def main():
    """
    Fungsi utama untuk menjalankan program.
    """
    print("Menjalankan program dengan penanganan error yang lebih baik...")

    # Dapatkan direktori script
    script_dir = os.path.dirname(os.path.abspath(__file__))

    # Tambahkan direktori saat ini ke sys.path
    sys.path.append(script_dir)

    # Impor modul-modul
    from src.utils import load_config, setup_logging
    from src.network import Network
    from src.dex import DEX

    # Muat konfigurasi
    config_path = os.path.join(script_dir, "config.yaml")
    config = load_config(config_path)

    # Dapatkan pengaturan
    settings = config.get('settings', {})

    # Setup logging
    logs_dir = os.path.join(script_dir, "logs")
    os.makedirs(logs_dir, exist_ok=True)
    logger = setup_logging(settings.get('log_level', 'INFO'), logs_dir)

    # Inisialisasi Network
    try:
        network = Network('ethereum', config['networks']['ethereum'], settings)

        # Cek koneksi
        if network.w3.is_connected():
            print("Berhasil terhubung ke jaringan Ethereum!")

            # Dapatkan nomor blok terbaru
            latest_block = network.w3.eth.block_number
            print(f"Nomor blok terbaru: {latest_block}")

            # Dapatkan harga gas
            gas_price = await network.get_gas_price()
            print(f"Harga gas: {gas_price / 1e9} Gwei")

            # Dapatkan harga token native dalam USD
            native_price_usd = await network.get_native_token_price_usd()
            print(f"Harga ETH: ${native_price_usd}")

            # Dapatkan informasi token
            token_addresses = {
                "WETH": "******************************************",
                "USDC": "******************************************",
                "USDT": "******************************************",
                "DAI": "******************************************",
                "WBTC": "******************************************"
            }

            print("\nInformasi Token:")
            for symbol, address in token_addresses.items():
                try:
                    token_symbol, token_name, token_decimals = await network.get_token_info(address)
                    print(f"  {symbol}: {token_name} ({token_symbol}), Desimal: {token_decimals}")
                except Exception as e:
                    print(f"  Error saat mendapatkan informasi token {symbol}: {e}")

            # Inisialisasi DEX
            dex_names = list(config['networks']['ethereum']['dexs'].keys())

            print("\nDEX yang Tersedia:")
            for dex_name in dex_names:
                try:
                    dex = DEX(dex_name, network)

                    # Dapatkan alamat router dari konfigurasi
                    dex_config = config['networks']['ethereum']['dexs'][dex_name]
                    router_address = dex_config.get('router_address', dex_config.get('quoter_address', 'N/A'))

                    print(f"  {dex_name}: {router_address}")

                    # Cek harga WETH/USDC
                    weth_address = token_addresses["WETH"]
                    usdc_address = token_addresses["USDC"]

                    # Jumlah WETH (1 WETH)
                    amount_in = 10 ** 18

                    # Dapatkan jumlah USDC yang akan diterima
                    try:
                        amounts_out = await dex.get_amounts_out(amount_in, [weth_address, usdc_address])
                        usdc_amount = amounts_out[1] / (10 ** 6)  # USDC memiliki 6 desimal
                        print(f"    Harga 1 WETH di {dex_name}: {usdc_amount} USDC")
                    except Exception as e:
                        print(f"    Error saat mendapatkan harga di {dex_name}: {e}")
                except Exception as e:
                    print(f"  Error saat menginisialisasi DEX {dex_name}: {e}")

            print("\nProgram selesai dengan sukses!")
        else:
            print("Gagal terhubung ke jaringan Ethereum.")
    except Exception as e:
        print(f"Error saat menjalankan program: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    # Jalankan dengan asyncio
    asyncio.run(main())
