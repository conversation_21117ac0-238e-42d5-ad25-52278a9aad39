"""
Modul untuk mengambil fee DEX secara real-time.
"""

import logging
import aiohttp
import asyncio
import json
import time
from typing import Dict, Any, Optional, Tuple
import requests

from config import DEFAULT_DEX_FEE_PERCENTAGE

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("dex_fee_fetcher")

# Cache untuk menyimpan fee DEX
DEX_FEE_CACHE = {}
# Waktu cache dalam detik (1 jam)
CACHE_EXPIRY = 3600

# Mapping DEX ID ke endpoint API fee
DEX_FEE_ENDPOINTS = {
    # Uniswap V2 & V3
    "uniswap-v2": "https://api.uniswap.org/v1/pools/fee",
    "uniswap-v3": "https://api.uniswap.org/v1/pools/fee",
    
    # PancakeSwap V2 & V3
    "pancakeswap-v2": "https://api.pancakeswap.info/api/v2/pairs",
    "pancakeswap-v3": "https://api.pancakeswap.info/api/v2/pairs",
    
    # SushiSwap
    "sushiswap": "https://api.sushi.com/pairs",
    
    # QuickSwap
    "quickswap": "https://api.quickswap.exchange/v1/pairs",
    
    # TraderJoe
    "traderjoe": "https://api.traderjoexyz.com/v1/pairs",
    
    # SpookySwap
    "spookyswap": "https://api.spookyswap.finance/pairs",
}

# Mapping DEX ID ke fee default (dalam persentase)
DEFAULT_DEX_FEES = {
    # Ethereum
    "uniswap-v2": 0.3,
    "uniswap-v3": 0.3,  # Bisa 0.05%, 0.3%, atau 1% tergantung pool
    "sushiswap": 0.3,
    "curve": 0.04,
    "balancer": 0.3,
    
    # BSC
    "pancakeswap-v2": 0.25,
    "pancakeswap-v3": 0.25,  # Bisa 0.01%, 0.05%, 0.3%, atau 1% tergantung pool
    "biswap": 0.2,
    "apeswap": 0.3,
    "mdex": 0.3,
    
    # Polygon
    "quickswap": 0.3,
    "sushiswap-polygon": 0.3,
    "uniswap-v3-polygon": 0.3,
    "balancer-polygon": 0.3,
    "meshswap": 0.3,
    
    # Avalanche
    "traderjoe": 0.3,
    "pangolin": 0.3,
    "sushiswap-avalanche": 0.3,
    "curve-avalanche": 0.04,
    
    # Fantom
    "spookyswap": 0.2,
    "spiritswap": 0.3,
    "sushiswap-fantom": 0.3,
    "curve-fantom": 0.04,
    
    # Arbitrum
    "sushiswap-arbitrum": 0.3,
    "uniswap-v3-arbitrum": 0.3,
    "camelot": 0.25,
    "curve-arbitrum": 0.04,
    
    # Optimism
    "uniswap-v3-optimism": 0.3,
    "curve-optimism": 0.04,
    "velodrome": 0.3,
    "zipswap": 0.3,
    
    # Solana
    "raydium": 0.25,
    "orca": 0.3,
    "serum": 0.22,
    "saber": 0.04,
}

async def fetch_dex_fee_async(dex_id: str, pair_address: str, chain_id: str) -> float:
    """
    Mengambil fee DEX secara asinkron dari API DEX.
    
    Args:
        dex_id: ID DEX.
        pair_address: Alamat pair.
        chain_id: ID chain.
        
    Returns:
        Fee DEX dalam persentase.
    """
    # Cek cache terlebih dahulu
    cache_key = f"{dex_id}:{pair_address}:{chain_id}"
    if cache_key in DEX_FEE_CACHE:
        cache_entry = DEX_FEE_CACHE[cache_key]
        # Cek apakah cache masih valid
        if time.time() - cache_entry["timestamp"] < CACHE_EXPIRY:
            return cache_entry["fee"]
    
    # Jika tidak ada di cache atau cache sudah expired, ambil dari API
    try:
        # Cek apakah DEX memiliki endpoint API fee
        if dex_id in DEX_FEE_ENDPOINTS:
            endpoint = DEX_FEE_ENDPOINTS[dex_id]
            
            # Tambahkan parameter sesuai dengan DEX
            params = {}
            if "uniswap" in dex_id:
                params["pairAddress"] = pair_address
            elif "pancakeswap" in dex_id:
                params["pair"] = pair_address
            elif "sushiswap" in dex_id:
                params["id"] = pair_address
            else:
                params["address"] = pair_address
            
            async with aiohttp.ClientSession() as session:
                async with session.get(endpoint, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        # Parse fee sesuai dengan format respons DEX
                        fee = None
                        if "uniswap" in dex_id:
                            fee = data.get("fee", None)
                        elif "pancakeswap" in dex_id:
                            pair_data = data.get("data", {}).get(pair_address, {})
                            fee = pair_data.get("fee", None)
                        elif "sushiswap" in dex_id:
                            fee = data.get("swapFee", None)
                        elif "quickswap" in dex_id:
                            fee = data.get("fee", None)
                        elif "traderjoe" in dex_id:
                            fee = data.get("fee", None)
                        elif "spookyswap" in dex_id:
                            fee = data.get("fee", None)
                        
                        if fee is not None:
                            # Konversi ke persentase jika perlu
                            if fee > 1:
                                fee = fee / 100
                            
                            # Simpan ke cache
                            DEX_FEE_CACHE[cache_key] = {
                                "fee": fee,
                                "timestamp": time.time()
                            }
                            
                            return fee
    except Exception as e:
        logger.warning(f"Error saat mengambil fee DEX untuk {dex_id} di {chain_id}: {e}")
    
    # Jika tidak berhasil mengambil dari API, gunakan nilai default dari mapping
    if dex_id in DEFAULT_DEX_FEES:
        fee = DEFAULT_DEX_FEES[dex_id]
        
        # Simpan ke cache
        DEX_FEE_CACHE[cache_key] = {
            "fee": fee,
            "timestamp": time.time()
        }
        
        return fee
    
    # Jika tidak ada di mapping, gunakan nilai default global
    return DEFAULT_DEX_FEE_PERCENTAGE

def fetch_dex_fee(dex_id: str, pair_address: str, chain_id: str) -> float:
    """
    Mengambil fee DEX secara sinkron dari API DEX.
    
    Args:
        dex_id: ID DEX.
        pair_address: Alamat pair.
        chain_id: ID chain.
        
    Returns:
        Fee DEX dalam persentase.
    """
    # Cek cache terlebih dahulu
    cache_key = f"{dex_id}:{pair_address}:{chain_id}"
    if cache_key in DEX_FEE_CACHE:
        cache_entry = DEX_FEE_CACHE[cache_key]
        # Cek apakah cache masih valid
        if time.time() - cache_entry["timestamp"] < CACHE_EXPIRY:
            return cache_entry["fee"]
    
    # Jika tidak ada di cache atau cache sudah expired, ambil dari API
    try:
        # Cek apakah DEX memiliki endpoint API fee
        if dex_id in DEX_FEE_ENDPOINTS:
            endpoint = DEX_FEE_ENDPOINTS[dex_id]
            
            # Tambahkan parameter sesuai dengan DEX
            params = {}
            if "uniswap" in dex_id:
                params["pairAddress"] = pair_address
            elif "pancakeswap" in dex_id:
                params["pair"] = pair_address
            elif "sushiswap" in dex_id:
                params["id"] = pair_address
            else:
                params["address"] = pair_address
            
            response = requests.get(endpoint, params=params)
            if response.status_code == 200:
                data = response.json()
                
                # Parse fee sesuai dengan format respons DEX
                fee = None
                if "uniswap" in dex_id:
                    fee = data.get("fee", None)
                elif "pancakeswap" in dex_id:
                    pair_data = data.get("data", {}).get(pair_address, {})
                    fee = pair_data.get("fee", None)
                elif "sushiswap" in dex_id:
                    fee = data.get("swapFee", None)
                elif "quickswap" in dex_id:
                    fee = data.get("fee", None)
                elif "traderjoe" in dex_id:
                    fee = data.get("fee", None)
                elif "spookyswap" in dex_id:
                    fee = data.get("fee", None)
                
                if fee is not None:
                    # Konversi ke persentase jika perlu
                    if fee > 1:
                        fee = fee / 100
                    
                    # Simpan ke cache
                    DEX_FEE_CACHE[cache_key] = {
                        "fee": fee,
                        "timestamp": time.time()
                    }
                    
                    return fee
    except Exception as e:
        logger.warning(f"Error saat mengambil fee DEX untuk {dex_id} di {chain_id}: {e}")
    
    # Jika tidak berhasil mengambil dari API, gunakan nilai default dari mapping
    if dex_id in DEFAULT_DEX_FEES:
        fee = DEFAULT_DEX_FEES[dex_id]
        
        # Simpan ke cache
        DEX_FEE_CACHE[cache_key] = {
            "fee": fee,
            "timestamp": time.time()
        }
        
        return fee
    
    # Jika tidak ada di mapping, gunakan nilai default global
    return DEFAULT_DEX_FEE_PERCENTAGE

def get_dex_fee(pair: Dict[str, Any]) -> float:
    """
    Mendapatkan fee DEX untuk pasangan token.
    
    Args:
        pair: Data pasangan token.
        
    Returns:
        Fee DEX dalam persentase.
    """
    dex_id = pair.get("dexId", "")
    pair_address = pair.get("pairAddress", "")
    chain_id = pair.get("chainId", "")
    
    # Cek apakah fee sudah ada di data pair dari Dexscreener
    if "fee" in pair:
        fee = pair.get("fee")
        # Konversi ke persentase jika perlu
        if fee > 1:
            fee = fee / 100
        return fee
    
    # Jika tidak ada di data pair, ambil dari API DEX
    return fetch_dex_fee(dex_id, pair_address, chain_id)

async def get_dex_fee_async(pair: Dict[str, Any]) -> float:
    """
    Mendapatkan fee DEX untuk pasangan token secara asinkron.
    
    Args:
        pair: Data pasangan token.
        
    Returns:
        Fee DEX dalam persentase.
    """
    dex_id = pair.get("dexId", "")
    pair_address = pair.get("pairAddress", "")
    chain_id = pair.get("chainId", "")
    
    # Cek apakah fee sudah ada di data pair dari Dexscreener
    if "fee" in pair:
        fee = pair.get("fee")
        # Konversi ke persentase jika perlu
        if fee > 1:
            fee = fee / 100
        return fee
    
    # Jika tidak ada di data pair, ambil dari API DEX
    return await fetch_dex_fee_async(dex_id, pair_address, chain_id)
