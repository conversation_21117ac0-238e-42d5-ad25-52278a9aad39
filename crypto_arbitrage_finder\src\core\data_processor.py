#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Data processor for normalizing exchange data.
"""

import logging
from datetime import datetime

class DataProcessor:
    """Data processor for normalizing exchange data."""
    
    def __init__(self, logger=None):
        """
        Initialize the data processor.
        
        Args:
            logger (logging.Logger, optional): Logger instance
        """
        self.logger = logger or logging.getLogger('crypto_arbitrage.data_processor')
    
    def normalize_ticker(self, exchange_id, ticker_data):
        """
        Normalize ticker data from an exchange.
        
        Args:
            exchange_id (str): The ID of the exchange
            ticker_data (dict): Raw ticker data from the exchange
            
        Returns:
            dict: Normalized ticker data
        """
        try:
            # Default values
            normalized = {
                'exchange': exchange_id,
                'symbol': None,
                'bid': None,
                'ask': None,
                'bid_size': None,
                'ask_size': None,
                'last': None,
                'volume': None,
                'timestamp': int(datetime.now().timestamp() * 1000),
                'datetime': datetime.now().isoformat()
            }
            
            # Exchange-specific normalization
            if exchange_id == 'binance':
                return self._normalize_binance_ticker(ticker_data, normalized)
            # Add other exchanges as they are implemented
            # elif exchange_id == 'coinbase':
            #     return self._normalize_coinbase_ticker(ticker_data, normalized)
            # ... and so on
            
            return normalized
        except Exception as e:
            self.logger.error(f"Error normalizing ticker data from {exchange_id}: {e}")
            return None
    
    def normalize_orderbook(self, exchange_id, orderbook_data):
        """
        Normalize order book data from an exchange.
        
        Args:
            exchange_id (str): The ID of the exchange
            orderbook_data (dict): Raw order book data from the exchange
            
        Returns:
            dict: Normalized order book data
        """
        try:
            # Default values
            normalized = {
                'exchange': exchange_id,
                'symbol': None,
                'bid': None,
                'ask': None,
                'bid_size': None,
                'ask_size': None,
                'bids': [],
                'asks': [],
                'timestamp': int(datetime.now().timestamp() * 1000),
                'datetime': datetime.now().isoformat()
            }
            
            # Exchange-specific normalization
            if exchange_id == 'binance':
                return self._normalize_binance_orderbook(orderbook_data, normalized)
            # Add other exchanges as they are implemented
            # elif exchange_id == 'coinbase':
            #     return self._normalize_coinbase_orderbook(orderbook_data, normalized)
            # ... and so on
            
            return normalized
        except Exception as e:
            self.logger.error(f"Error normalizing order book data from {exchange_id}: {e}")
            return None
    
    def _normalize_binance_ticker(self, ticker_data, normalized):
        """
        Normalize Binance ticker data.
        
        Args:
            ticker_data (dict): Raw Binance ticker data
            normalized (dict): Normalized data template
            
        Returns:
            dict: Normalized ticker data
        """
        try:
            normalized['symbol'] = self._normalize_symbol(ticker_data.get('s', ''))
            normalized['bid'] = float(ticker_data.get('b', 0))
            normalized['ask'] = float(ticker_data.get('a', 0))
            normalized['bid_size'] = float(ticker_data.get('B', 0))
            normalized['ask_size'] = float(ticker_data.get('A', 0))
            normalized['last'] = float(ticker_data.get('c', 0))
            normalized['volume'] = float(ticker_data.get('v', 0))
            
            if 'E' in ticker_data:
                normalized['timestamp'] = int(ticker_data['E'])
                normalized['datetime'] = datetime.fromtimestamp(int(ticker_data['E']) / 1000).isoformat()
            
            return normalized
        except Exception as e:
            self.logger.error(f"Error normalizing Binance ticker data: {e}")
            return normalized
    
    def _normalize_binance_orderbook(self, orderbook_data, normalized):
        """
        Normalize Binance order book data.
        
        Args:
            orderbook_data (dict): Raw Binance order book data
            normalized (dict): Normalized data template
            
        Returns:
            dict: Normalized order book data
        """
        try:
            if 's' in orderbook_data:
                normalized['symbol'] = self._normalize_symbol(orderbook_data['s'])
            
            bids = orderbook_data.get('bids', [])
            asks = orderbook_data.get('asks', [])
            
            if bids:
                normalized['bid'] = float(bids[0][0])
                normalized['bid_size'] = float(bids[0][1])
                normalized['bids'] = [[float(price), float(size)] for price, size in bids]
            
            if asks:
                normalized['ask'] = float(asks[0][0])
                normalized['ask_size'] = float(asks[0][1])
                normalized['asks'] = [[float(price), float(size)] for price, size in asks]
            
            if 'E' in orderbook_data:
                normalized['timestamp'] = int(orderbook_data['E'])
                normalized['datetime'] = datetime.fromtimestamp(int(orderbook_data['E']) / 1000).isoformat()
            
            return normalized
        except Exception as e:
            self.logger.error(f"Error normalizing Binance order book data: {e}")
            return normalized
    
    def _normalize_symbol(self, symbol):
        """
        Normalize symbol format.
        
        Args:
            symbol (str): Symbol in exchange-specific format
            
        Returns:
            str: Symbol in standard format (e.g., 'BTC/USDT')
        """
        # This is a simplified version. In a real implementation, we would need to
        # know the base and quote currencies for each symbol.
        
        # Common quote currencies
        quote_currencies = ['USDT', 'BTC', 'ETH', 'BNB', 'BUSD', 'USDC', 'USD', 'EUR']
        
        for quote in quote_currencies:
            if symbol.upper().endswith(quote):
                base = symbol[:-len(quote)]
                return f"{base}/{quote}"
        
        # If we can't determine the base/quote, return as is with a slash in the middle
        # This is just a fallback and might not be accurate
        if len(symbol) > 3:
            middle = len(symbol) // 2
            return f"{symbol[:middle]}/{symbol[middle:]}"
        
        return symbol
