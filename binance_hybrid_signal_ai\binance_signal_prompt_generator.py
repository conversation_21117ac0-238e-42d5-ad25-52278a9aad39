#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Binance Signal Prompt Generator
-------------------------------
Program untuk mencari peluang pada semua pairs di pasar futures binance
dan <PERSON><PERSON><PERSON><PERSON><PERSON> prompt untuk AI.

Author: bob<PERSON><PERSON>
"""

import os
import sys
import time
import json
import traceback
import ccxt
import pandas as pd
import numpy as np
import threading
from datetime import datetime
from typing import Dict, List, Tuple, Optional, Union, Any
from concurrent.futures import ThreadPoolExecutor, as_completed

# Import rich library untuk tampilan UI yang cantik
from rich.console import Console
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TaskProgressColumn
from rich.table import Table
from rich.text import Text
from rich.prompt import Prompt
from rich.layout import Layout
from rich.live import Live
from rich import box

# Import library ta untuk indikator teknikal
import ta
from ta.trend import (
    SMAIndicator, EMAIndicator, WMAIndicator, MACD, ADXIndicator,
    VortexIndicator, TRIXIndicator, MassIndex, CCIIndicator, DPOIndicator,
    KSTIndicator, IchimokuIndicator, PSARIndicator, STCIndicator
)
from ta.momentum import (
    RSIIndicator, StochRSIIndicator, TSIIndicator, UltimateOscillator,
    StochasticOscillator, WilliamsRIndicator, AwesomeOscillatorIndicator,
    KAMAIndicator, ROCIndicator, PercentagePriceOscillator, PercentageVolumeOscillator
)
from ta.volatility import (
    AverageTrueRange, BollingerBands, KeltnerChannel, DonchianChannel, UlcerIndex
)
from ta.volume import (
    MFIIndicator, AccDistIndexIndicator, OnBalanceVolumeIndicator,
    ChaikinMoneyFlowIndicator, ForceIndexIndicator, EaseOfMovementIndicator,
    VolumePriceTrendIndicator, NegativeVolumeIndexIndicator, VolumeWeightedAveragePrice
)
from ta.others import (
    DailyReturnIndicator, DailyLogReturnIndicator, CumulativeReturnIndicator
)

# Coba import pyperclip untuk fitur copy ke clipboard
try:
    import pyperclip
    HAS_PYPERCLIP = True
except ImportError:
    HAS_PYPERCLIP = False

# Konstanta
VERSION = "1.0.0"
MAX_THREADS = 10
TIMEFRAME = "1h"
CANDLE_LIMIT = 250
ANALYSIS_CANDLES = 50
TOP_SIGNALS = 5

class BinanceSignalGenerator:
    """Kelas utama untuk Binance Signal Prompt Generator"""

    def __init__(self):
        """Inisialisasi kelas"""
        self.console = Console()
        self.exchange = None
        self.pairs = []
        self.data = {}
        self.signals = {}
        self.top_signals = []

    def _format_value(self, value, precision=2):
        """
        Formats a value as a float with a given precision if it's a valid number.
        Returns 'N/A' otherwise.
        """
        try:
            if value is None:
                return "N/A"
            if isinstance(value, dict):
                return "N/A"
            if isinstance(value, (int, float)):
                if pd.isna(value) or np.isnan(value) or np.isinf(value):
                    return "N/A"
                return f"{value:.{precision}f}"
            # Try to convert to float
            float_value = float(value)
            if pd.isna(float_value) or np.isnan(float_value) or np.isinf(float_value):
                return "N/A"
            return f"{float_value:.{precision}f}"
        except (ValueError, TypeError, AttributeError):
            return "N/A"

    def initialize(self):
        """Inisialisasi koneksi ke Binance"""
        try:
            print("Memulai inisialisasi...")

            self.console.print(Panel.fit(
                "[bold cyan]Binance Signal Prompt Generator[/bold cyan]\n"
                f"[yellow]Versi {VERSION}[/yellow]\n"
                "[green]Menghubungkan ke Binance...[/green]",
                title="Inisialisasi",
                border_style="blue"
            ))

            print("Mencoba menghubungkan ke Binance...")

            # Inisialisasi exchange dengan rate limiting yang optimal
            self.exchange = ccxt.binance({
                'enableRateLimit': True,
                'options': {
                    'defaultType': 'future',
                    'adjustForTimeDifference': True,
                }
            })

            print("Koneksi ke Binance berhasil dibuat.")

            # Tes koneksi
            self.exchange.load_markets()
            self.console.print("[bold green]✓ Terhubung ke Binance Futures[/bold green]")

            # Ambil semua pairs futures
            self.pairs = []
            markets = self.exchange.load_markets()

            # Pertama coba dengan metode yang lebih spesifik untuk futures
            try:
                # Gunakan API khusus futures untuk mendapatkan semua kontrak
                futures_markets = self.exchange.fapiPublicGetExchangeInfo()
                if 'symbols' in futures_markets:
                    for symbol_info in futures_markets['symbols']:
                        if symbol_info.get('status') == 'TRADING':
                            # Format simbol yang benar untuk Binance Futures
                            symbol = f"{symbol_info['baseAsset']}/{symbol_info['quoteAsset']}"
                            self.pairs.append(symbol)
            except Exception as e:
                self.console.print(f"[yellow]Gagal mengambil data dengan API futures khusus: {str(e)}[/yellow]")
                self.console.print("[yellow]Mencoba dengan metode alternatif...[/yellow]")

                # Jika gagal, gunakan metode alternatif
                for symbol, market in markets.items():
                    # Cek apakah ini adalah pair futures yang aktif
                    if (market.get('active', False) and
                        (market.get('future', False) or
                         market.get('contract', False) or
                         market.get('linear', False)) and
                        '/USDT' in symbol):  # Hanya ambil pair dengan USDT
                        # Pastikan format simbol benar (tanpa suffix kontrak)
                        clean_symbol = symbol.split(':')[0] if ':' in symbol else symbol
                        if clean_symbol not in self.pairs:
                            self.pairs.append(clean_symbol)

            self.console.print(f"[bold green]✓ Menemukan {len(self.pairs)} pairs aktif di Binance Futures[/bold green]")
            return True

        except Exception as e:
            self.console.print(f"[bold red]Error: {str(e)}[/bold red]")
            return False

    def fetch_ohlcv_data(self, symbol: str) -> Optional[pd.DataFrame]:
        """Mengambil data OHLCV untuk sebuah simbol"""
        try:
            # Ambil data candle dengan penanganan rate limit
            for attempt in range(3):  # Coba hingga 3 kali jika terjadi error
                try:
                    # Coba ambil data dengan format yang benar
                    try:
                        ohlcv = self.exchange.fetch_ohlcv(symbol, TIMEFRAME, limit=CANDLE_LIMIT)
                    except Exception as e:
                        if "does not exist" in str(e).lower() or "not found" in str(e).lower():
                            return None  # Jika pair tidak ada, langsung return None
                        # Coba format alternatif (tanpa /USDT)
                        if '/USDT' in symbol:
                            try:
                                alt_symbol = symbol.replace('/USDT', 'USDT')
                                ohlcv = self.exchange.fetch_ohlcv(alt_symbol, TIMEFRAME, limit=CANDLE_LIMIT)
                            except:
                                # Jika masih gagal, coba format lain
                                try:
                                    alt_symbol = symbol.replace('/', '')
                                    ohlcv = self.exchange.fetch_ohlcv(alt_symbol, TIMEFRAME, limit=CANDLE_LIMIT)
                                except:
                                    self.console.print(f"[yellow]Tidak dapat menemukan format yang tepat untuk {symbol}[/yellow]")
                                    return None
                        else:
                            raise  # Re-raise jika bukan format /USDT

                    # Periksa apakah data valid
                    if not ohlcv or len(ohlcv) < 50:  # Minimal butuh 50 candle untuk analisis
                        self.console.print(f"[yellow]Data tidak cukup untuk {symbol}, melewati...[/yellow]")
                        return None

                    # Konversi ke DataFrame
                    df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])

                    # Periksa apakah data valid
                    if df.empty or df.isnull().values.any():
                        self.console.print(f"[yellow]Data tidak valid untuk {symbol}, melewati...[/yellow]")
                        return None

                    df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')

                    return df
                except ccxt.RateLimitExceeded:
                    if attempt < 2:  # Jika masih ada kesempatan mencoba lagi
                        self.console.print(f"[yellow]Rate limit terlampaui untuk {symbol}, menunggu dan mencoba lagi...[/yellow]")
                        time.sleep(2)  # Tunggu 2 detik sebelum mencoba lagi
                    else:
                        raise  # Re-raise exception jika sudah mencoba 3 kali
                except Exception as e:
                    self.console.print(f"[red]Error saat mengambil data {symbol} (attempt {attempt+1}): {str(e)}[/red]")
                    time.sleep(1)  # Tunggu sebentar sebelum mencoba lagi

            return None  # Return None jika semua percobaan gagal
        except Exception as e:
            self.console.print(f"[red]Error fatal saat mengambil data {symbol}: {str(e)}[/red]")
            return None

    def calculate_indicators(self, df: pd.DataFrame) -> Optional[pd.DataFrame]:
        """Menghitung semua indikator teknikal"""
        try:
            # Periksa apakah DataFrame valid
            if df is None or df.empty or len(df) < 200:  # Minimal butuh 200 candle untuk indikator
                self.console.print(f"[yellow]Data tidak cukup untuk menghitung indikator (butuh minimal 200 candle)[/yellow]")
                return None

            # Buat copy dari DataFrame
            df_with_indicators = df.copy()

            # === TREND INDICATORS ===
            # Moving Averages
            try:
                df_with_indicators['sma_20'] = SMAIndicator(close=df['close'], window=20).sma_indicator()
                df_with_indicators['sma_50'] = SMAIndicator(close=df['close'], window=50).sma_indicator()
                df_with_indicators['sma_100'] = SMAIndicator(close=df['close'], window=100).sma_indicator()
                df_with_indicators['sma_200'] = SMAIndicator(close=df['close'], window=200).sma_indicator()

                df_with_indicators['ema_9'] = EMAIndicator(close=df['close'], window=9).ema_indicator()
                df_with_indicators['ema_20'] = EMAIndicator(close=df['close'], window=20).ema_indicator()
                df_with_indicators['ema_50'] = EMAIndicator(close=df['close'], window=50).ema_indicator()
                df_with_indicators['ema_100'] = EMAIndicator(close=df['close'], window=100).ema_indicator()

                df_with_indicators['wma_20'] = WMAIndicator(close=df['close'], window=20).wma()
            except Exception as e:
                self.console.print(f"[yellow]Error saat menghitung Moving Averages: {str(e)}[/yellow]")

            # MACD
            try:
                macd = MACD(close=df['close'])
                df_with_indicators['macd'] = macd.macd()
                df_with_indicators['macd_signal'] = macd.macd_signal()
                df_with_indicators['macd_diff'] = macd.macd_diff()
            except Exception as e:
                self.console.print(f"[yellow]Error saat menghitung MACD: {str(e)}[/yellow]")

            # ADX
            try:
                adx = ADXIndicator(high=df['high'], low=df['low'], close=df['close'])
                df_with_indicators['adx'] = adx.adx()
                df_with_indicators['adx_pos'] = adx.adx_pos()
                df_with_indicators['adx_neg'] = adx.adx_neg()
            except Exception as e:
                self.console.print(f"[yellow]Error saat menghitung ADX: {str(e)}[/yellow]")

            # Vortex
            try:
                vortex = VortexIndicator(high=df['high'], low=df['low'], close=df['close'])
                df_with_indicators['vortex_pos'] = vortex.vortex_indicator_pos()
                df_with_indicators['vortex_neg'] = vortex.vortex_indicator_neg()
            except Exception as e:
                self.console.print(f"[yellow]Error saat menghitung Vortex: {str(e)}[/yellow]")

            # TRIX
            try:
                df_with_indicators['trix'] = TRIXIndicator(close=df['close']).trix()
            except Exception as e:
                self.console.print(f"[yellow]Error saat menghitung TRIX: {str(e)}[/yellow]")

            # CCI
            try:
                df_with_indicators['cci'] = CCIIndicator(high=df['high'], low=df['low'], close=df['close']).cci()
            except Exception as e:
                self.console.print(f"[yellow]Error saat menghitung CCI: {str(e)}[/yellow]")

            # DPO
            try:
                df_with_indicators['dpo'] = DPOIndicator(close=df['close']).dpo()
            except Exception as e:
                self.console.print(f"[yellow]Error saat menghitung DPO: {str(e)}[/yellow]")

            # Ichimoku
            try:
                ichimoku = IchimokuIndicator(high=df['high'], low=df['low'])
                df_with_indicators['ichimoku_a'] = ichimoku.ichimoku_a()
                df_with_indicators['ichimoku_b'] = ichimoku.ichimoku_b()
                df_with_indicators['ichimoku_base'] = ichimoku.ichimoku_base_line()
                df_with_indicators['ichimoku_conv'] = ichimoku.ichimoku_conversion_line()
            except Exception as e:
                self.console.print(f"[yellow]Error saat menghitung Ichimoku: {str(e)}[/yellow]")

            # Parabolic SAR
            try:
                psar = PSARIndicator(high=df['high'], low=df['low'], close=df['close'])
                df_with_indicators['psar_up'] = psar.psar_up()
                df_with_indicators['psar_down'] = psar.psar_down()
                df_with_indicators['psar_up_ind'] = psar.psar_up_indicator()
                df_with_indicators['psar_down_ind'] = psar.psar_down_indicator()
            except Exception as e:
                self.console.print(f"[yellow]Error saat menghitung Parabolic SAR: {str(e)}[/yellow]")

            # Aroon - menggunakan fungsi langsung
            try:
                # Karena aroon memerlukan high dan low, kita akan menggunakan close sebagai keduanya untuk sementara
                # Ini tidak ideal, tapi akan membuat program berjalan
                df_with_indicators['aroon_up'] = ta.trend.aroon_up(df['high'], df['low'], window=25, fillna=True)
                df_with_indicators['aroon_down'] = ta.trend.aroon_down(df['high'], df['low'], window=25, fillna=True)
            except Exception as e:
                self.console.print(f"[yellow]Error saat menghitung Aroon: {str(e)}[/yellow]")

            # === MOMENTUM INDICATORS ===
            # RSI
            try:
                df_with_indicators['rsi'] = RSIIndicator(close=df['close']).rsi()
            except Exception as e:
                self.console.print(f"[yellow]Error saat menghitung RSI: {str(e)}[/yellow]")

            # Stochastic RSI
            try:
                stoch_rsi = StochRSIIndicator(close=df['close'])
                df_with_indicators['stoch_rsi'] = stoch_rsi.stochrsi()
                df_with_indicators['stoch_rsi_k'] = stoch_rsi.stochrsi_k()
                df_with_indicators['stoch_rsi_d'] = stoch_rsi.stochrsi_d()
            except Exception as e:
                self.console.print(f"[yellow]Error saat menghitung Stochastic RSI: {str(e)}[/yellow]")

            # TSI
            try:
                df_with_indicators['tsi'] = TSIIndicator(close=df['close']).tsi()
            except Exception as e:
                self.console.print(f"[yellow]Error saat menghitung TSI: {str(e)}[/yellow]")

            # Ultimate Oscillator
            try:
                df_with_indicators['uo'] = UltimateOscillator(high=df['high'], low=df['low'], close=df['close']).ultimate_oscillator()
            except Exception as e:
                self.console.print(f"[yellow]Error saat menghitung Ultimate Oscillator: {str(e)}[/yellow]")

            # Stochastic
            try:
                stoch = StochasticOscillator(high=df['high'], low=df['low'], close=df['close'])
                df_with_indicators['stoch_k'] = stoch.stoch()
                df_with_indicators['stoch_d'] = stoch.stoch_signal()
            except Exception as e:
                self.console.print(f"[yellow]Error saat menghitung Stochastic: {str(e)}[/yellow]")

            # Williams %R
            try:
                df_with_indicators['williams_r'] = WilliamsRIndicator(high=df['high'], low=df['low'], close=df['close']).williams_r()
            except Exception as e:
                self.console.print(f"[yellow]Error saat menghitung Williams %R: {str(e)}[/yellow]")

            # Awesome Oscillator
            try:
                df_with_indicators['ao'] = AwesomeOscillatorIndicator(high=df['high'], low=df['low']).awesome_oscillator()
            except Exception as e:
                self.console.print(f"[yellow]Error saat menghitung Awesome Oscillator: {str(e)}[/yellow]")

            # KAMA
            try:
                df_with_indicators['kama'] = KAMAIndicator(close=df['close']).kama()
            except Exception as e:
                self.console.print(f"[yellow]Error saat menghitung KAMA: {str(e)}[/yellow]")

            # ROC
            try:
                df_with_indicators['roc'] = ROCIndicator(close=df['close']).roc()
            except Exception as e:
                self.console.print(f"[yellow]Error saat menghitung ROC: {str(e)}[/yellow]")

            # PPO
            try:
                ppo = PercentagePriceOscillator(close=df['close'])
                df_with_indicators['ppo'] = ppo.ppo()
                df_with_indicators['ppo_signal'] = ppo.ppo_signal()
                df_with_indicators['ppo_hist'] = ppo.ppo_hist()
            except Exception as e:
                self.console.print(f"[yellow]Error saat menghitung PPO: {str(e)}[/yellow]")

            # === VOLATILITY INDICATORS ===
            # ATR
            try:
                df_with_indicators['atr'] = AverageTrueRange(high=df['high'], low=df['low'], close=df['close']).average_true_range()
            except Exception as e:
                self.console.print(f"[yellow]Error saat menghitung ATR: {str(e)}[/yellow]")

            # Bollinger Bands
            try:
                bollinger = BollingerBands(close=df['close'])
                df_with_indicators['bb_high'] = bollinger.bollinger_hband()
                df_with_indicators['bb_mid'] = bollinger.bollinger_mavg()
                df_with_indicators['bb_low'] = bollinger.bollinger_lband()
                df_with_indicators['bb_width'] = bollinger.bollinger_wband()
                df_with_indicators['bb_pband'] = bollinger.bollinger_pband()
            except Exception as e:
                self.console.print(f"[yellow]Error saat menghitung Bollinger Bands: {str(e)}[/yellow]")

            # Keltner Channel
            try:
                keltner = KeltnerChannel(high=df['high'], low=df['low'], close=df['close'])
                df_with_indicators['kc_high'] = keltner.keltner_channel_hband()
                df_with_indicators['kc_mid'] = keltner.keltner_channel_mband()
                df_with_indicators['kc_low'] = keltner.keltner_channel_lband()
                df_with_indicators['kc_width'] = keltner.keltner_channel_wband()
            except Exception as e:
                self.console.print(f"[yellow]Error saat menghitung Keltner Channel: {str(e)}[/yellow]")

            # Donchian Channel
            try:
                donchian = DonchianChannel(high=df['high'], low=df['low'], close=df['close'])
                df_with_indicators['dc_high'] = donchian.donchian_channel_hband()
                df_with_indicators['dc_mid'] = donchian.donchian_channel_mband()
                df_with_indicators['dc_low'] = donchian.donchian_channel_lband()
            except Exception as e:
                self.console.print(f"[yellow]Error saat menghitung Donchian Channel: {str(e)}[/yellow]")

            # === VOLUME INDICATORS ===
            # MFI
            try:
                df_with_indicators['mfi'] = MFIIndicator(high=df['high'], low=df['low'], close=df['close'], volume=df['volume']).money_flow_index()
            except Exception as e:
                self.console.print(f"[yellow]Error saat menghitung MFI: {str(e)}[/yellow]")

            # OBV
            try:
                df_with_indicators['obv'] = OnBalanceVolumeIndicator(close=df['close'], volume=df['volume']).on_balance_volume()
            except Exception as e:
                self.console.print(f"[yellow]Error saat menghitung OBV: {str(e)}[/yellow]")

            # Chaikin Money Flow
            try:
                df_with_indicators['cmf'] = ChaikinMoneyFlowIndicator(high=df['high'], low=df['low'], close=df['close'], volume=df['volume']).chaikin_money_flow()
            except Exception as e:
                self.console.print(f"[yellow]Error saat menghitung Chaikin Money Flow: {str(e)}[/yellow]")

            # Force Index
            try:
                df_with_indicators['fi'] = ForceIndexIndicator(close=df['close'], volume=df['volume']).force_index()
            except Exception as e:
                self.console.print(f"[yellow]Error saat menghitung Force Index: {str(e)}[/yellow]")

            # Ease of Movement
            try:
                eom = EaseOfMovementIndicator(high=df['high'], low=df['low'], volume=df['volume'])
                df_with_indicators['eom'] = eom.ease_of_movement()
                df_with_indicators['sma_eom'] = eom.sma_ease_of_movement()
            except Exception as e:
                self.console.print(f"[yellow]Error saat menghitung Ease of Movement: {str(e)}[/yellow]")

            # Volume Price Trend
            try:
                df_with_indicators['vpt'] = VolumePriceTrendIndicator(close=df['close'], volume=df['volume']).volume_price_trend()
            except Exception as e:
                self.console.print(f"[yellow]Error saat menghitung Volume Price Trend: {str(e)}[/yellow]")

            # VWAP
            try:
                df_with_indicators['vwap'] = VolumeWeightedAveragePrice(high=df['high'], low=df['low'], close=df['close'], volume=df['volume']).volume_weighted_average_price()
            except Exception as e:
                self.console.print(f"[yellow]Error saat menghitung VWAP: {str(e)}[/yellow]")

            # Periksa apakah ada NaN values dan isi dengan 0
            df_with_indicators = df_with_indicators.fillna(0)

            return df_with_indicators

        except Exception as e:
            self.console.print(f"[red]Error fatal saat menghitung indikator: {str(e)}[/red]")
            return None

    def identify_support_resistance(self, df: pd.DataFrame, window: int = 10) -> Tuple[List[float], List[float]]:
        """Mengidentifikasi level support dan resistance dari data harga"""
        # Gunakan hanya data terakhir sesuai window
        recent_df = df.tail(ANALYSIS_CANDLES).copy()

        # Temukan swing high dan swing low
        highs = []
        lows = []

        for i in range(window, len(recent_df) - window):
            # Cek apakah ini adalah swing high
            if all(recent_df.iloc[i]['high'] > recent_df.iloc[i-j]['high'] for j in range(1, window+1)) and \
               all(recent_df.iloc[i]['high'] > recent_df.iloc[i+j]['high'] for j in range(1, window+1)):
                highs.append(recent_df.iloc[i]['high'])

            # Cek apakah ini adalah swing low
            if all(recent_df.iloc[i]['low'] < recent_df.iloc[i-j]['low'] for j in range(1, window+1)) and \
               all(recent_df.iloc[i]['low'] < recent_df.iloc[i+j]['low'] for j in range(1, window+1)):
                lows.append(recent_df.iloc[i]['low'])

        # Kelompokkan level yang berdekatan
        def group_levels(levels, threshold_pct=0.5):
            if not levels:
                return []

            # Urutkan level
            sorted_levels = sorted(levels)
            grouped = []
            current_group = [sorted_levels[0]]

            for i in range(1, len(sorted_levels)):
                # Jika level saat ini dekat dengan rata-rata grup saat ini
                current_avg = sum(current_group) / len(current_group)
                if abs(sorted_levels[i] - current_avg) / current_avg * 100 < threshold_pct:
                    current_group.append(sorted_levels[i])
                else:
                    # Tambahkan rata-rata grup saat ini ke hasil dan mulai grup baru
                    grouped.append(sum(current_group) / len(current_group))
                    current_group = [sorted_levels[i]]

            # Tambahkan grup terakhir
            if current_group:
                grouped.append(sum(current_group) / len(current_group))

            return grouped

        # Kelompokkan level support dan resistance
        support_levels = group_levels(lows)
        resistance_levels = group_levels(highs)

        return support_levels, resistance_levels

    def identify_chart_patterns(self, df: pd.DataFrame) -> List[Dict[str, Any]]:
        """Mengidentifikasi pola chart dari data harga"""
        try:
            # Periksa apakah DataFrame valid
            if df is None or df.empty or len(df) < ANALYSIS_CANDLES:
                return []

            # Gunakan hanya data terakhir
            recent_df = df.tail(ANALYSIS_CANDLES).copy()

            # Periksa apakah data valid
            if recent_df.empty or len(recent_df) < 20:  # Minimal butuh 20 candle untuk analisis pola
                return []

            patterns = []

            # === REVERSAL PATTERNS ===
            # Head and Shoulders
            try:
                self._check_head_and_shoulders(recent_df, patterns)
            except Exception as e:
                print(f"Error saat mengecek pola Head and Shoulders: {str(e)}")

            # Double Top / Double Bottom
            try:
                self._check_double_patterns(recent_df, patterns)
            except Exception as e:
                print(f"Error saat mengecek pola Double Top/Bottom: {str(e)}")

            # Triple Top / Triple Bottom - placeholder, tidak digunakan
            # self._check_triple_patterns(recent_df, patterns)

            # === CONTINUATION PATTERNS ===
            # Flags and Pennants - placeholder, tidak digunakan
            # self._check_flags_pennants(recent_df, patterns)

            # Triangles - placeholder, tidak digunakan
            # self._check_triangles(recent_df, patterns)

            # Rectangles - placeholder, tidak digunakan
            # self._check_rectangles(recent_df, patterns)

            # Cup and Handle - placeholder, tidak digunakan
            # self._check_cup_and_handle(recent_df, patterns)

            return patterns

        except Exception as e:
            print(f"Error saat mengidentifikasi pola chart: {str(e)}")
            return []

    def _check_head_and_shoulders(self, df: pd.DataFrame, patterns: List[Dict[str, Any]]) -> None:
        """Cek pola Head and Shoulders dan Inverse Head and Shoulders"""
        try:
            # Periksa apakah DataFrame valid
            if df is None or df.empty or len(df) < 20:  # Minimal butuh 20 candle
                return

            # Implementasi deteksi pola Head and Shoulders
            # Cari 3 puncak dengan puncak tengah lebih tinggi (H&S) atau 3 lembah dengan lembah tengah lebih rendah (Inverse H&S)
            window = 5

            # Pastikan DataFrame memiliki cukup data
            if len(df) <= 5*window:
                return

            for i in range(window, len(df) - 5*window):
                try:
                    # Head and Shoulders (bearish)
                    left_shoulder_high = df.iloc[i-window:i+window]['high'].max()
                    head_high = df.iloc[i+window:i+3*window]['high'].max()
                    right_shoulder_high = df.iloc[i+3*window:i+5*window]['high'].max()

                    if (left_shoulder_high == df.iloc[i]['high'] and  # Left shoulder
                        head_high > df.iloc[i]['high'] and  # Head
                        right_shoulder_high < df.iloc[i+2*window]['high'] and  # Right shoulder
                        abs(df.iloc[i]['high'] - df.iloc[i+4*window]['high']) / df.iloc[i]['high'] < 0.03):  # Shoulders pada level yang sama

                        patterns.append({
                            'type': 'Head and Shoulders',
                            'position': len(df) - i,
                            'direction': 'bearish'
                        })
                except Exception:
                    continue

                try:
                    # Inverse Head and Shoulders (bullish)
                    left_shoulder_low = df.iloc[i-window:i+window]['low'].min()
                    head_low = df.iloc[i+window:i+3*window]['low'].min()
                    right_shoulder_low = df.iloc[i+3*window:i+5*window]['low'].min()

                    if (left_shoulder_low == df.iloc[i]['low'] and  # Left shoulder
                        head_low < df.iloc[i]['low'] and  # Head
                        right_shoulder_low > df.iloc[i+2*window]['low'] and  # Right shoulder
                        abs(df.iloc[i]['low'] - df.iloc[i+4*window]['low']) / df.iloc[i]['low'] < 0.03):  # Shoulders pada level yang sama

                        patterns.append({
                            'type': 'Inverse Head and Shoulders',
                            'position': len(df) - i,
                            'direction': 'bullish'
                        })
                except Exception:
                    continue
        except Exception as e:
            print(f"Error saat mengecek pola Head and Shoulders: {str(e)}")

    def _check_double_patterns(self, df: pd.DataFrame, patterns: List[Dict[str, Any]]) -> None:
        """Cek pola Double Top dan Double Bottom"""
        try:
            # Periksa apakah DataFrame valid
            if df is None or df.empty or len(df) < 15:  # Minimal butuh 15 candle
                return

            window = 5

            # Pastikan DataFrame memiliki cukup data
            if len(df) <= 3*window:
                return

            for i in range(window, len(df) - 3*window):
                try:
                    # Double Top (bearish)
                    left_high = df.iloc[i-window:i+window]['high'].max()
                    right_high = df.iloc[i+window:i+3*window]['high'].max()

                    if (left_high == df.iloc[i]['high'] and
                        right_high >= df.iloc[i]['high'] * 0.98 and
                        right_high <= df.iloc[i]['high'] * 1.02):

                        patterns.append({
                            'type': 'Double Top',
                            'position': len(df) - i,
                            'direction': 'bearish'
                        })
                except Exception:
                    continue

                try:
                    # Double Bottom (bullish)
                    left_low = df.iloc[i-window:i+window]['low'].min()
                    right_low = df.iloc[i+window:i+3*window]['low'].min()

                    if (left_low == df.iloc[i]['low'] and
                        right_low >= df.iloc[i]['low'] * 0.98 and
                        right_low <= df.iloc[i]['low'] * 1.02):

                        patterns.append({
                            'type': 'Double Bottom',
                            'position': len(df) - i,
                            'direction': 'bullish'
                        })
                except Exception:
                    continue
        except Exception as e:
            print(f"Error saat mengecek pola Double Top/Bottom: {str(e)}")

    # Metode-metode berikut adalah placeholder untuk implementasi di masa depan
    # Saat ini tidak digunakan dalam program

    def _check_triple_patterns(self, _df: pd.DataFrame, _patterns: List[Dict[str, Any]]) -> None:  # noqa
        """Cek pola Triple Top dan Triple Bottom"""
        # TODO: Implementasi deteksi Triple Top dan Triple Bottom
        # Saat ini tidak digunakan
        return

    def _check_flags_pennants(self, _df: pd.DataFrame, _patterns: List[Dict[str, Any]]) -> None:  # noqa
        """Cek pola Flags dan Pennants"""
        # TODO: Implementasi deteksi Flags dan Pennants
        # Saat ini tidak digunakan
        return

    def _check_triangles(self, _df: pd.DataFrame, _patterns: List[Dict[str, Any]]) -> None:  # noqa
        """Cek pola Triangle (Ascending, Descending, Symmetrical)"""
        # TODO: Implementasi deteksi pola Triangle
        # Saat ini tidak digunakan
        return

    def _check_rectangles(self, _df: pd.DataFrame, _patterns: List[Dict[str, Any]]) -> None:  # noqa
        """Cek pola Rectangle"""
        # TODO: Implementasi deteksi pola Rectangle
        # Saat ini tidak digunakan
        return

    def _check_cup_and_handle(self, _df: pd.DataFrame, _patterns: List[Dict[str, Any]]) -> None:  # noqa
        """Cek pola Cup and Handle"""
        # TODO: Implementasi deteksi pola Cup and Handle
        # Saat ini tidak digunakan
        return

    def identify_orderblock(self, df: pd.DataFrame) -> Optional[Dict[str, Any]]:
        """Mengidentifikasi candle orderblock terakhir"""
        try:
            # Periksa apakah DataFrame valid
            if df is None or df.empty or len(df) < ANALYSIS_CANDLES:
                return None

            # Gunakan hanya data terakhir
            recent_df = df.tail(ANALYSIS_CANDLES).copy()

            # Periksa apakah data valid
            if recent_df.empty or len(recent_df) < 5:  # Minimal butuh 5 candle
                return None

            # Cari candle dengan volume tinggi yang diikuti oleh pergerakan harga signifikan
            orderblock = None

            try:
                threshold_volume = recent_df['volume'].mean() * 1.5  # Volume 50% di atas rata-rata
            except Exception as e:
                # Jika gagal menghitung threshold, gunakan nilai default
                threshold_volume = 0

            # Pastikan DataFrame memiliki cukup data
            if len(recent_df) <= 4:
                return None

            for i in range(1, len(recent_df) - 4):  # Pastikan ada cukup data untuk i+4
                try:
                    # Bullish orderblock (candle bearish dengan volume tinggi diikuti oleh pergerakan naik)
                    if (recent_df.iloc[i]['volume'] > threshold_volume and
                        recent_df.iloc[i]['close'] < recent_df.iloc[i]['open']):  # Bearish candle

                        # Periksa apakah ada kenaikan setelahnya
                        next_candles = recent_df.iloc[i+1:i+4]
                        if len(next_candles) > 0 and next_candles['close'].max() > recent_df.iloc[i]['high'] * 1.01:
                            orderblock = {
                                'type': 'Bullish Orderblock',
                                'position': len(recent_df) - i,
                                'price_range': [float(recent_df.iloc[i]['low']), float(recent_df.iloc[i]['high'])],
                                'volume': float(recent_df.iloc[i]['volume'])
                            }
                            break

                    # Bearish orderblock (candle bullish dengan volume tinggi diikuti oleh pergerakan turun)
                    if (recent_df.iloc[i]['volume'] > threshold_volume and
                        recent_df.iloc[i]['close'] > recent_df.iloc[i]['open']):  # Bullish candle

                        # Periksa apakah ada penurunan setelahnya
                        next_candles = recent_df.iloc[i+1:i+4]
                        if len(next_candles) > 0 and next_candles['close'].min() < recent_df.iloc[i]['low'] * 0.99:
                            orderblock = {
                                'type': 'Bearish Orderblock',
                                'position': len(recent_df) - i,
                                'price_range': [float(recent_df.iloc[i]['low']), float(recent_df.iloc[i]['high'])],
                                'volume': float(recent_df.iloc[i]['volume'])
                            }
                            break
                except Exception as e:
                    # Jika terjadi error pada candle ini, lanjutkan ke candle berikutnya
                    continue

            return orderblock
        except Exception as e:
            # Jika terjadi error, return None
            return None

    def analyze_pair(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Menganalisis sebuah pair dan mengembalikan hasil analisis"""
        try:
            # Ambil data OHLCV
            df = self.fetch_ohlcv_data(symbol)
            if df is None or len(df) < CANDLE_LIMIT:
                return None

            # Periksa apakah data valid
            if df.empty:
                return None

            # Ambil harga terakhir
            try:
                last_price = df.iloc[-1]['close']
            except Exception as e:
                self.console.print(f"[red]Error saat mengambil harga terakhir untuk {symbol}: {str(e)}[/red]")
                return None

            # Hitung indikator teknikal
            df_indicators = self.calculate_indicators(df)

            # Periksa apakah indikator berhasil dihitung
            if df_indicators is None:
                self.console.print(f"[yellow]Tidak dapat menghitung indikator untuk {symbol}[/yellow]")
                return None

            # Periksa apakah semua indikator yang diperlukan tersedia
            required_indicators = [
                'sma_20', 'sma_50', 'sma_100', 'sma_200',
                'ema_9', 'ema_20', 'ema_50',
                'macd', 'macd_signal', 'macd_diff',
                'adx', 'adx_pos', 'adx_neg',
                'rsi', 'stoch_k', 'stoch_d', 'williams_r', 'ao',
                'bb_high', 'bb_mid', 'bb_low',
                'mfi', 'cmf', 'obv'
            ]

            # Periksa apakah semua indikator yang diperlukan ada di DataFrame
            missing_indicators = [ind for ind in required_indicators if ind not in df_indicators.columns]
            if missing_indicators:
                self.console.print(f"[yellow]Indikator yang hilang untuk {symbol}: {', '.join(missing_indicators)}[/yellow]")
                return None

            # Identifikasi support dan resistance
            try:
                support_levels, resistance_levels = self.identify_support_resistance(df)
            except Exception as e:
                self.console.print(f"[yellow]Error saat mengidentifikasi support/resistance untuk {symbol}: {str(e)}[/yellow]")
                support_levels, resistance_levels = [], []

            # Identifikasi pola chart
            try:
                patterns = self.identify_chart_patterns(df)
            except Exception as e:
                self.console.print(f"[yellow]Error saat mengidentifikasi pola chart untuk {symbol}: {str(e)}[/yellow]")
                patterns = []

            # Identifikasi orderblock
            try:
                orderblock = self.identify_orderblock(df)
            except Exception as e:
                self.console.print(f"[yellow]Error saat mengidentifikasi orderblock untuk {symbol}: {str(e)}[/yellow]")
                orderblock = None

            # Analisis sinyal
            try:
                signal_strength, signal_direction = self.calculate_signal_strength(df_indicators)
            except Exception as e:
                self.console.print(f"[yellow]Error saat menghitung kekuatan sinyal untuk {symbol}: {str(e)}[/yellow]")
                signal_strength, signal_direction = 0, 'neutral'

            # Buat hasil analisis
            try:
                indicators_dict = df_indicators.iloc[-1].to_dict()

                # Pastikan semua nilai dalam indicators_dict adalah tipe data yang valid
                for key, value in indicators_dict.items():
                    if pd.isna(value) or pd.isnull(value) or np.isnan(value) if isinstance(value, float) else False:
                        indicators_dict[key] = 0

                analysis_result = {
                    'symbol': symbol,
                    'last_price': float(last_price),
                    'signal_strength': float(signal_strength),
                    'signal_direction': signal_direction,
                    'indicators': indicators_dict,
                    'support_levels': support_levels,
                    'resistance_levels': resistance_levels,
                    'patterns': patterns,
                    'orderblock': orderblock
                }

                return analysis_result
            except Exception as e:
                self.console.print(f"[red]Error saat membuat hasil analisis untuk {symbol}: {str(e)}[/red]")
                return None

        except Exception as e:
            self.console.print(f"[red]Error saat menganalisis {symbol}: {str(e)}[/red]")
            return None

    def calculate_signal_strength(self, df: pd.DataFrame) -> Tuple[float, str]:
        """Menghitung kekuatan sinyal berdasarkan indikator"""
        try:
            # Periksa apakah DataFrame valid
            if df is None or df.empty:
                return 0, 'neutral'

            # Ambil data terakhir
            try:
                last_row = df.iloc[-1]
            except Exception as e:
                print(f"Error saat mengambil baris terakhir: {str(e)}")
                return 0, 'neutral'

            # Inisialisasi counter untuk sinyal bullish dan bearish
            bullish_signals = 0
            bearish_signals = 0
            total_signals = 0

            # === TREND INDICATORS ===
            # Moving Averages
            # SMA
            try:
                if 'sma_20' in last_row and pd.notna(last_row['sma_20']):
                    if last_row['close'] > last_row['sma_20']:
                        bullish_signals += 1
                    else:
                        bearish_signals += 1
                    total_signals += 1
            except Exception:
                pass

            try:
                if 'sma_50' in last_row and pd.notna(last_row['sma_50']):
                    if last_row['close'] > last_row['sma_50']:
                        bullish_signals += 1
                    else:
                        bearish_signals += 1
                    total_signals += 1
            except Exception:
                pass

            try:
                if 'sma_100' in last_row and pd.notna(last_row['sma_100']):
                    if last_row['close'] > last_row['sma_100']:
                        bullish_signals += 1
                    else:
                        bearish_signals += 1
                    total_signals += 1
            except Exception:
                pass

            try:
                if 'sma_200' in last_row and pd.notna(last_row['sma_200']):
                    if last_row['close'] > last_row['sma_200']:
                        bullish_signals += 1
                    else:
                        bearish_signals += 1
                    total_signals += 1
            except Exception:
                pass

            # EMA
            try:
                if 'ema_20' in last_row and pd.notna(last_row['ema_20']):
                    if last_row['close'] > last_row['ema_20']:
                        bullish_signals += 1
                    else:
                        bearish_signals += 1
                    total_signals += 1
            except Exception:
                pass

            try:
                if 'ema_50' in last_row and pd.notna(last_row['ema_50']):
                    if last_row['close'] > last_row['ema_50']:
                        bullish_signals += 1
                    else:
                        bearish_signals += 1
                    total_signals += 1
            except Exception:
                pass

            # MACD
            try:
                if 'macd' in last_row and 'macd_signal' in last_row and pd.notna(last_row['macd']) and pd.notna(last_row['macd_signal']):
                    if last_row['macd'] > last_row['macd_signal']:
                        bullish_signals += 1
                    else:
                        bearish_signals += 1
                    total_signals += 1
            except Exception:
                pass

            try:
                if 'macd_diff' in last_row and pd.notna(last_row['macd_diff']):
                    if last_row['macd_diff'] > 0:
                        bullish_signals += 1
                    else:
                        bearish_signals += 1
                    total_signals += 1
            except Exception:
                pass

            # ADX
            try:
                if 'adx' in last_row and 'adx_pos' in last_row and 'adx_neg' in last_row and pd.notna(last_row['adx']):
                    if last_row['adx'] > 25:  # Trend kuat
                        if last_row['adx_pos'] > last_row['adx_neg']:
                            bullish_signals += 1
                        else:
                            bearish_signals += 1
                        total_signals += 1
            except Exception:
                pass

            # Parabolic SAR
            try:
                if 'psar_up' in last_row and 'psar_down' in last_row:
                    if pd.notna(last_row['psar_up']) and pd.isna(last_row['psar_down']):
                        bullish_signals += 1
                        total_signals += 1
                    elif pd.isna(last_row['psar_up']) and pd.notna(last_row['psar_down']):
                        bearish_signals += 1
                        total_signals += 1
            except Exception:
                pass

            # === MOMENTUM INDICATORS ===
            # RSI
            try:
                if 'rsi' in last_row and pd.notna(last_row['rsi']):
                    if last_row['rsi'] < 30:  # Oversold
                        bullish_signals += 1
                    elif last_row['rsi'] > 70:  # Overbought
                        bearish_signals += 1
                    total_signals += 1
            except Exception:
                pass

            # Stochastic
            try:
                if 'stoch_k' in last_row and 'stoch_d' in last_row and pd.notna(last_row['stoch_k']) and pd.notna(last_row['stoch_d']):
                    if last_row['stoch_k'] < 20 and last_row['stoch_d'] < 20:  # Oversold
                        bullish_signals += 1
                    elif last_row['stoch_k'] > 80 and last_row['stoch_d'] > 80:  # Overbought
                        bearish_signals += 1
                    total_signals += 1

                    if last_row['stoch_k'] > last_row['stoch_d']:  # Golden cross
                        bullish_signals += 1
                    else:  # Death cross
                        bearish_signals += 1
                    total_signals += 1
            except Exception:
                pass

            # Williams %R
            try:
                if 'williams_r' in last_row and pd.notna(last_row['williams_r']):
                    if last_row['williams_r'] < -80:  # Oversold
                        bullish_signals += 1
                    elif last_row['williams_r'] > -20:  # Overbought
                        bearish_signals += 1
                    total_signals += 1
            except Exception:
                pass

            # Awesome Oscillator
            try:
                if 'ao' in last_row and pd.notna(last_row['ao']):
                    if last_row['ao'] > 0:
                        bullish_signals += 1
                    else:
                        bearish_signals += 1
                    total_signals += 1
            except Exception:
                pass

            # === VOLATILITY INDICATORS ===
            # Bollinger Bands
            try:
                if 'bb_low' in last_row and 'bb_high' in last_row and pd.notna(last_row['bb_low']) and pd.notna(last_row['bb_high']):
                    if last_row['close'] < last_row['bb_low']:  # Harga di bawah lower band
                        bullish_signals += 1
                    elif last_row['close'] > last_row['bb_high']:  # Harga di atas upper band
                        bearish_signals += 1
                    total_signals += 1
            except Exception:
                pass

            # === VOLUME INDICATORS ===
            # MFI
            try:
                if 'mfi' in last_row and pd.notna(last_row['mfi']):
                    if last_row['mfi'] < 20:  # Oversold
                        bullish_signals += 1
                    elif last_row['mfi'] > 80:  # Overbought
                        bearish_signals += 1
                    total_signals += 1
            except Exception:
                pass

            # Chaikin Money Flow
            try:
                if 'cmf' in last_row and pd.notna(last_row['cmf']):
                    if last_row['cmf'] > 0:
                        bullish_signals += 1
                    else:
                        bearish_signals += 1
                    total_signals += 1
            except Exception:
                pass

            # Hitung kekuatan sinyal
            if total_signals > 0:
                if bullish_signals > bearish_signals:
                    signal_strength = bullish_signals / total_signals * 100
                    signal_direction = 'bullish'
                else:
                    signal_strength = bearish_signals / total_signals * 100
                    signal_direction = 'bearish'
            else:
                signal_strength = 0
                signal_direction = 'neutral'

            return signal_strength, signal_direction

        except Exception as e:
            print(f"Error saat menghitung kekuatan sinyal: {str(e)}")
            return 0, 'neutral'

    def analyze_all_pairs(self):
        """Menganalisis semua pairs dan menemukan sinyal terkuat"""
        # Periksa apakah ada pairs yang tersedia
        if not self.pairs:
            self.console.print("[bold red]Tidak ada pairs yang tersedia untuk dianalisis.[/bold red]")
            self.console.print("[yellow]Mencoba mengambil pairs lagi...[/yellow]")

            try:
                # Coba ambil pairs lagi
                self.exchange.load_markets(reload=True)
                self.pairs = []

                # Pertama coba dengan metode yang lebih spesifik untuk futures
                try:
                    # Gunakan API khusus futures untuk mendapatkan semua kontrak
                    futures_markets = self.exchange.fapiPublicGetExchangeInfo()
                    if 'symbols' in futures_markets:
                        for symbol_info in futures_markets['symbols']:
                            if symbol_info.get('status') == 'TRADING':
                                # Format simbol yang benar untuk Binance Futures
                                symbol = f"{symbol_info['baseAsset']}/{symbol_info['quoteAsset']}"
                                self.pairs.append(symbol)
                except Exception as e:
                    self.console.print(f"[yellow]Gagal mengambil data dengan API futures khusus: {str(e)}[/yellow]")
                    self.console.print("[yellow]Mencoba dengan metode alternatif...[/yellow]")

                    # Jika gagal, gunakan metode alternatif
                    for symbol, market in self.exchange.markets.items():
                        # Cek apakah ini adalah pair futures yang aktif
                        if (market.get('active', False) and
                            (market.get('future', False) or
                             market.get('contract', False) or
                             market.get('linear', False)) and
                            '/USDT' in symbol):  # Hanya ambil pair dengan USDT
                            # Pastikan format simbol benar (tanpa suffix kontrak)
                            clean_symbol = symbol.split(':')[0] if ':' in symbol else symbol
                            if clean_symbol not in self.pairs:
                                self.pairs.append(clean_symbol)

                if not self.pairs:
                    self.console.print("[bold red]Masih tidak dapat menemukan pairs. Periksa koneksi internet Anda.[/bold red]")
                    return
            except Exception as e:
                self.console.print(f"[bold red]Error saat mengambil pairs: {str(e)}[/bold red]")
                return

        self.console.print(Panel.fit(
            "[bold cyan]Menganalisis Pairs[/bold cyan]\n"
            f"[yellow]Total {len(self.pairs)} pairs akan dianalisis[/yellow]",
            title="Analisis",
            border_style="blue"
        ))

        # Gunakan semua pairs yang tersedia
        pairs_to_analyze = self.pairs
        self.console.print(f"[green]Menganalisis semua {len(pairs_to_analyze)} pairs yang tersedia...[/green]")

        # Buat progress bar
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TaskProgressColumn(),
            refresh_per_second=10
        ) as progress:
            task = progress.add_task("[cyan]Menganalisis pairs...", total=len(pairs_to_analyze))

            # Gunakan ThreadPoolExecutor untuk analisis paralel
            results = []
            with ThreadPoolExecutor(max_workers=MAX_THREADS) as executor:
                futures = {executor.submit(self.analyze_pair, pair): pair for pair in pairs_to_analyze}

                for future in as_completed(futures):
                    pair = futures[future]
                    try:
                        result = future.result()
                        if result is not None:
                            results.append(result)
                    except Exception as e:
                        self.console.print(f"[red]Error saat menganalisis {pair}: {str(e)}[/red]")

                    progress.update(task, advance=1)

        # Filter hasil yang valid
        valid_results = [r for r in results if r is not None]

        # Periksa apakah ada hasil valid
        if not valid_results:
            self.console.print("[bold yellow]Tidak ada sinyal valid yang ditemukan. Coba lagi nanti.[/bold yellow]")
            self.signals = {}
            self.top_signals = []
            return

        # Urutkan berdasarkan kekuatan sinyal
        sorted_results = sorted(valid_results, key=lambda x: x['signal_strength'], reverse=True)

        # Simpan hasil
        self.signals = {result['symbol']: result for result in sorted_results}

        # Ambil top signals
        self.top_signals = sorted_results[:TOP_SIGNALS]

        self.console.print(f"[bold green]✓ Analisis selesai. Ditemukan {len(valid_results)} pairs dengan sinyal valid.[/bold green]")

    def display_top_signals(self):
        """Menampilkan top signals"""
        if not self.top_signals:
            self.console.print("[yellow]Tidak ada sinyal yang ditemukan.[/yellow]")
            return

        # Buat tabel
        table = Table(title="Top 5 Sinyal Terkuat", box=box.ROUNDED)

        table.add_column("No", style="cyan", justify="center")
        table.add_column("Pair", style="green")
        table.add_column("Harga", justify="right")
        table.add_column("Sinyal", justify="center")
        table.add_column("Kekuatan", justify="right")
        table.add_column("Pola Chart", justify="left")

        for i, signal in enumerate(self.top_signals, 1):
            # Format harga
            price = self._format_value(signal.get('last_price'), 8).rstrip('0').rstrip('.')

            # Format sinyal
            signal_text = signal['signal_direction'].upper()
            signal_style = "green" if signal['signal_direction'] == 'bullish' else "red"

            # Format kekuatan
            strength = self._format_value(signal.get('signal_strength'), 2) + "%"

            # Format pola chart
            patterns_text = ", ".join([p['type'] for p in signal['patterns'][:2]]) if signal['patterns'] else "Tidak ada"

            table.add_row(
                str(i),
                signal['symbol'],
                price,
                f"[{signal_style}]{signal_text}[/{signal_style}]",
                strength,
                patterns_text
            )

        self.console.print(table)

    def generate_prompt(self, symbol: str) -> str:
        """Membuat prompt untuk AI berdasarkan analisis pair"""
        if symbol not in self.signals:
            return "Pair tidak ditemukan dalam hasil analisis."

        signal = self.signals[symbol]

        # Format support dan resistance
        support_text = ", ".join([self._format_value(level, 8).rstrip('0').rstrip('.') for level in signal.get('support_levels', [])])
        resistance_text = ", ".join([self._format_value(level, 8).rstrip('0').rstrip('.') for level in signal.get('resistance_levels', [])])

        # Format pola chart
        patterns_text = ""
        if signal['patterns']:
            for pattern in signal['patterns']:
                patterns_text += f"- {pattern['type']} (arah: {pattern['direction']})\n"
        else:
            patterns_text = "Tidak ada pola chart yang terdeteksi.\n"

        # Format orderblock
        orderblock_text = ""
        if signal.get('orderblock'):
            ob = signal['orderblock']
            price_range = [self._format_value(p, 8).rstrip('0').rstrip('.') for p in ob.get('price_range', [])]
            orderblock_text = f"{ob.get('type', 'Unknown')} terdeteksi pada posisi {ob.get('position', 'N/A')} candle dari akhir, dengan range harga {price_range[0] if price_range else 'N/A'} - {price_range[1] if len(price_range) > 1 else 'N/A'}."
        else:
            orderblock_text = "Tidak ada orderblock yang terdeteksi."

        # Format indikator
        indicators = signal['indicators']
        indicators_text = ""

        # Trend indicators
        indicators_text += "## Indikator Trend\n"
        indicators_text += f"- SMA 20: {self._format_value(indicators.get('sma_20'), 8)}\n"
        indicators_text += f"- SMA 50: {self._format_value(indicators.get('sma_50'), 8)}\n"
        indicators_text += f"- SMA 100: {self._format_value(indicators.get('sma_100'), 8)}\n"
        indicators_text += f"- SMA 200: {self._format_value(indicators.get('sma_200'), 8)}\n"
        indicators_text += f"- EMA 9: {self._format_value(indicators.get('ema_9'), 8)}\n"
        indicators_text += f"- EMA 20: {self._format_value(indicators.get('ema_20'), 8)}\n"
        indicators_text += f"- MACD: {self._format_value(indicators.get('macd'), 8)}\n"
        indicators_text += f"- MACD Signal: {self._format_value(indicators.get('macd_signal'), 8)}\n"
        indicators_text += f"- MACD Histogram: {self._format_value(indicators.get('macd_diff'), 8)}\n"
        indicators_text += f"- ADX: {self._format_value(indicators.get('adx'), 2)}\n"

        # Momentum indicators
        indicators_text += "\n## Indikator Momentum\n"
        indicators_text += f"- RSI: {self._format_value(indicators.get('rsi'), 2)}\n"
        indicators_text += f"- Stochastic K: {self._format_value(indicators.get('stoch_k'), 2)}\n"
        indicators_text += f"- Stochastic D: {self._format_value(indicators.get('stoch_d'), 2)}\n"
        indicators_text += f"- Williams %R: {self._format_value(indicators.get('williams_r'), 2)}\n"

        # Volatility indicators
        indicators_text += "\n## Indikator Volatilitas\n"
        indicators_text += f"- ATR: {self._format_value(indicators.get('atr'), 8)}\n"
        indicators_text += f"- Bollinger Upper: {self._format_value(indicators.get('bb_high'), 8)}\n"
        indicators_text += f"- Bollinger Middle: {self._format_value(indicators.get('bb_mid'), 8)}\n"
        indicators_text += f"- Bollinger Lower: {self._format_value(indicators.get('bb_low'), 8)}\n"

        # Volume indicators
        indicators_text += "\n## Indikator Volume\n"
        indicators_text += f"- MFI: {self._format_value(indicators.get('mfi'), 2)}\n"
        indicators_text += f"- CMF: {self._format_value(indicators.get('cmf'), 4)}\n"
        indicators_text += f"- OBV: {self._format_value(indicators.get('obv'), 2)}\n"

        # Buat prompt
        prompt = f"""
Tolong respon percakapan ini dengan gaya seperti grup sinyal di telegram yang informatif dan interaktif.

# Analisis Teknikal {symbol}

## Data Pasar
- Pair: {symbol}
- Harga Terakhir: {self._format_value(signal.get('last_price'), 8)}
- Sinyal: {signal.get('signal_direction', 'Unknown').upper()}
- Kekuatan Sinyal: {self._format_value(signal.get('signal_strength'), 2)}%

## Support dan Resistance
- Support: {support_text}
- Resistance: {resistance_text}

## Pola Chart
{patterns_text}

## Orderblock
{orderblock_text}

## Indikator Teknikal
{indicators_text}

Berdasarkan data di atas, tolong analisa lebih lanjut dengan mencari informasi terbaru di internet tentang {symbol.split('/')[0]} dan berikan:

1. Keputusan yang jelas dan tegas: BELI atau JUAL untuk {symbol}
2. Saran untuk entry point atau open posisi
3. Tingkat keyakinan analisa dalam persentase
4. Saran stop loss dan take profit jika menggunakan modal 3 juta rupiah
5. Rekomendasi leverage yang aman

signal analyzer ai by bobacheese
"""

        return prompt

    def run(self):
        """Menjalankan program utama"""
        try:
            # Tambahkan pesan debug
            print("Program dimulai...")

            # Tampilkan header
            self.console.print(Panel.fit(
                "[bold cyan]Binance Signal Prompt Generator[/bold cyan]\n"
                f"[yellow]Versi {VERSION}[/yellow]\n"
                "[green]Program pencari peluang pada semua pairs di pasar futures binance[/green]",
                title="Selamat Datang",
                border_style="blue"
            ))

            # Inisialisasi koneksi
            print("Mencoba inisialisasi koneksi...")
            max_retries = 3
            for attempt in range(max_retries):
                print(f"Percobaan {attempt+1} dari {max_retries}...")
                if self.initialize():
                    print("Inisialisasi berhasil!")
                    break
                else:
                    if attempt < max_retries - 1:
                        print(f"Percobaan {attempt+1} dari {max_retries} gagal. Mencoba lagi...")
                        self.console.print(f"[yellow]Percobaan {attempt+1} dari {max_retries} gagal. Mencoba lagi...[/yellow]")
                        time.sleep(2)  # Tunggu 2 detik sebelum mencoba lagi
                    else:
                        print("Semua percobaan gagal.")
                        self.console.print("[bold red]Gagal menginisialisasi program setelah beberapa percobaan. Silakan periksa koneksi internet Anda dan coba lagi nanti.[/bold red]")
                        return

            # Loop utama program
            running = True
            while running:
                try:
                    # Analisis semua pairs
                    self.analyze_all_pairs()

                    # Tampilkan top signals
                    self.display_top_signals()

                    # Tampilkan menu pilihan
                    if not self.top_signals:
                        choice = Prompt.ask(
                            "[bold cyan]Pilihan[/bold cyan]",
                            choices=["ulangi", "keluar"],
                            default="ulangi"
                        )

                        if choice == "ulangi":
                            continue
                        else:  # keluar
                            running = False
                            break

                    # Pilih pair untuk generate prompt
                    self.console.print("\n[bold cyan]Pilih nomor pair untuk generate prompt:[/bold cyan]")
                    pair_choice = Prompt.ask(
                        "Nomor pair",
                        choices=[str(i) for i in range(1, len(self.top_signals) + 1)] + ["menu", "ulangi", "keluar"],
                        default="1"
                    )

                    if pair_choice == "menu":
                        continue
                    elif pair_choice == "ulangi":
                        continue
                    elif pair_choice == "keluar":
                        running = False
                        break
                    else:
                        # Generate prompt untuk pair yang dipilih
                        selected_index = int(pair_choice) - 1
                        if selected_index >= len(self.top_signals):
                            self.console.print("[bold red]Nomor pair tidak valid![/bold red]")
                            continue

                        selected_pair = self.top_signals[selected_index]['symbol']

                        prompt = self.generate_prompt(selected_pair)

                        # Tampilkan prompt
                        self.console.print(Panel.fit(
                            prompt,
                            title=f"Prompt untuk {selected_pair}",
                            border_style="green",
                            width=100
                        ))

                        # Salin ke clipboard jika memungkinkan
                        if HAS_PYPERCLIP:
                            try:
                                pyperclip.copy(prompt)
                                self.console.print("[green]Prompt telah disalin ke clipboard![/green]")
                            except Exception as e:
                                self.console.print(f"[yellow]Gagal menyalin ke clipboard: {str(e)}[/yellow]")
                        else:
                            self.console.print("[yellow]Modul pyperclip tidak ditemukan. Prompt tidak dapat disalin otomatis.[/yellow]")

                        # Tampilkan menu setelah generate prompt
                        after_prompt = Prompt.ask(
                            "[bold cyan]Pilihan[/bold cyan]",
                            choices=["menu", "ulangi", "keluar"],
                            default="menu"
                        )

                        if after_prompt == "menu":
                            continue
                        elif after_prompt == "ulangi":
                            continue
                        else:  # keluar
                            running = False
                            break

                except Exception as e:
                    self.console.print(f"[bold red]Error dalam loop utama: {str(e)}[/bold red]")
                    self.console.print("[yellow]Mencoba melanjutkan...[/yellow]")
                    time.sleep(2)

            # Tampilkan pesan penutup
            self.console.print(Panel.fit(
                "[bold cyan]Terima kasih telah menggunakan Binance Signal Prompt Generator[/bold cyan]",
                border_style="blue"
            ))

        except KeyboardInterrupt:
            self.console.print("\n[bold yellow]Program dihentikan oleh pengguna.[/bold yellow]")
        except Exception as e:
            self.console.print(f"[bold red]Error fatal: {str(e)}[/bold red]")
            self.console.print(traceback.format_exc())


def main():
    """Fungsi utama program"""
    generator = BinanceSignalGenerator()
    generator.run()


if __name__ == "__main__":
    main()
