"""
Program utama untuk Jupiter Arbitrage Detector.
Program ini mencari peluang swap menguntungkan di Jupiter Aggregator pada blockchain Solana.
"""
import os
import sys
import asyncio
import signal
import logging
import time
from typing import Dict, Any, List, Optional
from pathlib import Path
from datetime import datetime

from src.utils import load_config, setup_logging, console
from src.jupiter_api import JupiterAPI
from src.arbitrage_detector import ArbitrageDetector

# Variabel global untuk menangani sinyal interupsi
should_exit = False

def signal_handler(sig, frame):
    """
    Menangani sinyal interupsi (Ctrl+C).
    """
    global should_exit
    print("\nMenerima sinyal interupsi. Menghentikan program dengan aman...")
    should_exit = True

async def main():
    """
    Fungsi utama program.
    """
    # Tangani sinyal interupsi
    signal.signal(signal.SIGINT, signal_handler)

    try:
        # Muat konfigurasi
        # Gunakan path relatif terhadap file main.py
        script_dir = os.path.dirname(os.path.abspath(__file__))
        config_path = os.path.join(script_dir, "config.yaml")
        config = load_config(config_path)

        # Dapatkan pengaturan
        settings = config.get('settings', {})

        # Setup logging
        log_level = settings.get('log_level', 'INFO')
        log_dir = os.path.join(script_dir, "logs")
        logger = setup_logging(log_level, log_dir)

        # Tampilkan banner dengan desain futuristik
        console.print("\n")
        console.print("[bold blue]╔══════════════════════════════════════════════════════════════════════════╗[/]")
        console.print("[bold blue]║                                                                          ║[/]")
        console.print("[bold blue]║                    [bold white]🚀 JUPITER ARBITRAGE DETECTOR 🚀[/]                    ║[/]")
        console.print("[bold blue]║                                                                          ║[/]")
        console.print("[bold blue]║      [white]Mendeteksi peluang swap menguntungkan di Jupiter Aggregator[/]      ║[/]")
        console.print("[bold blue]║                                                                          ║[/]")
        console.print("[bold blue]╚══════════════════════════════════════════════════════════════════════════╝[/]")
        console.print("\n")

        # Tampilkan informasi konfigurasi dengan desain futuristik
        console.print("[bold cyan]⚙️  KONFIGURASI:[/]")
        console.print(f"[bold]Base Token:[/] [yellow]SOL[/]")
        console.print(f"[bold]Jumlah Base Token:[/] [yellow]{settings.get('base_token_amount', 1.0)} SOL[/]")
        console.print(f"[bold]Slippage:[/] [yellow]{settings.get('slippage_percentage', 0.5)}%[/]")
        console.print(f"[bold]Profit Minimum:[/] [yellow]{settings.get('min_profit_percentage', 0.5)}%[/]")
        console.print(f"[bold]Interval Pemeriksaan:[/] [yellow]{settings.get('check_interval_seconds', 10)} detik[/]")
        console.print(f"[bold]Level Log:[/] [yellow]{log_level}[/]")
        console.print(f"[bold]API Version:[/] [yellow]Jupiter v6[/]")
        console.print("\n")

        # Inisialisasi Jupiter API
        jupiter_api = JupiterAPI(settings)

        # Inisialisasi Arbitrage Detector
        detector = ArbitrageDetector(jupiter_api, settings)

        # Simpan semua peluang arbitrase yang ditemukan
        all_opportunities = []

        # Waktu mulai
        start_time = time.time()

        # Loop utama
        iteration = 0

        # Untuk pengujian, batasi jumlah iterasi
        max_iterations = 2

        while not should_exit and iteration < max_iterations:
            iteration += 1
            console.print(f"\n[bold cyan]🔍 ITERASI #{iteration}:[/] [white]Memulai pemeriksaan peluang swap...[/]")

            # Tampilkan progress bar
            with console.status("[bold green]Mencari peluang swap...[/]", spinner="dots"):
                try:
                    # Deteksi peluang swap
                    opportunities = await detector.detect_opportunities()

                    # Tambahkan peluang yang ditemukan ke daftar
                    if opportunities:
                        all_opportunities.extend(opportunities)

                        # Peluang sudah ditampilkan oleh display_opportunity di arbitrage_detector.py
                    else:
                        console.print("[yellow]Tidak ditemukan peluang swap yang menguntungkan.[/]")
                except Exception as e:
                    logger.error(f"Error saat mendeteksi peluang swap: {e}")
                    console.print(f"[bold red]Error saat mendeteksi peluang swap: {e}[/]")
                    import traceback
                    traceback.print_exc()

            # Tampilkan ringkasan
            elapsed_time = time.time() - start_time
            hours, remainder = divmod(elapsed_time, 3600)
            minutes, seconds = divmod(remainder, 60)

            console.print("\n[bold cyan]📊 RINGKASAN ITERASI:[/]")
            console.print(f"[bold]Iterasi:[/] [yellow]#{iteration}[/]")
            console.print(f"[bold]Waktu berjalan:[/] [yellow]{int(hours):02}:{int(minutes):02}:{int(seconds):02}[/]")
            console.print(f"[bold]Total peluang swap ditemukan:[/] [yellow]{len(all_opportunities)}[/]")

            # Jika perlu keluar, keluar dari loop
            if should_exit or iteration >= max_iterations:
                break

            # Tunggu interval yang dikonfigurasi
            interval = settings.get('check_interval_seconds', 10)
            console.print(f"\n[bold cyan]⏱️ MENUNGGU:[/] [white]{interval} detik sebelum pemeriksaan berikutnya...[/]")

            # Gunakan asyncio.sleep dengan pengecekan should_exit dan progress bar
            with console.status("[bold green]Menunggu iterasi berikutnya...[/]", spinner="dots") as status:
                for i in range(interval):
                    if should_exit:
                        break

                    # Update status dengan countdown
                    remaining = interval - i
                    status.update(f"[bold green]Menunggu iterasi berikutnya... [white]{remaining}s[/][/]")

                    await asyncio.sleep(1)
    except Exception as e:
        print(f"Error saat menjalankan program: {e}")
        import traceback
        traceback.print_exc()

    finally:
        # Tampilkan ringkasan akhir dengan desain futuristik
        console.print("\n")
        console.print("[bold blue]╔══════════════════════════════════════════════════════════════════════════╗[/]")
        console.print("[bold blue]║                                                                          ║[/]")
        console.print("[bold blue]║                      [bold white]📊 RINGKASAN AKHIR 📊[/]                        ║[/]")
        console.print("[bold blue]║                                                                          ║[/]")
        console.print("[bold blue]╚══════════════════════════════════════════════════════════════════════════╝[/]")
        console.print("\n")

        elapsed_time = time.time() - start_time
        hours, remainder = divmod(elapsed_time, 3600)
        minutes, seconds = divmod(remainder, 60)

        # Tampilkan statistik
        console.print("[bold cyan]📈 STATISTIK:[/]")
        console.print(f"[bold]Total waktu berjalan:[/] [yellow]{int(hours):02}:{int(minutes):02}:{int(seconds):02}[/]")
        console.print(f"[bold]Total iterasi:[/] [yellow]{iteration}[/]")
        console.print(f"[bold]Total peluang swap ditemukan:[/] [yellow]{len(all_opportunities)}[/]")

        # Tampilkan peluang swap terbaik
        if all_opportunities:
            # Urutkan berdasarkan profit persentase
            sorted_opportunities = sorted(all_opportunities, key=lambda x: x['net_profit_percentage'] if 'net_profit_percentage' in x else x['profit_percentage'], reverse=True)

            console.print("\n[bold cyan]🏆 PELUANG SWAP TERBAIK:[/]")

            # Tampilkan tabel header
            console.print("[bold white on blue]╔════╦═══════════════╦════════════╦════════════════╦════════════════╦═══════════════╗[/]")
            console.print("[bold white on blue]║ NO ║     TOKEN     ║ JUMLAH SOL ║   NILAI AWAL   ║  NILAI AKHIR   ║    PROFIT     ║[/]")
            console.print("[bold white on blue]╠════╬═══════════════╬════════════╬════════════════╬════════════════╬═══════════════╣[/]")

            # Tampilkan 5 peluang terbaik dalam format tabel
            for i, opportunity in enumerate(sorted_opportunities[:5]):
                profit = opportunity.get('net_profit_usd', opportunity.get('profit_usd', 0))
                profit_pct = opportunity.get('net_profit_percentage', opportunity.get('profit_percentage', 0))

                # Tentukan warna berdasarkan profit
                profit_color = "green"
                if profit_pct > 5.0:
                    profit_color = "bright_green"
                elif profit_pct > 2.0:
                    profit_color = "green"
                elif profit_pct > 1.0:
                    profit_color = "green3"

                console.print(
                    f"[bold white on blue]║[/] [bold cyan]{i+1:2}[/] [bold white on blue]║[/] "
                    f"[bold yellow]{opportunity['token_symbol']:13}[/] [bold white on blue]║[/] "
                    f"[cyan]{opportunity['input_amount']:10.2f}[/] [bold white on blue]║[/] "
                    f"[yellow]${opportunity['input_value_usd']:14.2f}[/] [bold white on blue]║[/] "
                    f"[yellow]${opportunity['output_value_usd']:14.2f}[/] [bold white on blue]║[/] "
                    f"[bold {profit_color}]${profit:7.2f} ({profit_pct:4.2f}%)[/] [bold white on blue]║[/]"
                )

            # Tampilkan tabel footer
            console.print("[bold white on blue]╚════╩═══════════════╩════════════╩════════════════╩════════════════╩═══════════════╝[/]")

            # Tampilkan link ke Jupiter
            console.print("\n[bold cyan]🔗 LINK JUPITER:[/]")
            console.print(f"[link=https://jup.ag/swap/SOL-{sorted_opportunities[0]['token_symbol']}]https://jup.ag/swap/SOL-{sorted_opportunities[0]['token_symbol']}[/link]")

        # Tampilkan footer
        console.print("\n[bold blue]╔══════════════════════════════════════════════════════════════════════════╗[/]")
        console.print("[bold blue]║                    [bold white]Program berhenti dengan aman[/]                     ║[/]")
        console.print("[bold blue]╚══════════════════════════════════════════════════════════════════════════╝[/]")

if __name__ == "__main__":
    # Jalankan dengan asyncio
    asyncio.run(main())
