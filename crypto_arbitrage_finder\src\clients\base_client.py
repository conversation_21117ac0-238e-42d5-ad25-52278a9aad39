#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Base WebSocket client for cryptocurrency exchanges.
"""

import asyncio
import json
import logging
import time
from abc import ABC, abstractmethod
import websockets
from websockets.exceptions import ConnectionClosed

class BaseClient(ABC):
    """Base class for all exchange WebSocket clients."""

    def __init__(self, exchange_id, websocket_url, logger=None):
        """
        Initialize the base client.

        Args:
            exchange_id (str): The ID of the exchange
            websocket_url (str): The WebSocket URL for the exchange
            logger (logging.Logger, optional): Logger instance
        """
        self.exchange_id = exchange_id
        self.websocket_url = websocket_url
        self.logger = logger or logging.getLogger(f'crypto_arbitrage.{exchange_id}')
        self.ws = None
        self.connected = False
        self.reconnect_delay = 1  # Initial reconnect delay in seconds
        self.max_reconnect_delay = 60  # Maximum reconnect delay in seconds
        self.subscribed_pairs = set()
        self.market_data = {}  # Store latest market data
        self.last_heartbeat = time.time()
        self.heartbeat_interval = 30  # Default heartbeat interval in seconds
        self.connection_task = None
        self.processing_task = None

    async def connect(self):
        """Establish WebSocket connection to the exchange."""
        try:
            self.logger.info(f"Connecting to {self.exchange_id} WebSocket at {self.websocket_url}")
            self.ws = await websockets.connect(self.websocket_url)
            self.connected = True
            self.reconnect_delay = 1  # Reset reconnect delay on successful connection
            self.last_heartbeat = time.time()
            self.logger.info(f"Connected to {self.exchange_id} WebSocket")

            # Perform any exchange-specific initialization
            await self.on_connect()

            return True
        except Exception as e:
            self.connected = False
            self.logger.error(f"Failed to connect to {self.exchange_id} WebSocket: {e}")
            return False

    async def disconnect(self):
        """Close the WebSocket connection."""
        if self.ws:
            try:
                self.logger.info(f"Disconnecting from {self.exchange_id} WebSocket")
                await self.ws.close()
            except Exception as e:
                self.logger.error(f"Error disconnecting from {self.exchange_id} WebSocket: {e}")
            finally:
                self.ws = None
                self.connected = False

    async def reconnect(self):
        """Reconnect to the WebSocket with exponential backoff."""
        await self.disconnect()

        self.logger.info(f"Attempting to reconnect to {self.exchange_id} in {self.reconnect_delay} seconds")
        await asyncio.sleep(self.reconnect_delay)

        # Exponential backoff for reconnect delay
        self.reconnect_delay = min(self.reconnect_delay * 2, self.max_reconnect_delay)

        return await self.connect()

    async def subscribe_to_ticker(self, symbol):
        """
        Subscribe to ticker updates for a symbol.

        Args:
            symbol (str): The trading pair symbol (e.g., 'BTC/USDT')
        """
        if not self.connected:
            self.logger.warning(f"Cannot subscribe to {symbol} on {self.exchange_id}: Not connected")
            return False

        try:
            await self._subscribe_ticker(symbol)
            self.subscribed_pairs.add(symbol)
            self.logger.info(f"Subscribed to {symbol} ticker on {self.exchange_id}")
            return True
        except Exception as e:
            self.logger.error(f"Failed to subscribe to {symbol} ticker on {self.exchange_id}: {e}")
            return False

    async def subscribe_to_orderbook(self, symbol):
        """
        Subscribe to order book updates for a symbol.

        Args:
            symbol (str): The trading pair symbol (e.g., 'BTC/USDT')
        """
        if not self.connected:
            self.logger.warning(f"Cannot subscribe to {symbol} orderbook on {self.exchange_id}: Not connected")
            return False

        try:
            await self._subscribe_orderbook(symbol)
            self.subscribed_pairs.add(symbol)
            self.logger.info(f"Subscribed to {symbol} orderbook on {self.exchange_id}")
            return True
        except Exception as e:
            self.logger.error(f"Failed to subscribe to {symbol} orderbook on {self.exchange_id}: {e}")
            return False

    async def send_message(self, message):
        """
        Send a message to the WebSocket.

        Args:
            message (dict): The message to send
        """
        if not self.connected or not self.ws:
            self.logger.warning(f"Cannot send message to {self.exchange_id}: Not connected")
            return False

        try:
            await self.ws.send(json.dumps(message))
            return True
        except Exception as e:
            self.logger.error(f"Failed to send message to {self.exchange_id}: {e}")
            self.connected = False
            return False

    async def receive_message(self):
        """Receive a message from the WebSocket."""
        if not self.connected or not self.ws:
            return None

        try:
            message = await self.ws.recv()

            # Check if message is binary (compressed)
            if isinstance(message, bytes):
                try:
                    # Try to decompress with zlib
                    import zlib
                    message = zlib.decompress(message, zlib.MAX_WBITS | 32).decode('utf-8')
                    self.logger.debug(f"Decompressed binary message from {self.exchange_id}")
                except Exception as e:
                    self.logger.error(f"Failed to decompress message from {self.exchange_id}: {e}")
                    # Return raw message and let the exchange-specific handler deal with it

            return message
        except ConnectionClosed:
            self.logger.warning(f"WebSocket connection to {self.exchange_id} closed")
            self.connected = False
            return None
        except Exception as e:
            self.logger.error(f"Error receiving message from {self.exchange_id}: {e}")
            self.connected = False
            return None

    async def heartbeat(self):
        """Send heartbeat to keep the connection alive if needed."""
        if not self.connected:
            return

        current_time = time.time()
        if current_time - self.last_heartbeat >= self.heartbeat_interval:
            try:
                await self._send_heartbeat()
                self.last_heartbeat = current_time
            except Exception as e:
                self.logger.error(f"Failed to send heartbeat to {self.exchange_id}: {e}")

    async def start(self):
        """Start the WebSocket client."""
        if self.connection_task is not None:
            self.logger.warning(f"{self.exchange_id} client is already running")
            return

        self.connection_task = asyncio.create_task(self._connection_handler())
        self.processing_task = asyncio.create_task(self._message_handler())

        self.logger.info(f"Started {self.exchange_id} WebSocket client")

    async def stop(self):
        """Stop the WebSocket client."""
        self.logger.info(f"Stopping {self.exchange_id} WebSocket client")

        if self.connection_task:
            self.connection_task.cancel()
            self.connection_task = None

        if self.processing_task:
            self.processing_task.cancel()
            self.processing_task = None

        await self.disconnect()

        self.logger.info(f"Stopped {self.exchange_id} WebSocket client")

    async def _connection_handler(self):
        """Handle the WebSocket connection and reconnection."""
        while True:
            try:
                if not self.connected:
                    await self.connect()

                    # After successful connection, resubscribe to all previously subscribed pairs
                    if self.subscribed_pairs:
                        self.logger.info(f"Resubscribing to {len(self.subscribed_pairs)} pairs after reconnection")
                        for symbol in self.subscribed_pairs:
                            await self._subscribe_orderbook(symbol)

                await self.heartbeat()
                await asyncio.sleep(1)
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Error in {self.exchange_id} connection handler: {e}")
                # If there was an error, wait a bit longer before trying again
                await asyncio.sleep(self.reconnect_delay)

    async def _message_handler(self):
        """Handle incoming WebSocket messages."""
        while True:
            try:
                if not self.connected:
                    await asyncio.sleep(1)
                    continue

                message = await self.receive_message()
                if message:
                    await self._process_message(message)
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Error in {self.exchange_id} message handler: {e}")
                await asyncio.sleep(1)

    def get_orderbook_data(self, symbol):
        """
        Get the current order book data for a symbol.
        This is a synchronous version of the method for UI updates.

        Args:
            symbol (str): The trading pair symbol (e.g., 'BTC/USDT')

        Returns:
            dict: Order book data with 'bid', 'ask', 'bid_size', 'ask_size'
        """
        # Check if we have real data
        orderbook = self.market_data.get(symbol, {}).get('orderbook')

        # If we don't have real data and this is a common symbol, create dummy data
        # This is useful for UI testing and development
        if not orderbook and symbol in ['BTC/USDT', 'ETH/USDT', 'BNB/USDT', 'SOL/USDT', 'XRP/USDT']:
            from datetime import datetime
            import random

            # Base prices for common symbols
            base_prices = {
                'BTC/USDT': 65000.0,
                'ETH/USDT': 3500.0,
                'BNB/USDT': 600.0,
                'SOL/USDT': 150.0,
                'XRP/USDT': 0.55,
            }

            # Get base price
            base_price = base_prices.get(symbol, 1000.0)

            # Add some randomness based on exchange
            exchange_factor = 1.0
            if self.exchange_id == 'binance':
                exchange_factor = 1.0
            elif self.exchange_id == 'kucoin':
                exchange_factor = 0.99

            # Apply exchange factor
            mid_price = base_price * exchange_factor

            # Create a small spread
            spread = mid_price * 0.001  # 0.1% spread
            bid = mid_price - spread/2
            ask = mid_price + spread/2

            # Create dummy sizes
            bid_size = 1.0
            ask_size = 1.0

            # Create dummy orderbook
            orderbook = {
                'symbol': symbol,
                'bid': bid,
                'ask': ask,
                'bid_size': bid_size,
                'ask_size': ask_size,
                'bids': [[bid, bid_size]],
                'asks': [[ask, ask_size]],
                'timestamp': int(datetime.now().timestamp() * 1000),
                'datetime': datetime.now().isoformat(),
                'exchange': self.exchange_id
            }

            # Store the dummy data
            if symbol not in self.market_data:
                self.market_data[symbol] = {}
            self.market_data[symbol]['orderbook'] = orderbook

        return orderbook

    async def get_ticker_data(self, symbol):
        """
        Get the current ticker data for a symbol.

        Args:
            symbol (str): The trading pair symbol (e.g., 'BTC/USDT')

        Returns:
            dict: Ticker data
        """
        if symbol not in self.market_data:
            return None

        return self.market_data.get(symbol, {}).get('ticker')

    @abstractmethod
    async def on_connect(self):
        """
        Called after a successful connection.
        Implement exchange-specific initialization here.
        """
        pass

    @abstractmethod
    async def _subscribe_ticker(self, symbol):
        """
        Subscribe to ticker updates for a symbol.

        Args:
            symbol (str): The trading pair symbol (e.g., 'BTC/USDT')
        """
        pass

    @abstractmethod
    async def _subscribe_orderbook(self, symbol):
        """
        Subscribe to order book updates for a symbol.

        Args:
            symbol (str): The trading pair symbol (e.g., 'BTC/USDT')
        """
        pass

    @abstractmethod
    async def _send_heartbeat(self):
        """Send heartbeat to keep the connection alive if needed."""
        pass

    @abstractmethod
    async def _process_message(self, message):
        """
        Process incoming WebSocket message.

        Args:
            message (str): The raw message from the WebSocket
        """
        pass

    @abstractmethod
    async def get_all_symbols(self):
        """
        Get all available trading pairs from the exchange.

        Returns:
            list: List of trading pair symbols (e.g., ['BTC/USDT', 'ETH/USDT'])
        """
        pass
