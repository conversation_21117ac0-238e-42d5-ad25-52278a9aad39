#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Core module for finding arbitrage opportunities between exchanges.
"""

import asyncio
import logging
import time
from datetime import datetime
from ..clients.client_factory import ClientFactory
from ..utils.config_loader import ConfigLoader

class ArbitrageFinder:
    """Core class for finding arbitrage opportunities."""

    def __init__(self, logger=None):
        """
        Initialize the arbitrage finder.

        Args:
            logger (logging.Logger, optional): Logger instance
        """
        self.logger = logger or logging.getLogger('crypto_arbitrage.arbitrage_finder')
        self.client_factory = ClientFactory(self.logger)
        self.config_loader = ConfigLoader(self.logger)
        self.running = False
        self.scan_interval = 1.0  # Scan interval in seconds
        self.min_profit_threshold = 0.005  # Minimum profit threshold (0.5%)
        self.scan_task = None
        self.exchanges = {}
        self.common_symbols = set()
        self.opportunities = []  # List to store arbitrage opportunities
        self.errors = []  # List to store error messages

    async def start(self):
        """Start the arbitrage finder."""
        if self.running:
            self.logger.warning("Arbitrage finder is already running")
            return

        self.logger.info("Starting arbitrage finder")
        self.running = True

        # Initialize exchanges
        await self._initialize_exchanges()

        # Start scanning for arbitrage opportunities
        self.scan_task = asyncio.create_task(self._scan_loop())

        self.logger.info("Arbitrage finder started")

    async def stop(self):
        """Stop the arbitrage finder."""
        if not self.running:
            self.logger.warning("Arbitrage finder is not running")
            return

        self.logger.info("Stopping arbitrage finder")
        self.running = False

        if self.scan_task:
            self.scan_task.cancel()
            self.scan_task = None

        # Stop all clients
        await self.client_factory.stop_all_clients()

        self.logger.info("Arbitrage finder stopped")

    def find_arbitrage_opportunities(self):
        """Get the current arbitrage opportunities.

        Returns:
            list: List of arbitrage opportunities
        """
        # Clear old opportunities (older than 60 seconds)
        current_time = datetime.now()
        self.opportunities = [opp for opp in self.opportunities
                             if (current_time - datetime.fromisoformat(opp['timestamp'])).total_seconds() < 60]

        # Sort opportunities by profit percentage (descending)
        self.opportunities.sort(key=lambda x: x['profit_percent'], reverse=True)

        # Return a copy of the opportunities list
        return self.opportunities.copy()

    def add_error(self, exchange_id, message):
        """Add an error message to the errors list.

        Args:
            exchange_id (str): The ID of the exchange
            message (str): The error message
        """
        error = {
            'time': datetime.now().strftime('%H:%M:%S'),
            'exchange': exchange_id,
            'message': message,
            'timestamp': datetime.now().isoformat()
        }

        # Add to errors list
        self.errors.append(error)

        # Keep only the last 20 errors
        if len(self.errors) > 20:
            self.errors = self.errors[-20:]

    def get_errors(self):
        """Get the current error messages.

        Returns:
            list: List of error messages
        """
        # Clear old errors (older than 5 minutes)
        current_time = datetime.now()
        self.errors = [err for err in self.errors
                      if (current_time - datetime.fromisoformat(err['timestamp'])).total_seconds() < 300]

        # Return a copy of the errors list
        return self.errors.copy()

    async def _initialize_exchanges(self):
        """Initialize exchange clients and subscribe to market data."""
        self.logger.info("Initializing exchanges")

        # Get supported exchanges
        supported_exchanges = self.client_factory.get_supported_exchanges()
        self.logger.info(f"Supported exchanges: {supported_exchanges}")

        # Create clients for each exchange
        for exchange_id in supported_exchanges:
            try:
                client = self.client_factory.create_client(exchange_id)
                if client:
                    self.exchanges[exchange_id] = client
                    self.logger.info(f"Created client for {exchange_id}")

                    # Start the client with a timeout
                    try:
                        # Create a task with timeout
                        start_task = asyncio.create_task(client.start())
                        await asyncio.wait_for(start_task, timeout=10.0)  # 10 second timeout
                        self.logger.info(f"Started {exchange_id} WebSocket client")
                    except asyncio.TimeoutError:
                        self.logger.error(f"Timeout starting {exchange_id} WebSocket client")
                        self.add_error(exchange_id, f"Timeout starting WebSocket client")
                        # Keep the client in the list but mark it as not connected
                        client.connected = False
                    except Exception as e:
                        self.logger.error(f"Error starting {exchange_id} WebSocket client: {e}")
                        self.add_error(exchange_id, f"Error starting WebSocket client: {e}")
                        # Keep the client in the list but mark it as not connected
                        client.connected = False
            except Exception as e:
                self.logger.error(f"Error creating client for {exchange_id}: {e}")
                self.add_error(exchange_id, f"Error creating client: {e}")

        # Wait for clients to connect
        await asyncio.sleep(2)

        # Log connection status
        connected_exchanges = [exchange_id for exchange_id, client in self.exchanges.items() if client.connected]
        self.logger.info(f"Connected exchanges: {connected_exchanges}")

        # If no exchanges are connected, add an error
        if not connected_exchanges:
            error_msg = "No exchanges connected. Check your network connection and exchange endpoints."
            self.logger.error(error_msg)
            self.add_error("system", error_msg)

        # Get common symbols across exchanges
        await self._find_common_symbols()

        # Subscribe to order book data for common symbols
        await self._subscribe_to_market_data()

        self.logger.info("Exchanges initialized")

    async def _find_common_symbols(self):
        """Find common symbols across all exchanges."""
        self.logger.info("Finding common symbols across exchanges")

        exchange_symbols = {}

        # Get symbols for each exchange
        for exchange_id, client in self.exchanges.items():
            if not client.connected:
                self.logger.warning(f"Skipping {exchange_id} as it is not connected")
                continue

            try:
                symbols = await client.get_all_symbols()
                if symbols:
                    exchange_symbols[exchange_id] = set(symbols)
                    self.logger.info(f"Found {len(symbols)} symbols on {exchange_id}")
                else:
                    self.logger.warning(f"No symbols found on {exchange_id}")
                    self.add_error(exchange_id, "No symbols found")
            except Exception as e:
                self.logger.error(f"Error getting symbols from {exchange_id}: {e}")
                self.add_error(exchange_id, f"Error getting symbols: {e}")

        # Find common symbols across all exchanges
        if exchange_symbols:
            # If we have at least one exchange with symbols
            if len(exchange_symbols) >= 1:
                # Start with symbols from the first exchange
                common = set(next(iter(exchange_symbols.values())))

                # Intersect with symbols from other exchanges
                for symbols in exchange_symbols.values():
                    common &= symbols

                self.common_symbols = common
                self.logger.info(f"Found {len(self.common_symbols)} common symbols across all exchanges")

                # If no common symbols found, use a default set for testing
                if not self.common_symbols:
                    self.logger.warning("No common symbols found across exchanges, using default symbols")
                    self.common_symbols = {'BTC/USDT', 'ETH/USDT', 'BNB/USDT', 'SOL/USDT', 'XRP/USDT'}
                    self.add_error("system", "No common symbols found across exchanges, using default symbols")
            else:
                self.logger.warning("Only one exchange with symbols, using its symbols")
                self.common_symbols = next(iter(exchange_symbols.values()))
        else:
            self.logger.warning("No symbols found on any exchange, using default symbols")
            self.common_symbols = {'BTC/USDT', 'ETH/USDT', 'BNB/USDT', 'SOL/USDT', 'XRP/USDT'}
            self.add_error("system", "No symbols found on any exchange, using default symbols")

    async def _subscribe_to_market_data(self):
        """Subscribe to market data for common symbols."""
        self.logger.info("Subscribing to market data for common symbols")

        # Define the most common trading pairs that are likely to be available on all exchanges
        # These are typically the most liquid and widely traded pairs
        priority_symbols = [
            'BTC/USDT', 'ETH/USDT', 'BNB/USDT', 'SOL/USDT', 'XRP/USDT',
            'ADA/USDT', 'DOGE/USDT', 'AVAX/USDT', 'DOT/USDT', 'MATIC/USDT',
            'LINK/USDT', 'LTC/USDT', 'UNI/USDT', 'ATOM/USDT', 'ETC/USDT',
            'BCH/USDT', 'TRX/USDT', 'XLM/USDT', 'FIL/USDT', 'NEAR/USDT'
        ]

        # Filter priority symbols that are in our common symbols
        available_priority_symbols = [symbol for symbol in priority_symbols if symbol in self.common_symbols]

        # If we don't have enough priority symbols, add more from common symbols
        top_symbols = available_priority_symbols
        if len(top_symbols) < 10:
            # Add more symbols from common symbols until we have 10
            remaining_symbols = list(self.common_symbols - set(top_symbols))
            top_symbols.extend(remaining_symbols[:10 - len(top_symbols)])

        if not top_symbols:
            self.logger.warning("No common symbols to subscribe to")
            self.add_error("system", "No common symbols to subscribe to")
            return

        # Limit to 5 symbols for now to avoid WebSocket connection issues
        top_symbols = top_symbols[:5]

        self.logger.info(f"Subscribing to {len(top_symbols)} symbols: {top_symbols}")

        # Subscribe to order book data for each symbol on each exchange
        for exchange_id, client in self.exchanges.items():
            if not client.connected:
                self.logger.warning(f"Skipping subscription for {exchange_id} as it is not connected")
                continue

            for symbol in top_symbols:
                try:
                    # Create a task with timeout
                    subscribe_task = asyncio.create_task(client.subscribe_to_orderbook(symbol))
                    await asyncio.wait_for(subscribe_task, timeout=5.0)  # 5 second timeout
                except asyncio.TimeoutError:
                    self.logger.error(f"Timeout subscribing to {symbol} on {exchange_id}")
                    self.add_error(exchange_id, f"Timeout subscribing to {symbol}")
                except Exception as e:
                    self.logger.error(f"Error subscribing to {symbol} on {exchange_id}: {e}")
                    self.add_error(exchange_id, f"Error subscribing to {symbol}: {e}")

        # Log subscription status
        for exchange_id, client in self.exchanges.items():
            if client.connected:
                subscribed_symbols = client.subscribed_pairs
                self.logger.info(f"{exchange_id} subscribed to {len(subscribed_symbols)} symbols: {subscribed_symbols}")
            else:
                self.logger.warning(f"{exchange_id} is not connected, no subscriptions")

        self.logger.info("Subscribed to market data")

    async def _scan_loop(self):
        """Main loop for scanning arbitrage opportunities."""
        self.logger.info("Starting arbitrage scan loop")

        while self.running:
            try:
                # Scan for cross-exchange arbitrage opportunities
                await self._scan_cross_exchange_arbitrage()

                # Wait for the next scan
                await asyncio.sleep(self.scan_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                error_msg = f"Error in arbitrage scan loop: {e}"
                self.logger.error(error_msg)
                self.add_error("system", error_msg)
                await asyncio.sleep(self.scan_interval)

        self.logger.info("Arbitrage scan loop stopped")

    async def _scan_cross_exchange_arbitrage(self):
        """Scan for cross-exchange arbitrage opportunities."""
        # Get all exchange clients
        clients = self.client_factory.get_all_clients()

        if len(clients) < 2:
            self.logger.warning("Need at least 2 exchanges for cross-exchange arbitrage")
            self.add_error("system", "Need at least 2 exchanges for cross-exchange arbitrage")
            return

        # Filter to only connected clients
        connected_clients = {exchange_id: client for exchange_id, client in clients.items() if client.connected}

        if len(connected_clients) < 2:
            self.logger.warning("Need at least 2 connected exchanges for cross-exchange arbitrage")
            self.add_error("system", "Need at least 2 connected exchanges for cross-exchange arbitrage")
            return

        # Use only the top 5 symbols to avoid too many errors
        top_symbols = list(self.common_symbols)[:5] if self.common_symbols else []

        if not top_symbols:
            self.logger.warning("No common symbols to scan for arbitrage")
            self.add_error("system", "No common symbols to scan for arbitrage")
            return

        self.logger.debug(f"Scanning {len(top_symbols)} symbols for arbitrage opportunities")

        # Iterate through top symbols
        for symbol in top_symbols:
            try:
                # Get order book data for each exchange
                exchange_data = {}

                for exchange_id, client in connected_clients.items():
                    try:
                        # Get orderbook data synchronously to avoid await issues
                        orderbook = client.get_orderbook_data(symbol)
                        if orderbook and 'bid' in orderbook and 'ask' in orderbook:
                            exchange_data[exchange_id] = orderbook
                    except Exception as e:
                        error_msg = f"Error getting orderbook data for {symbol} from {exchange_id}: {e}"
                        self.logger.error(error_msg)
                        self.add_error(exchange_id, error_msg)

                # Find arbitrage opportunities
                if len(exchange_data) >= 2:
                    await self._find_arbitrage_opportunities(symbol, exchange_data)
                else:
                    self.logger.debug(f"Not enough exchanges with data for {symbol}")
            except Exception as e:
                error_msg = f"Error scanning arbitrage for {symbol}: {e}"
                self.logger.error(error_msg)
                self.add_error("system", error_msg)

    async def _find_arbitrage_opportunities(self, symbol, exchange_data):
        """
        Find arbitrage opportunities for a symbol across exchanges.

        Args:
            symbol (str): The trading pair symbol (e.g., 'BTC/USDT')
            exchange_data (dict): Order book data for each exchange
        """
        if len(exchange_data) < 2:
            return

        try:
            # Extract base and quote assets from the symbol
            base_asset, quote_asset = symbol.split('/')

            # Compare each pair of exchanges
            exchanges = list(exchange_data.keys())

            for i in range(len(exchanges)):
                for j in range(i + 1, len(exchanges)):
                    try:
                        exchange_a = exchanges[i]
                        exchange_b = exchanges[j]

                        data_a = exchange_data[exchange_a]
                        data_b = exchange_data[exchange_b]

                        # Check if we can buy on exchange A and sell on exchange B
                        if data_a['ask'] < data_b['bid']:
                            await self._calculate_arbitrage_profit(
                                symbol, base_asset, quote_asset,
                                exchange_a, exchange_b,
                                data_a['ask'], data_b['bid'],
                                data_a['ask_size'], data_b['bid_size']
                            )

                        # Check if we can buy on exchange B and sell on exchange A
                        if data_b['ask'] < data_a['bid']:
                            await self._calculate_arbitrage_profit(
                                symbol, base_asset, quote_asset,
                                exchange_b, exchange_a,
                                data_b['ask'], data_a['bid'],
                                data_b['ask_size'], data_a['bid_size']
                            )
                    except Exception as e:
                        error_msg = f"Error comparing {exchange_a} and {exchange_b} for {symbol}: {e}"
                        self.logger.error(error_msg)
                        self.add_error("system", error_msg)
        except Exception as e:
            error_msg = f"Error processing arbitrage for {symbol}: {e}"
            self.logger.error(error_msg)
            self.add_error("system", error_msg)

    async def _calculate_arbitrage_profit(self, symbol, base_asset, quote_asset,
                                         buy_exchange, sell_exchange,
                                         buy_price, sell_price,
                                         buy_size, sell_size):
        """
        Calculate potential profit for an arbitrage opportunity.

        Args:
            symbol (str): The trading pair symbol (e.g., 'BTC/USDT')
            base_asset (str): The base asset (e.g., 'BTC')
            quote_asset (str): The quote asset (e.g., 'USDT')
            buy_exchange (str): The exchange to buy on
            sell_exchange (str): The exchange to sell on
            buy_price (float): The price to buy at
            sell_price (float): The price to sell at
            buy_size (float): The available size to buy
            sell_size (float): The available size to sell
        """
        # Get taker fees for both exchanges
        buy_fee = self.config_loader.get_exchange_taker_fee(buy_exchange)
        sell_fee = self.config_loader.get_exchange_taker_fee(sell_exchange)

        # Find common networks for transferring the base asset
        common_networks = self.config_loader.find_common_networks(buy_exchange, sell_exchange, base_asset)

        if not common_networks:
            # self.logger.debug(f"No common networks found for {base_asset} between {buy_exchange} and {sell_exchange}")
            return

        # Find the network with the lowest withdrawal fee
        best_network = None
        lowest_fee = float('inf')

        for network in common_networks:
            withdrawal_fee = self.config_loader.get_withdrawal_fee(buy_exchange, base_asset, network)
            if withdrawal_fee < lowest_fee:
                lowest_fee = withdrawal_fee
                best_network = network

        if best_network is None:
            return

        # Calculate the maximum trade size (minimum of buy and sell sizes)
        max_trade_size = min(buy_size, sell_size)

        # Calculate the effective amount after buying (accounting for taker fee)
        buy_amount = max_trade_size
        buy_cost = buy_amount * buy_price * (1 + buy_fee)

        # Calculate the effective amount after withdrawal fee
        transfer_amount = buy_amount - lowest_fee

        if transfer_amount <= 0:
            return

        # Calculate the sell proceeds (accounting for taker fee)
        sell_proceeds = transfer_amount * sell_price * (1 - sell_fee)

        # Calculate profit in quote currency
        profit = sell_proceeds - buy_cost

        # Calculate profit percentage
        profit_percentage = profit / buy_cost

        # Check if profit exceeds threshold
        if profit_percentage > self.min_profit_threshold:
            # Create an arbitrage opportunity object
            opportunity = {
                'symbol': symbol,
                'buy_exchange': buy_exchange,
                'sell_exchange': sell_exchange,
                'buy_price': buy_price,
                'sell_price': sell_price,
                'size': max_trade_size,
                'base_asset': base_asset,
                'quote_asset': quote_asset,
                'network': best_network,
                'withdrawal_fee': lowest_fee,
                'profit': profit,
                'profit_percent': profit_percentage * 100,  # Convert to percentage
                'timestamp': datetime.now().isoformat()
            }

            # Add to opportunities list
            self.opportunities.append(opportunity)

            # Log the opportunity
            self.logger.info(f"Arbitrage opportunity found!")
            self.logger.info(f"Symbol: {symbol}")
            self.logger.info(f"Buy on {buy_exchange} at {buy_price:.8f}, Sell on {sell_exchange} at {sell_price:.8f}")
            self.logger.info(f"Size: {max_trade_size:.8f} {base_asset}")
            self.logger.info(f"Transfer network: {best_network}, Withdrawal fee: {lowest_fee:.8f} {base_asset}")
            self.logger.info(f"Profit: {profit:.8f} {quote_asset} ({profit_percentage:.2%})")
            self.logger.info(f"Timestamp: {datetime.now().isoformat()}")
            self.logger.info("-" * 80)
