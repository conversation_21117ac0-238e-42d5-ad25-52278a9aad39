"""
File untuk menjalankan program dengan output yang lebih jelas.
"""
import os
import sys
import time

def main():
    """
    Fungsi utama untuk menjalankan program.
    """
    print("Menjalankan program...")

    # Dapatkan direktori script
    script_dir = os.path.dirname(os.path.abspath(__file__))
    print(f"Direktori script: {script_dir}")

    # Cek apakah file main.py ada
    main_path = os.path.join(script_dir, "main.py")
    print(f"Path main.py: {main_path}")

    if os.path.exists(main_path):
        print("File main.py ditemukan!")

        # Jalankan program
        print("\nMemulai program...")
        print("=" * 50)

        # Gunakan os.system untuk menjalankan program
        # Ubah direktori kerja ke direktori script
        os.chdir(script_dir)
        os.system("python main.py")

        print("=" * 50)
        print("Program selesai.")
    else:
        print("File main.py tidak ditemukan!")

if __name__ == "__main__":
    main()
