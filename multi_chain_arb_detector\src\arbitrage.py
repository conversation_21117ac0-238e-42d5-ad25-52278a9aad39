"""
Modul untuk deteksi peluang arbitrase.
"""
import asyncio
import logging
import time
import random
from typing import Dict, Any, List, Optional, Union, Tuple, Set
from web3 import Web3

from .network import Network
from .dex import DEX
from .utils import (
    calculate_gas_cost_usd,
    display_arbitrage_opportunity,
    format_amount_with_decimals
)
from .optimizations import (
    BatchRequestManager,
    TokenPairCache,
    TokenFilter
)
from .token_lists import get_default_tokens, get_token_lists

logger = logging.getLogger("arbitrage_detector")

class ArbitrageDetector:
    """
    Kelas untuk mendeteksi peluang arbitrase di dalam satu jaringan.
    """

    def __init__(
        self,
        network: Network,
        config: Dict[str, Any],
        settings: Dict[str, Any]
    ):
        """
        Inisialisasi objek ArbitrageDetector.

        Args:
            network: Objek Network
            config: Konfigurasi jaringan
            settings: Pengaturan global
        """
        self.network = network
        self.config = config
        self.settings = settings

        # Inisialisasi DEX
        self.dexs = {}
        for dex_name in network.dexs:
            self.dexs[dex_name] = DEX(dex_name, network)

        # Inisialisasi objek optimasi
        self.batch_manager = BatchRequestManager(
            max_batch_size=settings.get('max_batch_size', 5),
            cooldown_seconds=settings.get('batch_cooldown_seconds', 0.5)
        )
        self.token_pair_cache = TokenPairCache(
            ttl_seconds=settings.get('cache_ttl_seconds', 300)
        )
        self.token_filter = TokenFilter()

        # Inisialisasi daftar token yang sudah diproses
        self.processed_token_pairs = set()
        self.successful_token_pairs = set()
        self.failed_token_pairs = set()

        # Inisialisasi daftar peluang arbitrase yang ditemukan
        self.arbitrage_opportunities = []

        logger.info(f"ArbitrageDetector diinisialisasi untuk jaringan {network.name} dengan {len(self.dexs)} DEX")

    async def detect_arbitrage_opportunities(self) -> None:
        """
        Mendeteksi peluang arbitrase di dalam jaringan.
        """
        logger.info(f"Memulai deteksi arbitrase di jaringan {self.network.name}")

        # Dapatkan daftar token dari konfigurasi
        config_tokens = self.network.tokens

        # Coba dapatkan daftar token yang lebih besar
        try:
            # Gunakan daftar token default sebagai fallback
            extended_tokens = get_default_tokens().get(self.network.name, {})

            # Coba dapatkan daftar token yang lebih besar dari API
            if self.settings.get('use_extended_token_list', True):
                logger.info(f"Mencoba mendapatkan daftar token yang lebih besar untuk jaringan {self.network.name}")
                token_lists = await get_token_lists()
                if self.network.name in token_lists and token_lists[self.network.name]:
                    extended_tokens = token_lists[self.network.name]
                    logger.info(f"Berhasil mendapatkan {len(extended_tokens)} token untuk jaringan {self.network.name}")

            # Gabungkan dengan daftar token dari konfigurasi
            tokens = {**config_tokens, **extended_tokens}

            # Batasi jumlah token jika terlalu banyak
            max_tokens = self.settings.get('max_tokens_per_network', 500)
            if len(tokens) > max_tokens:
                logger.info(f"Membatasi jumlah token dari {len(tokens)} menjadi {max_tokens}")
                # Prioritaskan token dari konfigurasi
                tokens_items = list(config_tokens.items()) + list({k: v for k, v in extended_tokens.items() if k not in config_tokens}.items())
                tokens = dict(tokens_items[:max_tokens])
        except Exception as e:
            logger.warning(f"Gagal mendapatkan daftar token yang lebih besar: {e}. Menggunakan daftar token dari konfigurasi.")
            tokens = config_tokens

        # Dapatkan daftar DEX
        dex_names = list(self.dexs.keys())

        # Dapatkan harga token native dalam USD
        native_price_usd = await self.network.get_native_token_price_usd()

        # Iterasi melalui semua pasangan token
        token_addresses = list(tokens.values())
        token_symbols = list(tokens.keys())

        logger.info(f"Menganalisis {len(token_addresses)} token di {len(dex_names)} DEX pada jaringan {self.network.name}")

        # Dapatkan informasi desimal untuk semua token secara batch
        token_decimals = {}
        token_info_tasks = []

        # Bagi token menjadi batch untuk menghindari rate limiting
        batch_size = self.settings.get('token_info_batch_size', 10)
        for i in range(0, len(token_addresses), batch_size):
            batch_tokens = token_addresses[i:i+batch_size]
            for address in batch_tokens:
                # Lewati token yang sudah diketahui bermasalah
                if address in self.network.problematic_tokens:
                    continue

                # Coba dapatkan informasi token
                token_info_tasks.append((address, self.network.get_token_info(address)))

            # Jalankan batch
            if token_info_tasks:
                try:
                    # Jalankan batch dengan jeda untuk menghindari rate limiting
                    for address, task in token_info_tasks:
                        try:
                            _, _, decimals = await task
                            token_decimals[address] = decimals
                        except Exception as e:
                            logger.debug(f"Gagal mendapatkan informasi untuk token {address}: {e}")
                            token_decimals[address] = 18  # Default ke 18 desimal

                    # Jeda sebentar antara batch
                    await asyncio.sleep(0.5)
                except Exception as e:
                    logger.error(f"Error saat memproses batch token: {e}")

                # Reset daftar tugas
                token_info_tasks = []

        # Filter token stablecoin
        if self.settings.get('filter_stablecoins', True):
            filtered_tokens = {}
            filtered_addresses = []
            filtered_symbols = []

            for i, (symbol, address) in enumerate(zip(token_symbols, token_addresses)):
                if not self.token_filter.is_stablecoin(symbol):
                    filtered_tokens[symbol] = address
                    filtered_addresses.append(address)
                    filtered_symbols.append(symbol)

            token_addresses = filtered_addresses
            token_symbols = filtered_symbols
            logger.info(f"Setelah filter stablecoin: {len(token_addresses)} token")

        # Deteksi arbitrase 2-DEX (A -> B -> A)
        await self._detect_two_dex_arbitrage(
            token_addresses,
            token_symbols,
            token_decimals,
            dex_names,
            native_price_usd
        )

        # Deteksi arbitrase 3-DEX (A -> B -> C -> A)
        if self.settings.get('enable_three_dex_arbitrage', True):
            await self._detect_three_dex_arbitrage(
                token_addresses,
                token_symbols,
                token_decimals,
                dex_names,
                native_price_usd
            )

    async def _detect_two_dex_arbitrage(
        self,
        token_addresses: List[str],
        token_symbols: List[str],
        token_decimals: Dict[str, int],
        dex_names: List[str],
        native_price_usd: float
    ) -> None:
        """
        Mendeteksi peluang arbitrase 2-DEX (A -> B -> A).

        Args:
            token_addresses: Daftar alamat token
            token_symbols: Daftar simbol token
            token_decimals: Dictionary desimal token
            dex_names: Daftar nama DEX
            native_price_usd: Harga token native dalam USD
        """
        logger.info(f"Mendeteksi arbitrase 2-DEX di jaringan {self.network.name}")

        # Dapatkan token stablecoin untuk perhitungan USD
        usdc_address = None
        for symbol, address in self.network.tokens.items():
            if symbol in ['USDC', 'USDT']:
                usdc_address = address
                break

        if not usdc_address:
            logger.warning(f"Tidak dapat menemukan token USDC atau USDT di jaringan {self.network.name}. Perhitungan profit USD mungkin tidak akurat.")

        # Dapatkan gas price sekali saja
        gas_price = await self.network.get_gas_price()

        # Buat daftar pasangan token yang akan dianalisis
        token_pairs = []
        for i, token_a_address in enumerate(token_addresses):
            token_a_symbol = token_symbols[i]

            # Lewati token yang bermasalah
            if token_a_address in self.network.problematic_tokens:
                continue

            for j, token_b_address in enumerate(token_addresses):
                if i == j:
                    continue  # Lewati jika token sama

                token_b_symbol = token_symbols[j]

                # Lewati token yang bermasalah
                if token_b_address in self.network.problematic_tokens:
                    continue

                # Lewati pasangan stablecoin-stablecoin
                if self.token_filter.is_stablecoin(token_a_symbol) and self.token_filter.is_stablecoin(token_b_symbol):
                    continue

                # Buat kunci pasangan token
                pair_key = f"{token_a_address}:{token_b_address}"

                # Lewati pasangan yang sudah diproses
                if pair_key in self.processed_token_pairs:
                    continue

                # Lewati pasangan yang sudah gagal
                if pair_key in self.failed_token_pairs:
                    continue

                # Tambahkan ke daftar pasangan yang akan dianalisis
                token_pairs.append((i, j, token_a_address, token_b_address, token_a_symbol, token_b_symbol))

                # Tandai pasangan sebagai sudah diproses
                self.processed_token_pairs.add(pair_key)

        # Acak urutan pasangan token untuk meningkatkan peluang menemukan arbitrase
        random.shuffle(token_pairs)

        # Batasi jumlah pasangan yang dianalisis jika terlalu banyak
        max_pairs = self.settings.get('max_token_pairs', 10000)
        if len(token_pairs) > max_pairs:
            logger.info(f"Membatasi jumlah pasangan token dari {len(token_pairs)} menjadi {max_pairs}")
            token_pairs = token_pairs[:max_pairs]

        logger.info(f"Menganalisis {len(token_pairs)} pasangan token di {len(dex_names)} DEX pada jaringan {self.network.name}")

        # Bagi pasangan token menjadi batch
        batch_size = self.settings.get('token_pair_batch_size', 50)
        for batch_idx in range(0, len(token_pairs), batch_size):
            batch = token_pairs[batch_idx:batch_idx+batch_size]

            # Proses batch
            for i, j, token_a_address, token_b_address, token_a_symbol, token_b_symbol in batch:
                # Dapatkan desimal token
                token_a_decimals = token_decimals.get(token_a_address, 18)
                token_b_decimals = token_decimals.get(token_b_address, 18)

                # Jumlah token awal (dalam satuan terkecil)
                # Gunakan 1 unit token untuk kesederhanaan
                amount_in = 10 ** token_a_decimals

                # Iterasi melalui semua pasangan DEX
                for dex1_name in dex_names:
                    # Lewati DEX yang bermasalah
                    if dex1_name in self.network.problematic_dexs:
                        continue

                    for dex2_name in dex_names:
                        # Lewati jika DEX sama atau bermasalah
                        if dex1_name == dex2_name or dex2_name in self.network.problematic_dexs:
                            continue

                        # Buat kunci untuk cache
                        cache_key = f"{self.network.name}:{dex1_name}:{dex2_name}:{token_a_address}:{token_b_address}"

                        # Cek apakah hasil sudah ada di cache
                        cached_result = self.token_pair_cache.get(
                            self.network.name,
                            f"{dex1_name}:{dex2_name}",
                            token_a_address,
                            token_b_address
                        )

                        if cached_result:
                            # Gunakan hasil dari cache
                            profit_percentage, net_profit_usd = cached_result

                            # Jika profit bersih positif dan di atas ambang batas
                            if profit_percentage > 0 and net_profit_usd > self.network.min_profit_usd:
                                logger.info(
                                    f"Peluang arbitrase ditemukan di {self.network.name} (dari cache): "
                                    f"{token_a_symbol} -> {token_b_symbol} -> {token_a_symbol} "
                                    f"menggunakan {dex1_name} dan {dex2_name}. "
                                    f"Profit: ${net_profit_usd:.2f} ({profit_percentage:.2f}%)"
                                )

                            # Lanjut ke pasangan berikutnya
                            continue

                        # Dapatkan objek DEX
                        dex1 = self.dexs[dex1_name]
                        dex2 = self.dexs[dex2_name]

                        try:
                            # Langkah 1: A -> B di DEX 1
                            amounts_1 = await dex1.get_amounts_out(
                                amount_in,
                                [token_a_address, token_b_address],
                                self.settings['slippage_percentage']
                            )
                            amount_b = amounts_1[1]

                            # Langkah 2: B -> A di DEX 2
                            amounts_2 = await dex2.get_amounts_out(
                                amount_b,
                                [token_b_address, token_a_address],
                                self.settings['slippage_percentage']
                            )
                            amount_out = amounts_2[1]

                            # Hitung profit
                            profit = amount_out - amount_in
                            profit_percentage = (profit / amount_in) * 100

                            # Jika ada profit, periksa apakah ini peluang arbitrase yang valid
                            if profit > 0 and profit_percentage > 0:
                                # Periksa apakah perbedaan harga tidak terlalu besar
                                price_diff_percentage = abs(profit_percentage)
                                if price_diff_percentage > self.settings['max_price_difference_percentage']:
                                    logger.warning(
                                        f"Perbedaan harga terlalu besar ({price_diff_percentage:.2f}%) "
                                        f"untuk {token_a_symbol}-{token_b_symbol} di {dex1_name}-{dex2_name}. "
                                        f"Kemungkinan bukan peluang arbitrase yang valid."
                                    )
                                    continue

                                # Hitung biaya gas
                                gas_estimate_1 = await dex1.estimate_swap_gas(
                                    token_a_address,
                                    token_b_address,
                                    amount_in
                                )
                                gas_estimate_2 = await dex2.estimate_swap_gas(
                                    token_b_address,
                                    token_a_address,
                                    amount_b
                                )

                                total_gas_used = gas_estimate_1 + gas_estimate_2

                                gas_cost_usd = calculate_gas_cost_usd(
                                    total_gas_used,
                                    gas_price,
                                    native_price_usd
                                )

                                # Hitung profit dalam USD
                                if usdc_address:
                                    try:
                                        # Gunakan DEX pertama untuk mendapatkan harga
                                        amounts_usd = await dex1.get_amounts_out(
                                            amount_in,
                                            [token_a_address, usdc_address]
                                        )
                                        token_a_price_usd = amounts_usd[1] / (10 ** token_decimals.get(usdc_address, 6))

                                        # Hitung profit dalam USD
                                        profit_usd = (profit / (10 ** token_a_decimals)) * token_a_price_usd

                                        # Hitung profit bersih
                                        net_profit_usd = profit_usd - gas_cost_usd

                                        # Simpan hasil ke cache
                                        self.token_pair_cache.set(
                                            self.network.name,
                                            f"{dex1_name}:{dex2_name}",
                                            token_a_address,
                                            token_b_address,
                                            (profit_percentage, net_profit_usd)
                                        )

                                        # Jika profit bersih positif dan di atas ambang batas
                                        if net_profit_usd > self.network.min_profit_usd:
                                            # Tambahkan ke daftar peluang arbitrase
                                            self.arbitrage_opportunities.append({
                                                'network': self.network.name,
                                                'token_path': [token_a_symbol, token_b_symbol, token_a_symbol],
                                                'dex_path': [dex1_name, dex2_name],
                                                'amounts': [amount_in, amount_b, amount_out],
                                                'token_symbols': [token_a_symbol, token_b_symbol, token_a_symbol],
                                                'token_decimals': [token_a_decimals, token_b_decimals, token_a_decimals],
                                                'profit_usd': profit_usd,
                                                'gas_cost_usd': gas_cost_usd,
                                                'net_profit_usd': net_profit_usd,
                                                'profit_percentage': profit_percentage
                                            })

                                            # Tandai pasangan sebagai berhasil
                                            self.successful_token_pairs.add(f"{token_a_address}:{token_b_address}")

                                            logger.info(
                                                f"Peluang arbitrase ditemukan di {self.network.name}: "
                                                f"{token_a_symbol} -> {token_b_symbol} -> {token_a_symbol} "
                                                f"menggunakan {dex1_name} dan {dex2_name}. "
                                                f"Profit: ${net_profit_usd:.2f} ({profit_percentage:.2f}%)"
                                            )

                                            # Tampilkan peluang arbitrase
                                            display_arbitrage_opportunity(
                                                self.network.name,
                                                [token_a_symbol, token_b_symbol, token_a_symbol],
                                                [dex1_name, dex2_name],
                                                [amount_in, amount_b, amount_out],
                                                [token_a_symbol, token_b_symbol, token_a_symbol],
                                                [token_a_decimals, token_b_decimals, token_a_decimals],
                                                profit_usd,
                                                gas_cost_usd,
                                                net_profit_usd,
                                                profit_percentage
                                            )
                                    except Exception as e:
                                        logger.debug(f"Error saat menghitung profit USD: {e}")
                            else:
                                # Simpan hasil negatif ke cache
                                self.token_pair_cache.set(
                                    self.network.name,
                                    f"{dex1_name}:{dex2_name}",
                                    token_a_address,
                                    token_b_address,
                                    (profit_percentage, 0)
                                )
                        except Exception as e:
                            # Tandai pasangan sebagai gagal
                            self.failed_token_pairs.add(f"{token_a_address}:{token_b_address}")

                            # Lewati jika ada error (misalnya, likuiditas tidak cukup)
                            logger.debug(
                                f"Error saat memeriksa arbitrase untuk {token_a_symbol}-{token_b_symbol} "
                                f"di {dex1_name}-{dex2_name}: {e}"
                            )

            # Jeda sebentar antara batch untuk menghindari rate limiting
            await asyncio.sleep(0.5)

    async def _detect_three_dex_arbitrage(
        self,
        token_addresses: List[str],
        token_symbols: List[str],
        token_decimals: Dict[str, int],
        dex_names: List[str],
        native_price_usd: float
    ) -> None:
        """
        Mendeteksi peluang arbitrase 3-DEX (A -> B -> C -> A).

        Args:
            token_addresses: Daftar alamat token
            token_symbols: Daftar simbol token
            token_decimals: Dictionary desimal token
            dex_names: Daftar nama DEX
            native_price_usd: Harga token native dalam USD
        """
        logger.info(f"Mendeteksi arbitrase 3-DEX di jaringan {self.network.name}")

        # Dapatkan token stablecoin untuk perhitungan USD
        usdc_address = None
        for symbol, address in self.network.tokens.items():
            if symbol in ['USDC', 'USDT']:
                usdc_address = address
                break

        if not usdc_address:
            logger.warning(f"Tidak dapat menemukan token USDC atau USDT di jaringan {self.network.name}. Perhitungan profit USD mungkin tidak akurat.")

        # Dapatkan gas price sekali saja
        gas_price = await self.network.get_gas_price()

        # Buat daftar triplet token yang akan dianalisis
        token_triplets = []

        # Batasi jumlah token untuk triplet (3-DEX memerlukan lebih banyak komputasi)
        max_tokens_for_triplet = self.settings.get('max_tokens_for_triplet', 20)
        if len(token_addresses) > max_tokens_for_triplet:
            logger.info(f"Membatasi jumlah token untuk triplet dari {len(token_addresses)} menjadi {max_tokens_for_triplet}")
            # Prioritaskan token dengan likuiditas tinggi (biasanya token populer di awal daftar)
            limited_token_addresses = token_addresses[:max_tokens_for_triplet]
            limited_token_symbols = token_symbols[:max_tokens_for_triplet]
        else:
            limited_token_addresses = token_addresses
            limited_token_symbols = token_symbols

        # Buat daftar triplet token
        for i, token_a_address in enumerate(limited_token_addresses):
            token_a_symbol = limited_token_symbols[i]

            # Lewati token yang bermasalah
            if token_a_address in self.network.problematic_tokens:
                continue

            for j, token_b_address in enumerate(limited_token_addresses):
                if i == j:
                    continue  # Lewati jika token sama

                token_b_symbol = limited_token_symbols[j]

                # Lewati token yang bermasalah
                if token_b_address in self.network.problematic_tokens:
                    continue

                # Lewati pasangan stablecoin-stablecoin
                if self.token_filter.is_stablecoin(token_a_symbol) and self.token_filter.is_stablecoin(token_b_symbol):
                    continue

                for k, token_c_address in enumerate(limited_token_addresses):
                    if i == k or j == k:
                        continue  # Lewati jika token sama

                    token_c_symbol = limited_token_symbols[k]

                    # Lewati token yang bermasalah
                    if token_c_address in self.network.problematic_tokens:
                        continue

                    # Lewati triplet dengan lebih dari satu stablecoin
                    stablecoin_count = 0
                    if self.token_filter.is_stablecoin(token_a_symbol):
                        stablecoin_count += 1
                    if self.token_filter.is_stablecoin(token_b_symbol):
                        stablecoin_count += 1
                    if self.token_filter.is_stablecoin(token_c_symbol):
                        stablecoin_count += 1

                    if stablecoin_count > 1:
                        continue

                    # Buat kunci triplet token
                    triplet_key = f"{token_a_address}:{token_b_address}:{token_c_address}"

                    # Lewati triplet yang sudah diproses
                    if triplet_key in self.processed_token_pairs:
                        continue

                    # Lewati triplet yang sudah gagal
                    if triplet_key in self.failed_token_pairs:
                        continue

                    # Tambahkan ke daftar triplet yang akan dianalisis
                    token_triplets.append((i, j, k, token_a_address, token_b_address, token_c_address, token_a_symbol, token_b_symbol, token_c_symbol))

                    # Tandai triplet sebagai sudah diproses
                    self.processed_token_pairs.add(triplet_key)

        # Acak urutan triplet token untuk meningkatkan peluang menemukan arbitrase
        random.shuffle(token_triplets)

        # Batasi jumlah triplet yang dianalisis jika terlalu banyak
        max_triplets = self.settings.get('max_token_triplets', 1000)
        if len(token_triplets) > max_triplets:
            logger.info(f"Membatasi jumlah triplet token dari {len(token_triplets)} menjadi {max_triplets}")
            token_triplets = token_triplets[:max_triplets]

        logger.info(f"Menganalisis {len(token_triplets)} triplet token di {len(dex_names)} DEX pada jaringan {self.network.name}")

        # Bagi triplet token menjadi batch
        batch_size = self.settings.get('token_triplet_batch_size', 10)
        for batch_idx in range(0, len(token_triplets), batch_size):
            batch = token_triplets[batch_idx:batch_idx+batch_size]

            # Proses batch
            for i, j, k, token_a_address, token_b_address, token_c_address, token_a_symbol, token_b_symbol, token_c_symbol in batch:
                # Dapatkan desimal token
                token_a_decimals = token_decimals.get(token_a_address, 18)
                token_b_decimals = token_decimals.get(token_b_address, 18)
                token_c_decimals = token_decimals.get(token_c_address, 18)

                # Jumlah token awal (dalam satuan terkecil)
                # Gunakan 1 unit token untuk kesederhanaan
                amount_in = 10 ** token_a_decimals

                # Iterasi melalui semua triplet DEX
                for dex1_name in dex_names:
                    # Lewati DEX yang bermasalah
                    if dex1_name in self.network.problematic_dexs:
                        continue

                    for dex2_name in dex_names:
                        # Lewati jika DEX sama atau bermasalah
                        if dex1_name == dex2_name or dex2_name in self.network.problematic_dexs:
                            continue

                        for dex3_name in dex_names:
                            # Lewati jika DEX sama atau bermasalah
                            if dex1_name == dex3_name or dex2_name == dex3_name or dex3_name in self.network.problematic_dexs:
                                continue

                            # Buat kunci untuk cache
                            cache_key = f"{self.network.name}:{dex1_name}:{dex2_name}:{dex3_name}:{token_a_address}:{token_b_address}:{token_c_address}"

                            # Cek apakah hasil sudah ada di cache
                            cached_result = self.token_pair_cache.get(
                                self.network.name,
                                f"{dex1_name}:{dex2_name}:{dex3_name}",
                                f"{token_a_address}:{token_b_address}:{token_c_address}",
                                ""
                            )

                            if cached_result:
                                # Gunakan hasil dari cache
                                profit_percentage, net_profit_usd = cached_result

                                # Jika profit bersih positif dan di atas ambang batas
                                if profit_percentage > 0 and net_profit_usd > self.network.min_profit_usd:
                                    logger.info(
                                        f"Peluang arbitrase ditemukan di {self.network.name} (dari cache): "
                                        f"{token_a_symbol} -> {token_b_symbol} -> {token_c_symbol} -> {token_a_symbol} "
                                        f"menggunakan {dex1_name}, {dex2_name}, dan {dex3_name}. "
                                        f"Profit: ${net_profit_usd:.2f} ({profit_percentage:.2f}%)"
                                    )

                                # Lanjut ke triplet berikutnya
                                continue

                            # Dapatkan objek DEX
                            dex1 = self.dexs[dex1_name]
                            dex2 = self.dexs[dex2_name]
                            dex3 = self.dexs[dex3_name]

                            try:
                                # Langkah 1: A -> B di DEX 1
                                amounts_1 = await dex1.get_amounts_out(
                                    amount_in,
                                    [token_a_address, token_b_address],
                                    self.settings['slippage_percentage']
                                )
                                amount_b = amounts_1[1]

                                # Langkah 2: B -> C di DEX 2
                                amounts_2 = await dex2.get_amounts_out(
                                    amount_b,
                                    [token_b_address, token_c_address],
                                    self.settings['slippage_percentage']
                                )
                                amount_c = amounts_2[1]

                                # Langkah 3: C -> A di DEX 3
                                amounts_3 = await dex3.get_amounts_out(
                                    amount_c,
                                    [token_c_address, token_a_address],
                                    self.settings['slippage_percentage']
                                )
                                amount_out = amounts_3[1]

                                # Hitung profit
                                profit = amount_out - amount_in
                                profit_percentage = (profit / amount_in) * 100

                                # Jika ada profit, periksa apakah ini peluang arbitrase yang valid
                                if profit > 0 and profit_percentage > 0:
                                    # Periksa apakah perbedaan harga tidak terlalu besar
                                    price_diff_percentage = abs(profit_percentage)
                                    if price_diff_percentage > self.settings['max_price_difference_percentage']:
                                        logger.warning(
                                            f"Perbedaan harga terlalu besar ({price_diff_percentage:.2f}%) "
                                            f"untuk {token_a_symbol}-{token_b_symbol}-{token_c_symbol} "
                                            f"di {dex1_name}-{dex2_name}-{dex3_name}. "
                                            f"Kemungkinan bukan peluang arbitrase yang valid."
                                        )

                                        # Simpan hasil negatif ke cache
                                        self.token_pair_cache.set(
                                            self.network.name,
                                            f"{dex1_name}:{dex2_name}:{dex3_name}",
                                            f"{token_a_address}:{token_b_address}:{token_c_address}",
                                            "",
                                            (profit_percentage, 0)
                                        )

                                        continue

                                    # Hitung biaya gas
                                    gas_estimate_1 = await dex1.estimate_swap_gas(
                                        token_a_address,
                                        token_b_address,
                                        amount_in
                                    )
                                    gas_estimate_2 = await dex2.estimate_swap_gas(
                                        token_b_address,
                                        token_c_address,
                                        amount_b
                                    )
                                    gas_estimate_3 = await dex3.estimate_swap_gas(
                                        token_c_address,
                                        token_a_address,
                                        amount_c
                                    )

                                    total_gas_used = gas_estimate_1 + gas_estimate_2 + gas_estimate_3

                                    gas_cost_usd = calculate_gas_cost_usd(
                                        total_gas_used,
                                        gas_price,
                                        native_price_usd
                                    )

                                    # Hitung profit dalam USD
                                    if usdc_address:
                                        try:
                                            # Gunakan DEX pertama untuk mendapatkan harga
                                            amounts_usd = await dex1.get_amounts_out(
                                                amount_in,
                                                [token_a_address, usdc_address]
                                            )
                                            token_a_price_usd = amounts_usd[1] / (10 ** token_decimals.get(usdc_address, 6))

                                            # Hitung profit dalam USD
                                            profit_usd = (profit / (10 ** token_a_decimals)) * token_a_price_usd

                                            # Hitung profit bersih
                                            net_profit_usd = profit_usd - gas_cost_usd

                                            # Simpan hasil ke cache
                                            self.token_pair_cache.set(
                                                self.network.name,
                                                f"{dex1_name}:{dex2_name}:{dex3_name}",
                                                f"{token_a_address}:{token_b_address}:{token_c_address}",
                                                "",
                                                (profit_percentage, net_profit_usd)
                                            )

                                            # Jika profit bersih positif dan di atas ambang batas
                                            if net_profit_usd > self.network.min_profit_usd:
                                                # Tambahkan ke daftar peluang arbitrase
                                                self.arbitrage_opportunities.append({
                                                    'network': self.network.name,
                                                    'token_path': [token_a_symbol, token_b_symbol, token_c_symbol, token_a_symbol],
                                                    'dex_path': [dex1_name, dex2_name, dex3_name],
                                                    'amounts': [amount_in, amount_b, amount_c, amount_out],
                                                    'token_symbols': [token_a_symbol, token_b_symbol, token_c_symbol, token_a_symbol],
                                                    'token_decimals': [token_a_decimals, token_b_decimals, token_c_decimals, token_a_decimals],
                                                    'profit_usd': profit_usd,
                                                    'gas_cost_usd': gas_cost_usd,
                                                    'net_profit_usd': net_profit_usd,
                                                    'profit_percentage': profit_percentage
                                                })

                                                # Tandai triplet sebagai berhasil
                                                self.successful_token_pairs.add(f"{token_a_address}:{token_b_address}:{token_c_address}")

                                                logger.info(
                                                    f"Peluang arbitrase ditemukan di {self.network.name}: "
                                                    f"{token_a_symbol} -> {token_b_symbol} -> {token_c_symbol} -> {token_a_symbol} "
                                                    f"menggunakan {dex1_name}, {dex2_name}, dan {dex3_name}. "
                                                    f"Profit: ${net_profit_usd:.2f} ({profit_percentage:.2f}%)"
                                                )

                                                # Tampilkan peluang arbitrase
                                                display_arbitrage_opportunity(
                                                    self.network.name,
                                                    [token_a_symbol, token_b_symbol, token_c_symbol, token_a_symbol],
                                                    [dex1_name, dex2_name, dex3_name],
                                                    [amount_in, amount_b, amount_c, amount_out],
                                                    [token_a_symbol, token_b_symbol, token_c_symbol, token_a_symbol],
                                                    [token_a_decimals, token_b_decimals, token_c_decimals, token_a_decimals],
                                                    profit_usd,
                                                    gas_cost_usd,
                                                    net_profit_usd,
                                                    profit_percentage
                                                )
                                        except Exception as e:
                                            logger.debug(f"Error saat menghitung profit USD: {e}")
                                else:
                                    # Simpan hasil negatif ke cache
                                    self.token_pair_cache.set(
                                        self.network.name,
                                        f"{dex1_name}:{dex2_name}:{dex3_name}",
                                        f"{token_a_address}:{token_b_address}:{token_c_address}",
                                        "",
                                        (profit_percentage, 0)
                                    )
                            except Exception as e:
                                # Tandai triplet sebagai gagal
                                self.failed_token_pairs.add(f"{token_a_address}:{token_b_address}:{token_c_address}")

                                # Lewati jika ada error (misalnya, likuiditas tidak cukup)
                                logger.debug(
                                    f"Error saat memeriksa arbitrase untuk {token_a_symbol}-{token_b_symbol}-{token_c_symbol} "
                                    f"di {dex1_name}-{dex2_name}-{dex3_name}: {e}"
                                )

            # Jeda sebentar antara batch untuk menghindari rate limiting
            await asyncio.sleep(1.0)
