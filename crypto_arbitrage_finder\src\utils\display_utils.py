import os
import platform
from datetime import datetime
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.layout import Layout
from rich.live import Live
from rich.text import Text

# Fungsi untuk membersihkan layar
def clear_screen():
    """Membersihkan layar terminal."""
    if platform.system() == "Windows":
        os.system('cls')
    else:
        os.system('clear')

# Fungsi untuk membuat layout tampilan
def create_layout():
    """Membuat layout untuk tampilan aplikasi."""
    layout = Layout(name="root")

    # Bagi layout menjadi beberapa bagian
    layout.split(
        Layout(name="header", size=3),
        Layout(name="main", ratio=1),
        Layout(name="footer", size=1)
    )

    # Bagi bagian main menjadi beberapa kolom
    layout["main"].split_row(
        Layout(name="market_data", ratio=1),
        Layout(name="right_panel", ratio=1)
    )

    # Bagi bagian right_panel menjadi top opportunities dan errors
    layout["right_panel"].split(
        Layout(name="top_opportunities", ratio=1),
        Layout(name="errors", ratio=1)
    )

    return layout

# Fungsi untuk membuat header
def create_header(version="1.0.0"):
    """Membuat header aplikasi."""
    return Panel.fit(
        "[bold green]Cryptocurrency Arbitrage Finder[/bold green]\n"
        "[italic]Real-time arbitrage opportunities across 10 exchanges[/italic]",
        border_style="green",
        title="Welcome",
        subtitle=f"v{version}"
    )

# Fungsi untuk membuat tabel data pasar
def create_market_table(market_data):
    """Membuat tabel untuk data pasar."""
    table = Table(title="Data Pasar Terkini", show_header=True, header_style="bold cyan")
    table.add_column("Bursa", style="cyan", justify="center")
    table.add_column("Simbol", style="yellow", justify="center")
    table.add_column("Bid", style="green", justify="right")
    table.add_column("Ask", style="red", justify="right")
    table.add_column("Spread %", style="magenta", justify="center")
    table.add_column("Diperbarui", style="blue", justify="center")

    # Tambahkan data ke tabel
    for data in market_data:
        table.add_row(
            data['exchange'],
            data['symbol'],
            f"{data['bid']:.8f}",
            f"{data['ask']:.8f}",
            f"{data['spread']}%",
            str(data['updated'])
        )

    return table

# Fungsi untuk membuat tabel peluang arbitrase
def create_opportunities_table(opportunities, title="Peluang Arbitrase", max_rows=None):
    """Membuat tabel untuk peluang arbitrase."""
    table = Table(title=title, show_header=True, header_style="bold green")
    table.add_column("Simbol", style="yellow", justify="center")
    table.add_column("Beli di", style="cyan", justify="center")
    table.add_column("Harga Beli", style="green", justify="right")
    table.add_column("Jual di", style="cyan", justify="center")
    table.add_column("Harga Jual", style="red", justify="right")
    table.add_column("Profit %", style="magenta", justify="center")

    # Batasi jumlah baris jika diperlukan
    display_opps = opportunities
    if max_rows and len(opportunities) > max_rows:
        display_opps = opportunities[:max_rows]

    # Tambahkan data ke tabel
    for opp in display_opps:
        table.add_row(
            opp['symbol'],
            opp['buy_exchange'],
            f"{opp['buy_price']:.8f}",
            opp['sell_exchange'],
            f"{opp['sell_price']:.8f}",
            f"{opp['profit_percent']:.4f}%"
        )

    return table

# Fungsi untuk membuat tabel error
def create_error_table(errors):
    """Membuat tabel untuk pesan error."""
    table = Table(title="Pesan Error", show_header=True, header_style="bold red")
    table.add_column("Waktu", style="blue", justify="center")
    table.add_column("Bursa", style="cyan", justify="center")
    table.add_column("Pesan", style="red", justify="left")

    # Tambahkan data ke tabel
    for error in errors:
        table.add_row(
            error['time'],
            error['exchange'],
            error['message']
        )

    return table

# Fungsi untuk membuat footer
def create_footer(status="Running", last_update=None):
    """Membuat footer aplikasi."""
    if last_update is None:
        last_update = datetime.now().strftime("%H:%M:%S")

    return Text(f"Status: {status} | Terakhir diperbarui: {last_update} | Tekan Ctrl+C untuk keluar", style="bold")
