"""
Arbitrage detector module for Jupiter DEX Arbitrage Analyzer
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Any

import aiohttp

import config
import utils
from jupiter_api import JupiterAPI
from token_manager import TokenManager

logger = logging.getLogger("jupiter_arbitrage")

class ArbitrageDetector:
    """Detects arbitrage opportunities on Jupiter DEX"""

    def __init__(self, session: aiohttp.ClientSession):
        self.session = session
        self.jupiter_api = JupiterAPI(session)
        self.token_manager = TokenManager(session)
        self.opportunities: List[Dict[str, Any]] = []
        self.analyzed_tokens: List[Dict[str, Any]] = []

    async def initialize(self) -> None:
        """Initialize the arbitrage detector"""
        await self.token_manager.initialize()

    @utils.measure_execution_time
    async def find_opportunities(
        self,
        max_tokens: int = 100,
        min_profit_percentage: float = 0.1,
        batch_size: int = 5
    ) -> List[Dict[str, Any]]:
        """
        Find arbitrage opportunities

        Args:
            max_tokens: Maximum number of tokens to check
            min_profit_percentage: Minimum profit percentage to consider
            batch_size: Number of tokens to process in parallel

        Returns:
            List of arbitrage opportunities
        """
        # Clear previous opportunities
        self.opportunities = []

        # Update SOL price
        await self.token_manager.update_sol_price()
        if self.token_manager.sol_price_usd is None:
            logger.error("Failed to get SOL price. Cannot find arbitrage opportunities.")
            return []

        # Get filtered tokens
        tokens = await self.token_manager.get_filtered_tokens(limit=max_tokens)
        if not tokens:
            logger.warning("No tokens found for arbitrage analysis.")
            return []

        # Store the tokens for reference
        self.analyzed_tokens = tokens

        logger.info(f"Analyzing {len(tokens)} tokens for arbitrage opportunities...")

        # Process tokens in batches
        for i in range(0, len(tokens), batch_size):
            batch = tokens[i:i+batch_size]
            utils.display_progress(i + len(batch), len(tokens))

            # Process batch in parallel
            tasks = [
                self.jupiter_api.analyze_arbitrage_opportunity(
                    token_info=token,
                    sol_price_usd=self.token_manager.sol_price_usd,
                    amount_sol=config.CAPITAL_AMOUNT_SOL,
                    slippage_bps=config.SLIPPAGE_BPS,
                    slippage_cost_percentage=config.SLIPPAGE_COST_PERCENTAGE
                )
                for token in batch
            ]

            results = await asyncio.gather(*tasks)

            # Filter valid opportunities
            valid_opportunities = [
                opp for opp in results
                if opp is not None and opp.get("profit_percentage", 0) >= min_profit_percentage
            ]

            # Add to opportunities list
            self.opportunities.extend(valid_opportunities)

            # Longer delay to avoid rate limiting
            await asyncio.sleep(5.0)

            # Log progress if we found opportunities
            if self.opportunities and len(self.opportunities) % 5 == 0:
                logger.info(f"Found {len(self.opportunities)} opportunities so far. Continuing analysis...")

            # Early exit if we found a lot of opportunities to speed up the process
            if len(self.opportunities) >= 20:
                logger.info(f"Found {len(self.opportunities)} opportunities. Stopping early to speed up the process.")
                break

            # Add a longer pause every 3 batches to avoid rate limiting
            if (i + len(batch)) % (batch_size * 3) == 0:
                logger.info(f"Pausing for 20 seconds to avoid rate limiting...")
                await asyncio.sleep(20.0)

        # Sort opportunities by profit percentage (descending)
        self.opportunities.sort(key=lambda x: x.get("profit_percentage", 0), reverse=True)

        logger.info(f"Found {len(self.opportunities)} arbitrage opportunities with profit >= {min_profit_percentage}%")
        return self.opportunities

    async def validate_opportunities(self) -> List[Dict[str, Any]]:
        """
        Validate arbitrage opportunities with additional checks

        Returns:
            List of validated arbitrage opportunities
        """
        if not self.opportunities:
            return []

        validated_opportunities = []

        for opp in self.opportunities:
            # Skip if already validated
            if opp.get("validated", False):
                validated_opportunities.append(opp)
                continue

            token_mint = opp.get("token_mint")
            token_symbol = opp.get("token_symbol", "???")

            # Check price difference percentage
            buy_price = opp.get("buy_price_usd")
            sell_price = opp.get("sell_price_usd")

            if buy_price and sell_price and buy_price > 0:
                price_diff_percentage = abs((sell_price / buy_price - 1) * 100)

                # Skip if price difference is too large (likely invalid)
                if price_diff_percentage > config.MAX_PRICE_DIFFERENCE_PERCENTAGE:
                    logger.warning(
                        f"Skipping {token_symbol}: Price difference too large ({price_diff_percentage:.2f}% > "
                        f"{config.MAX_PRICE_DIFFERENCE_PERCENTAGE}%)"
                    )
                    continue

            # Mark as validated
            opp["validated"] = True
            validated_opportunities.append(opp)

        # Update opportunities list
        self.opportunities = validated_opportunities

        logger.info(f"Validated {len(validated_opportunities)} arbitrage opportunities")
        return validated_opportunities

    async def get_detailed_opportunity(self, opportunity: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get detailed information for an arbitrage opportunity

        Args:
            opportunity: Basic opportunity information

        Returns:
            Detailed opportunity information
        """
        token_mint = opportunity.get("token_mint")
        if not token_mint:
            return opportunity

        # Get detailed token information if not already present
        if "tags" not in opportunity and "token_info" not in opportunity:
            token_info = await self.token_manager.get_token_info(token_mint)
            if token_info:
                opportunity["token_info"] = token_info
                opportunity["tags"] = token_info.get("tags", [])

        return opportunity
