# Jupiter Arbitrage Detector

Program untuk mencari peluang swap menguntungkan di Jupiter Aggregator pada blockchain Solana.

![Jupiter Logo](https://static.jup.ag/jup/icon.png)

## Deskripsi

Jupiter Arbitrage Detector adalah program Python yang menggunakan Jupiter API untuk mencari peluang swap menguntungkan. Program ini akan:

1. Mendapatkan daftar token yang dapat diperdagangkan dari Jupiter API
2. Mendapatkan harga token dalam USD
3. Mensimulasikan swap dari SOL ke token lain
4. Menganalisis hasil swap untuk menemukan peluang menguntungkan
5. Menampilkan peluang swap yang ditemukan

## Cara Kerja

Program ini bekerja dengan cara berikut:

1. **Mendapatkan Daftar Token**: Program menggunakan Jupiter Token API untuk mendapatkan daftar token yang dapat diperdagangkan di Jupiter Aggregator.
2. **Memfilter Token**: Program memfilter token berdasarkan tag dan volume untuk menghindari token dengan likuiditas rendah.
3. **Mendapatkan Harga Token**: Program menggunakan Jupiter Price API untuk mendapatkan harga token dalam USD.
4. **Mensimulasikan Swap**: Program menggunakan Jupiter Quote API untuk mensimulasikan swap dari SOL ke token lain.
5. **Menganalisis Hasil Swap**: Program menganalisis hasil swap untuk menemukan peluang menguntungkan.
6. **Menampilkan Peluang Swap**: Program menampilkan peluang swap yang ditemukan.

## Instalasi

1. Clone repository ini
2. Install dependensi dengan pip:

```bash
pip install -r requirements.txt
```

## Penggunaan

### Program Utama

Jalankan program utama dengan perintah:

```bash
python main.py
```

Program ini akan mencoba terhubung ke Jupiter API dan mencari peluang swap menguntungkan.

### Program Simulasi

Jika Anda mengalami masalah dengan koneksi ke Jupiter API, Anda dapat menjalankan program simulasi dengan perintah:

```bash
python simulation.py
```

Program simulasi ini akan mensimulasikan pencarian peluang swap tanpa terhubung ke Jupiter API.

## Konfigurasi

Konfigurasi program dapat diubah di file `config.yaml`. Beberapa pengaturan yang dapat diubah:

- `base_token_amount`: Jumlah SOL untuk simulasi swap (default: 1.0)
- `slippage_percentage`: Persentase slippage yang digunakan dalam perhitungan (default: 1.0%)
- `min_profit_percentage`: Persentase profit minimum (default: 1.0%)
- `check_interval_seconds`: Seberapa sering memeriksa peluang (default: 15 detik)
- `max_tokens`: Jumlah maksimum token untuk dianalisis (default: 500)
- `min_daily_volume`: Volume harian minimum dalam USD (default: 1000)
- `exclude_tags`: Tag token yang akan dilewati (default: ["meme", "scam"])

## Struktur Program

Program ini terdiri dari beberapa komponen utama:

- `main.py`: File utama yang menjalankan program dan menangani loop utama.
- `simulation.py`: File untuk mensimulasikan pencarian peluang swap tanpa terhubung ke Jupiter API.
- `src/utils.py`: Berisi fungsi-fungsi utilitas seperti loading konfigurasi, setup logging, dan formatting.
- `src/jupiter_api.py`: Berisi kelas JupiterAPI untuk berinteraksi dengan Jupiter API.
- `src/arbitrage_detector.py`: Berisi kelas ArbitrageDetector untuk mendeteksi peluang arbitrase.
- `config.yaml`: File konfigurasi untuk mengatur parameter program.

## Fitur Program

1. **Caching**: Program menggunakan caching untuk mengurangi jumlah permintaan API.
2. **Batching**: Program menggunakan batching untuk mengoptimalkan permintaan API.
3. **Filtering**: Program memfilter token berdasarkan tag dan volume untuk menghindari token dengan likuiditas rendah.
4. **Error Handling**: Program menangani error dengan baik dan mencoba ulang permintaan API yang gagal.
5. **Logging**: Program menggunakan logging untuk mencatat aktivitas dan error.
6. **Rich Output**: Program menggunakan rich untuk menampilkan output yang lebih baik.

## Referensi API Jupiter

Program ini menggunakan beberapa API Jupiter:

1. **Token API**: Untuk mendapatkan daftar token yang dapat diperdagangkan.
   - Endpoint: `https://lite-api.jup.ag/tokens/v1/mints/tradable`
   - Dokumentasi: [Jupiter Token API](https://dev.jup.ag/docs/token-api)

2. **Price API**: Untuk mendapatkan harga token dalam USD.
   - Endpoint: `https://lite-api.jup.ag/price/v2`
   - Dokumentasi: [Jupiter Price API](https://dev.jup.ag/docs/price-api)

3. **Quote API**: Untuk mensimulasikan swap.
   - Endpoint: `https://lite-api.jup.ag/swap/v1/quote`
   - Dokumentasi: [Jupiter Quote API](https://dev.jup.ag/docs/swap-api/get-quote)

## Catatan

- Program ini hanya mensimulasikan swap dan tidak melakukan swap sebenarnya
- Peluang swap yang ditemukan mungkin tidak selalu menguntungkan karena perubahan harga dan slippage
- Program ini menggunakan Jupiter API yang memiliki rate limit, jadi jangan menjalankan terlalu sering

## Disclaimer

Program ini hanya untuk tujuan pendidikan dan tidak dimaksudkan untuk digunakan dalam trading sebenarnya. Gunakan dengan risiko Anda sendiri.
