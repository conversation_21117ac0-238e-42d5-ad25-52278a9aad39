"""
File debug untuk menguji komponen-komponen program.
"""
import os
import sys
import yaml
import logging
from rich.console import Console

# Inisialisasi Rich console
console = Console()

def main():
    """
    Fungsi utama untuk debugging.
    """
    console.print("[bold green]Memulai debugging...[/bold green]")
    
    # Cek apakah file config.yaml ada
    if not os.path.exists("config.yaml"):
        console.print("[bold red]File config.yaml tidak ditemukan![/bold red]")
        return
    
    console.print("[bold green]File config.yaml ditemukan.[/bold green]")
    
    # Coba memuat konfigurasi
    try:
        with open("config.yaml", 'r') as file:
            config = yaml.safe_load(file)
        console.print("[bold green]Konfigurasi berhasil dimuat.[/bold green]")
        
        # Tampilkan jaringan yang dikonfigurasi
        console.print(f"[bold]J<PERSON>an yang Dikonfigurasi:[/bold]")
        for network_name in config['networks'].keys():
            console.print(f"  • [cyan]{network_name}[/cyan]")
        
        # Cek apakah direktori logs ada
        if not os.path.exists("logs"):
            console.print("[bold yellow]Direktori logs tidak ditemukan. Membuat direktori...[/bold yellow]")
            os.makedirs("logs", exist_ok=True)
        
        # Cek apakah direktori abis ada
        if not os.path.exists("abis"):
            console.print("[bold red]Direktori abis tidak ditemukan![/bold red]")
            return
        
        # Cek apakah file ABI ada
        abi_files = ["UniswapV2Router.json", "UniswapV3Quoter.json", "ERC20.json"]
        for abi_file in abi_files:
            if not os.path.exists(f"abis/{abi_file}"):
                console.print(f"[bold red]File ABI {abi_file} tidak ditemukan![/bold red]")
                return
        
        console.print("[bold green]Semua file ABI ditemukan.[/bold green]")
        
        # Cek apakah modul src ada
        if not os.path.exists("src"):
            console.print("[bold red]Direktori src tidak ditemukan![/bold red]")
            return
        
        # Cek apakah file-file src ada
        src_files = ["__init__.py", "network.py", "dex.py", "arbitrage.py", "utils.py"]
        for src_file in src_files:
            if not os.path.exists(f"src/{src_file}"):
                console.print(f"[bold red]File {src_file} tidak ditemukan![/bold red]")
                return
        
        console.print("[bold green]Semua file src ditemukan.[/bold green]")
        
        # Coba mengimpor modul-modul
        try:
            from src.utils import setup_logging
            from src.network import Network
            from src.dex import DEX
            from src.arbitrage import ArbitrageDetector
            
            console.print("[bold green]Semua modul berhasil diimpor.[/bold green]")
            
            # Setup logging
            logger = setup_logging("DEBUG", "logs")
            console.print("[bold green]Logging berhasil diatur.[/bold green]")
            
            # Coba inisialisasi Network
            try:
                # Ambil jaringan pertama untuk pengujian
                network_name = list(config['networks'].keys())[0]
                network_config = config['networks'][network_name]
                
                console.print(f"[bold]Mencoba menginisialisasi jaringan {network_name}...[/bold]")
                
                # Inisialisasi Network
                network = Network(network_name, network_config)
                console.print(f"[bold green]Jaringan {network_name} berhasil diinisialisasi.[/bold green]")
                
                # Cek koneksi
                if network.w3.is_connected():
                    console.print(f"[bold green]Berhasil terhubung ke jaringan {network_name}.[/bold green]")
                else:
                    console.print(f"[bold red]Gagal terhubung ke jaringan {network_name}.[/bold red]")
                
            except Exception as e:
                console.print(f"[bold red]Error saat menginisialisasi jaringan: {e}[/bold red]")
        
        except Exception as e:
            console.print(f"[bold red]Error saat mengimpor modul: {e}[/bold red]")
    
    except Exception as e:
        console.print(f"[bold red]Error saat memuat konfigurasi: {e}[/bold red]")

if __name__ == "__main__":
    main()
