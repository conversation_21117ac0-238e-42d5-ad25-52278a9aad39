"""
File untuk menjalankan program utama dengan cara yang lebih aman.
"""
import os
import sys
import importlib.util
import traceback

def main():
    """
    Fungsi utama untuk menjalankan program.
    """
    print("Menjalankan program utama...")
    
    # Dapatkan direktori script
    script_dir = os.path.dirname(os.path.abspath(__file__))
    print(f"Direktori script: {script_dir}")
    
    # Ubah direktori kerja ke direktori script
    os.chdir(script_dir)
    print(f"Direktori kerja diubah ke: {os.getcwd()}")
    
    # Cek apakah file main.py ada
    main_path = os.path.join(script_dir, "main.py")
    print(f"Path main.py: {main_path}")
    
    if os.path.exists(main_path):
        print("File main.py ditemukan!")
        
        # Impor main.py sebagai modul
        try:
            print("\nMemulai program utama...")
            print("=" * 50)
            
            # Impor modul main
            spec = importlib.util.spec_from_file_location("main", main_path)
            main_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(main_module)
            
            # Jalankan fungsi main
            if hasattr(main_module, "main"):
                # Jika main adalah fungsi async
                import asyncio
                asyncio.run(main_module.main())
            else:
                print("Fungsi main tidak ditemukan dalam modul main.py")
            
            print("=" * 50)
            print("Program selesai.")
        except Exception as e:
            print(f"Error saat menjalankan program: {e}")
            traceback.print_exc()
    else:
        print("File main.py tidak ditemukan!")

if __name__ == "__main__":
    main()
