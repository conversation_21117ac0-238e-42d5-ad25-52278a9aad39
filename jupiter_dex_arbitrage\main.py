"""
Jupiter DEX Arbitrage Analyzer

Program untuk menemukan peluang arbitrase pada Jupiter DEX Aggregator di blockchain Solana.
"""

import asyncio
import json
import logging
import os
import sys
import time
from datetime import datetime
from typing import Dict, List, Optional, Any

import aiohttp
from rich.console import Console
from rich.panel import Panel

import config
import utils
from arbitrage_detector import ArbitrageDetector

# Setup logging
logger = utils.setup_logging("jupiter_arbitrage.log")
console = utils.console

async def main():
    """Main function to run the Jupiter DEX Arbitrage Analyzer"""
    utils.display_header()

    console.print("[bold cyan]Memulai Jupiter DEX Arbitrage Analyzer...[/]")
    console.print("[yellow]Menghubungkan ke Jupiter DEX Aggregator API...[/]")

    # Create aiohttp session
    async with aiohttp.ClientSession() as session:
        try:
            # Initialize arbitrage detector
            detector = ArbitrageDetector(session)
            await detector.initialize()

            if detector.token_manager.sol_price_usd is None:
                console.print("[bold red]Gagal mendapatkan harga SOL. Program tidak dapat dilanjutkan.[/]")
                return

            console.print(f"[green]Harga SOL saat ini:[/] ${detector.token_manager.sol_price_usd:.2f}")
            console.print(f"[green]Modal:[/] {config.CAPITAL_AMOUNT_SOL} SOL (${config.CAPITAL_AMOUNT_SOL * detector.token_manager.sol_price_usd:.2f})")
            console.print(f"[green]Minimum profit:[/] {config.MIN_PROFIT_PERCENTAGE}%")
            console.print(f"[green]Slippage:[/] {config.SLIPPAGE_BPS / 100}%")
            console.print("")

            # Find arbitrage opportunities
            console.print("[bold cyan]Mencari peluang arbitrase...[/]")
            opportunities = await detector.find_opportunities(
                min_profit_percentage=config.MIN_PROFIT_PERCENTAGE,
                batch_size=config.BATCH_SIZE
            )

            # Get the tokens that were analyzed
            tokens = detector.analyzed_tokens

            # Validate opportunities
            if opportunities:
                console.print("[bold cyan]Memvalidasi peluang arbitrase...[/]")
                validated_opportunities = await detector.validate_opportunities()

                # Display opportunities
                utils.display_arbitrage_opportunities(
                    validated_opportunities[:config.MAX_OPPORTUNITIES_TO_DISPLAY],
                    detector.token_manager.sol_price_usd
                )

                # Save opportunities to file
                if validated_opportunities:
                    filename = f"jupiter_arbitrage_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
                    with open(filename, "w", encoding="utf-8") as f:
                        json.dump(validated_opportunities, f, indent=2)
                    console.print(f"[green]Peluang arbitrase disimpan ke file:[/] {filename}")
            else:
                console.print("[yellow]Tidak ditemukan peluang arbitrase yang memenuhi kriteria.[/]")

                # Display some of the analyzed tokens for debugging
                if tokens:
                    console.print("[bold cyan]Beberapa token yang dianalisis:[/]")
                    for i, token in enumerate(tokens[:5], 1):
                        console.print(f"[cyan]{i}.[/] [bold]{token.get('name', 'Unknown')}[/] ({token.get('symbol', '???')})")
                        console.print(f"   Address: {token.get('address', 'Unknown')}")
                        console.print(f"   Volume 24h: ${token.get('daily_volume', 0):.2f}")
                        console.print(f"   Tags: {', '.join(token.get('tags', []))}")
                        console.print("")

            # Display summary
            console.print("")
            console.print(Panel(
                f"[bold green]Analisis Selesai[/]\n"
                f"[cyan]Waktu:[/] {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
                f"[cyan]Total token terverifikasi:[/] {len(detector.token_manager.verified_tokens)}\n"
                f"[cyan]Total token dianalisis:[/] {len(tokens) if 'tokens' in locals() else 0}\n"
                f"[cyan]Total peluang ditemukan:[/] {len(opportunities)}\n"
                f"[cyan]Total peluang valid:[/] {len(detector.opportunities)}",
                title="Ringkasan",
                border_style="green"
            ))

        except Exception as e:
            logger.exception(f"Error in main function: {str(e)}")
            console.print(f"[bold red]Terjadi kesalahan:[/] {str(e)}")

        finally:
            console.print("[bold cyan]Program selesai.[/]")

if __name__ == "__main__":
    try:
        # Run the main function
        asyncio.run(main())
    except KeyboardInterrupt:
        console.print("[bold yellow]Program dihentikan oleh pengguna.[/]")
        sys.exit(0)
    except Exception as e:
        logger.exception(f"Unhandled exception: {str(e)}")
        console.print(f"[bold red]Terjadi kesalahan yang tidak tertangani:[/] {str(e)}")
        sys.exit(1)
