"""
Modul untuk memformat output dalam gaya pesan WhatsApp untuk grup trading.
"""

import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
import random
import emoji

from utils import (
    format_address, format_price, format_percentage, format_usd, format_idr,
    get_dexscreener_pair_url
)

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("whatsapp_formatter")

# Emoji yang akan digunakan
EMOJIS = {
    "rocket": emoji.emojize(":rocket:"),
    "money_bag": emoji.emojize(":money_bag:"),
    "chart": emoji.emojize(":chart_increasing:"),
    "fire": emoji.emojize(":fire:"),
    "gem": emoji.emojize(":gem_stone:"),
    "warning": emoji.emojize(":warning:"),
    "check": emoji.emojize(":check_mark_button:"),
    "cross": emoji.emojize(":cross_mark:"),
    "clock": emoji.emojize(":alarm_clock:"),
    "globe": emoji.emojize(":globe_showing_Americas:"),
    "lock": emoji.emojize(":locked:"),
    "unlock": emoji.emojize(":unlocked:"),
    "star": emoji.emojize(":star:"),
    "dollar": emoji.emojize(":dollar_banknote:"),
    "moneymouth": emoji.emojize(":money_mouth_face:"),
    "bulb": emoji.emojize(":light_bulb:"),
    "target": emoji.emojize(":direct_hit:"),
    "trophy": emoji.emojize(":trophy:"),
    "sparkles": emoji.emojize(":sparkles:"),
    "handshake": emoji.emojize(":handshake:"),
    "eyes": emoji.emojize(":eyes:"),
    "alert": emoji.emojize(":rotating_light:"),
    "graph": emoji.emojize(":bar_chart:"),
    "memo": emoji.emojize(":memo:"),
    "calendar": emoji.emojize(":tear-off_calendar:"),
    "time": emoji.emojize(":hourglass_not_done:"),
    "link": emoji.emojize(":link:"),
    "key": emoji.emojize(":key:"),
    "shield": emoji.emojize(":shield:"),
    "diamond": emoji.emojize(":diamond_with_a_dot:"),
    "up": emoji.emojize(":up_arrow:"),
    "down": emoji.emojize(":down_arrow:"),
    "right": emoji.emojize(":right_arrow:"),
    "left": emoji.emojize(":left_arrow:"),
}

# Frasa-frasa motivasi trading
TRADING_PHRASES = [
    "Peluang tidak menunggu. Bertindak sekarang!",
    "Pasar bergerak cepat. Jangan sampai ketinggalan!",
    "Profit menunggu mereka yang berani bertindak!",
    "Arbitrase adalah seni memanfaatkan inefisiensi pasar!",
    "Trader cerdas melihat peluang di mana orang lain tidak!",
    "Waktu adalah uang dalam trading!",
    "Analisis cepat, eksekusi tepat, profit maksimal!",
    "Peluang arbitrase terbaik hari ini!",
    "Jangan biarkan profit ini lewat begitu saja!",
    "Pasar crypto tidak pernah tidur, begitu juga peluang profit!",
    "Arbitrase: Strategi rendah risiko, tinggi hasil!",
    "Trader profesional selalu satu langkah di depan!",
    "Crypto adalah masa depan, arbitrase adalah sekarang!",
    "Profit konsisten datang dari strategi yang teruji!",
    "Pasar volatile, peluang arbitrase stabil!",
]

def get_random_phrase() -> str:
    """Mendapatkan frasa motivasi trading secara acak."""
    return random.choice(TRADING_PHRASES)

def get_signal_header() -> str:
    """Membuat header untuk sinyal trading."""
    current_time = datetime.now().strftime("%d/%m/%Y %H:%M:%S")

    header = f"""
{EMOJIS['alert']}{EMOJIS['alert']} *CRYPTO ARBITRAGE SIGNAL* {EMOJIS['alert']}{EMOJIS['alert']}

{EMOJIS['fire']}🔹🔸 *ALPHA SIGNALS* 🔸🔹{EMOJIS['fire']}
{EMOJIS['calendar']} *{current_time}* {EMOJIS['time']}
{EMOJIS['rocket']} *{get_random_phrase()}* {EMOJIS['rocket']}

{EMOJIS['sparkles']}{'⚡'*20}{EMOJIS['sparkles']}
"""
    return header

def get_signal_footer() -> str:
    """Membuat footer untuk sinyal trading."""
    footer = f"""
{EMOJIS['sparkles']}{'⚡'*20}{EMOJIS['sparkles']}
{EMOJIS['warning']} *DISCLAIMER* {EMOJIS['warning']}
{EMOJIS['memo']} Sinyal ini dibuat oleh bot analisis otomatis
{EMOJIS['shield']} Selalu lakukan riset mandiri sebelum trading
{EMOJIS['eyes']} Harga dapat berubah dengan cepat
{EMOJIS['handshake']} Trading dengan bijak dan bertanggung jawab

{EMOJIS['diamond']}{EMOJIS['diamond']} *DEX ARBITRAGE ANALYZER* {EMOJIS['diamond']}{EMOJIS['diamond']}
{EMOJIS['graph']} _Powered by AI Trading Technology_
{EMOJIS['rocket']} _Join VIP Group: wa.me/+6281234567890_

🔸🔹🔸🔹🔸🔹🔸🔹🔸🔹🔸🔹🔸🔹
"""
    return footer

def format_opportunity_for_whatsapp(opportunity: Dict[str, Any], index: int) -> str:
    """
    Memformat peluang arbitrase untuk pesan WhatsApp.

    Args:
        opportunity: Data peluang arbitrase.
        index: Indeks peluang.

    Returns:
        String yang diformat untuk WhatsApp.
    """
    # Dapatkan data dari opportunity
    if opportunity["type"] == "same_chain":
        chain_id = opportunity["chain_id"].upper()
        opportunity_type = f"SAME-CHAIN ({chain_id})"
    else:  # cross_chain
        source_chain = opportunity["source_chain"].upper()
        target_chain = opportunity["target_chain"].upper()
        opportunity_type = f"CROSS-CHAIN ({source_chain}→{target_chain})"

    base_symbol = opportunity["base_token"]["symbol"].upper()
    quote_symbol = opportunity["quote_token"]["symbol"].upper()
    pair = f"{base_symbol}/{quote_symbol}"

    buy_dex = opportunity["buy_dex"].upper()
    sell_dex = opportunity["sell_dex"].upper()

    buy_price = opportunity["buy_price"]
    sell_price = opportunity["sell_price"]
    price_diff = opportunity["price_diff_percentage"]

    buy_liquidity = opportunity["buy_liquidity"]
    sell_liquidity = opportunity["sell_liquidity"]

    capital = opportunity["capital"]
    net_profit = opportunity["net_profit"]
    net_profit_idr = opportunity["net_profit_idr"]
    roi = opportunity["roi_percentage"]

    # Buat emoji berdasarkan ROI
    roi_emoji = EMOJIS['fire'] * min(5, max(1, int(roi / 20)))

    # Buat tautan Dexscreener
    buy_link = get_dexscreener_pair_url(opportunity["buy_pair"])
    sell_link = get_dexscreener_pair_url(opportunity["sell_pair"])

    # Format pesan WhatsApp
    message = f"""
{EMOJIS['star']}{EMOJIS['star']} *SIGNAL #{index+1}* {EMOJIS['star']}{EMOJIS['star']}
{EMOJIS['target']} *{opportunity_type}* {EMOJIS['target']}
{EMOJIS['gem']} *Pair:* {pair} {EMOJIS['gem']}

{EMOJIS['dollar']}{EMOJIS['dollar']} *BUY AT* {EMOJIS['dollar']}{EMOJIS['dollar']}
{EMOJIS['right']} *DEX:* {buy_dex}
{EMOJIS['right']} *Price:* {format_price(buy_price)} USD
{EMOJIS['link']} {buy_link}

{EMOJIS['chart']}{EMOJIS['chart']} *SELL AT* {EMOJIS['chart']}{EMOJIS['chart']}
{EMOJIS['right']} *DEX:* {sell_dex}
{EMOJIS['right']} *Price:* {format_price(sell_price)} USD
{EMOJIS['link']} {sell_link}

{EMOJIS['fire']} *PROFIT DETAILS* {EMOJIS['fire']}
{EMOJIS['up']} *Price Diff:* {format_percentage(price_diff)} {roi_emoji}
{EMOJIS['money_bag']} *Profit:* {format_usd(net_profit)} / {format_idr(net_profit_idr)}
{EMOJIS['trophy']} *ROI:* {format_percentage(roi)}

{EMOJIS['bulb']} *EXECUTION STRATEGY* {EMOJIS['bulb']}
1️⃣ Connect wallet to {chain_id if opportunity["type"] == "same_chain" else source_chain}
2️⃣ Buy {base_symbol} on {buy_dex} with {format_usd(capital)}
3️⃣ {f"Bridge to {target_chain}" if opportunity["type"] == "cross_chain" else ""}
4️⃣ Sell {base_symbol} on {sell_dex} for {quote_symbol}
5️⃣ Secure your profit! {EMOJIS['moneymouth']}
"""

    return message

def format_opportunities_for_whatsapp(opportunities: List[Dict[str, Any]], limit: int = 5) -> str:
    """
    Memformat daftar peluang arbitrase untuk pesan WhatsApp.

    Args:
        opportunities: List peluang arbitrase.
        limit: Jumlah peluang yang ditampilkan.

    Returns:
        String yang diformat untuk WhatsApp.
    """
    if not opportunities:
        return f"{EMOJIS['cross']} *Tidak ada peluang arbitrase yang ditemukan.*"

    # Batasi jumlah peluang yang ditampilkan
    opportunities_to_show = opportunities[:limit]

    # Buat header
    message = get_signal_header()

    # Tambahkan setiap peluang
    for i, opportunity in enumerate(opportunities_to_show):
        message += format_opportunity_for_whatsapp(opportunity, i)

        # Tambahkan pemisah kecuali untuk peluang terakhir
        if i < len(opportunities_to_show) - 1:
            message += f"\n{EMOJIS['sparkles']}{'⚡✨⚡✨⚡✨⚡✨⚡✨⚡✨⚡✨⚡✨'}{EMOJIS['sparkles']}\n"

    # Tambahkan footer
    message += get_signal_footer()

    return message

def save_whatsapp_message_to_file(message: str, filename: str = "arbitrage_signal.txt"):
    """
    Menyimpan pesan WhatsApp ke file.

    Args:
        message: Pesan WhatsApp.
        filename: Nama file.
    """
    try:
        with open(filename, "w", encoding="utf-8") as f:
            f.write(message)
        logger.info(f"Pesan WhatsApp berhasil disimpan ke file: {filename}")
    except Exception as e:
        logger.error(f"Error saat menyimpan pesan WhatsApp ke file: {e}")
