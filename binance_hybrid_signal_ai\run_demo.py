#!/usr/bin/env python3
"""
Binance Hybrid Signal AI - Demo Mode Runner
===========================================

Script untuk menjalankan program dalam demo mode tanpa koneksi exchange.
Berguna untuk testing dan demonstrasi.
"""

import sys
import os
import subprocess
import tempfile
import shutil

def enable_demo_mode():
    """Enable demo mode dalam file utama"""
    main_file = "binance3timeframe (1).py"
    backup_file = "binance3timeframe (1).py.backup"
    
    if not os.path.exists(main_file):
        print(f"❌ File {main_file} tidak ditemukan!")
        return False
    
    # Backup original file
    shutil.copy2(main_file, backup_file)
    print(f"✅ Backup dibuat: {backup_file}")
    
    # Read original file
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Enable demo mode
    content = content.replace(
        'DEMO_MODE = False',
        'DEMO_MODE = True'
    )
    
    # Write modified file
    with open(main_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ Demo mode diaktifkan!")
    return True

def disable_demo_mode():
    """Disable demo mode dan restore original"""
    main_file = "binance3timeframe (1).py"
    backup_file = "binance3timeframe (1).py.backup"
    
    if os.path.exists(backup_file):
        shutil.copy2(backup_file, main_file)
        os.remove(backup_file)
        print("✅ Demo mode dinonaktifkan, file original dipulihkan!")
        return True
    else:
        print("⚠️ Backup file tidak ditemukan, manual restore diperlukan")
        return False

def create_demo_data():
    """Create mock data untuk demo"""
    import random
    import json
    from datetime import datetime, timedelta
    
    demo_data = {
        'pairs': ['BTCUSDT', 'ETHUSDT', 'ADAUSDT', 'BNBUSDT', 'SOLUSDT'],
        'signals': [
            {
                'pair': 'BTCUSDT',
                'signal': 'KUAT BELI',
                'score': 8.5,
                'confidence': 0.85,
                'price': 45000.0,
                'reasons': ['Bullish RSI Divergence', 'Strong Volume', 'Bullish OB Strong']
            },
            {
                'pair': 'ETHUSDT', 
                'signal': 'BELI',
                'score': 6.2,
                'confidence': 0.72,
                'price': 3200.0,
                'reasons': ['MACD Bullish', 'Volume Increase', 'Bullish OB Medium']
            },
            {
                'pair': 'ADAUSDT',
                'signal': 'JUAL',
                'score': -4.8,
                'confidence': 0.68,
                'price': 0.45,
                'reasons': ['Bearish Divergence', 'Overbought RSI', 'Bearish OB Weak']
            }
        ],
        'timestamp': datetime.now().isoformat()
    }
    
    with open('demo_data.json', 'w') as f:
        json.dump(demo_data, f, indent=2)
    
    print("✅ Demo data dibuat: demo_data.json")

def run_demo():
    """Jalankan program dalam demo mode"""
    main_file = "binance3timeframe (1).py"
    
    print("🎭 Menjalankan Binance Hybrid Signal AI dalam DEMO MODE...")
    print("=" * 60)
    print("📝 CATATAN:")
    print("   • Tidak ada koneksi ke exchange")
    print("   • Data dan sinyal adalah simulasi")
    print("   • Untuk testing dan demonstrasi saja")
    print("=" * 60)
    
    try:
        subprocess.run([sys.executable, main_file], check=True)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error running demo: {e}")
        return False
    except KeyboardInterrupt:
        print("\n⚠️ Demo dihentikan oleh user (Ctrl+C)")
        return True

def show_demo_info():
    """Tampilkan informasi demo mode"""
    print("=" * 80)
    print("🎭 BINANCE HYBRID SIGNAL AI - DEMO MODE")
    print("=" * 80)
    print("📋 Fitur Demo Mode:")
    print("   • ✅ GUI interface lengkap")
    print("   • ✅ Mock data untuk testing")
    print("   • ✅ Simulasi sinyal trading")
    print("   • ✅ Logging functionality")
    print("   • ❌ Tidak ada koneksi exchange")
    print("   • ❌ Data bukan real-time")
    print()
    print("🎯 Berguna untuk:")
    print("   • Testing program tanpa internet")
    print("   • Demonstrasi kepada client")
    print("   • Development dan debugging")
    print("   • Training dan pembelajaran")
    print()
    print("⚠️ PERINGATAN:")
    print("   • Jangan gunakan untuk trading real!")
    print("   • Data adalah simulasi/mock")
    print("   • Sinyal tidak akurat untuk market real")
    print("=" * 80)

def main():
    """Main function"""
    import argparse
    
    parser = argparse.ArgumentParser(
        description="Binance Hybrid Signal AI Demo Mode Runner",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python run_demo.py                # Run in demo mode
  python run_demo.py --info         # Show demo info
  python run_demo.py --enable       # Enable demo mode only
  python run_demo.py --disable      # Disable demo mode
  python run_demo.py --create-data  # Create demo data
        """
    )
    
    parser.add_argument('--info', action='store_true',
                       help='Show demo mode information')
    parser.add_argument('--enable', action='store_true',
                       help='Enable demo mode only (don\'t run)')
    parser.add_argument('--disable', action='store_true',
                       help='Disable demo mode and restore original')
    parser.add_argument('--create-data', action='store_true',
                       help='Create demo data file')
    
    args = parser.parse_args()
    
    # Show info
    if args.info:
        show_demo_info()
        return
    
    # Create demo data
    if args.create_data:
        create_demo_data()
        return
    
    # Enable demo mode only
    if args.enable:
        enable_demo_mode()
        return
    
    # Disable demo mode
    if args.disable:
        disable_demo_mode()
        return
    
    # Default: run demo
    try:
        # Enable demo mode
        if enable_demo_mode():
            # Create demo data
            create_demo_data()
            
            # Run program
            success = run_demo()
            
            # Restore original
            disable_demo_mode()
            
            if success:
                print("\n✅ Demo selesai dengan sukses!")
            else:
                print("\n❌ Demo berakhir dengan error!")
        else:
            print("❌ Gagal mengaktifkan demo mode!")
            
    except KeyboardInterrupt:
        print("\n⚠️ Demo dibatalkan oleh user")
        disable_demo_mode()
    except Exception as e:
        print(f"\n❌ Error dalam demo: {e}")
        disable_demo_mode()

if __name__ == "__main__":
    main()
