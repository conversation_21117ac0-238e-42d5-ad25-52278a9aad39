#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Binance Signal Prompt Generator - ROBUST ERROR-RESISTANT VERSION
----------------------------------------------------------------
Program untuk mencari peluang pada semua pairs di pasar futures binance
dan men<PERSON><PERSON>lkan prompt untuk AI dengan defensive programming.

Version: 3.0.0 - Error-Resistant Edition
Author: bobacheese
"""

import os
import sys
import time
import json
import traceback
import ccxt
import pandas as pd
import numpy as np
import threading
from datetime import datetime
from typing import Dict, List, Tuple, Optional, Union, Any
from concurrent.futures import ThreadPoolExecutor, as_completed

# Rich untuk tampilan yang lebih menarik
try:
    from rich.console import Console
    from rich.table import Table
    from rich.panel import Panel
    from rich.progress import Progress, SpinnerColumn, Text<PERSON><PERSON>umn, Bar<PERSON>olumn, TaskProgressColumn
    from rich.prompt import Prompt
    from rich.text import Text
    from rich.align import Align
    HAS_RICH = True
except ImportError:
    HAS_RICH = False
    print("Rich tidak ditemukan. Menggunakan tampilan sederhana.")

# Pyperclip untuk copy ke clipboard
try:
    import pyperclip
    HAS_PYPERCLIP = True
except ImportError:
    HAS_PYPERCLIP = False

# Konstanta
VERSION = "3.0.0"
MAX_WORKERS = 8
MIN_VOLUME_24H = 1000000  # Minimum volume 24 jam dalam USDT
MIN_PRICE_CHANGE = 2.0    # Minimum perubahan harga 24 jam dalam persen
TOP_SIGNALS_COUNT = 10    # Jumlah sinyal teratas yang ditampilkan


class RobustSignalGenerator:
    """Kelas utama untuk Binance Signal Prompt Generator dengan error resistance"""

    def __init__(self):
        """Inisialisasi kelas"""
        self.console = Console()
        self.exchange = None
        self.pairs = []
        self.data = {}
        self.signals = {}
        self.top_signals = []

    def _format_value(self, value, precision=2):
        """
        ROBUST VALUE FORMATTER - CORE DEFENSIVE FUNCTION
        Formats a value as a float with a given precision if it's a valid number.
        Returns 'N/A' otherwise. This prevents ALL format string errors.
        """
        try:
            # Handle None values
            if value is None:
                return "N/A"
            
            # Handle dict objects (the main culprit of format errors)
            if isinstance(value, dict):
                return "N/A"
            
            # Handle list/tuple objects
            if isinstance(value, (list, tuple)):
                return "N/A"
            
            # Handle string objects
            if isinstance(value, str):
                try:
                    float_value = float(value)
                    if pd.isna(float_value) or np.isnan(float_value) or np.isinf(float_value):
                        return "N/A"
                    return f"{float_value:.{precision}f}"
                except (ValueError, TypeError):
                    return "N/A"
            
            # Handle numeric types
            if isinstance(value, (int, float)):
                if pd.isna(value) or np.isnan(value) or np.isinf(value):
                    return "N/A"
                return f"{value:.{precision}f}"
            
            # Try to convert to float as last resort
            float_value = float(value)
            if pd.isna(float_value) or np.isnan(float_value) or np.isinf(float_value):
                return "N/A"
            return f"{float_value:.{precision}f}"
            
        except (ValueError, TypeError, AttributeError, OverflowError):
            return "N/A"

    def _safe_get_raw_value(self, data_dict, key, default=None):
        """
        SAFE RAW VALUE GETTER
        Gets raw numeric value for calculations, returns default if invalid
        """
        try:
            if not isinstance(data_dict, dict):
                return default
            
            value = data_dict.get(key, default)
            
            # If it's a dict (error object), return default
            if isinstance(value, dict):
                return default
            
            # If it's None, return default
            if value is None:
                return default
            
            # Try to convert to float for calculations
            if isinstance(value, (int, float)):
                if pd.isna(value) or np.isnan(value) or np.isinf(value):
                    return default
                return value
            
            # Try string conversion
            if isinstance(value, str):
                try:
                    float_val = float(value)
                    if pd.isna(float_val) or np.isnan(float_val) or np.isinf(float_val):
                        return default
                    return float_val
                except (ValueError, TypeError):
                    return default
            
            return default
            
        except Exception:
            return default

    def _safe_get(self, data_dict, key, precision=2, default="N/A"):
        """
        SAFE FORMATTED VALUE GETTER
        Gets and formats value safely, returns formatted string or default
        """
        try:
            raw_value = self._safe_get_raw_value(data_dict, key)
            if raw_value is None:
                return default
            return self._format_value(raw_value, precision)
        except Exception:
            return default

    def _safe_get_nested(self, data_dict, keys, precision=2, default="N/A"):
        """
        SAFE NESTED VALUE GETTER
        Safely gets nested dictionary values like data['macd']['macd']
        """
        try:
            if not isinstance(data_dict, dict):
                return default
            
            current = data_dict
            for key in keys:
                if not isinstance(current, dict):
                    return default
                current = current.get(key)
                if current is None:
                    return default
            
            return self._format_value(current, precision)

        except Exception:
            return default

    def generate_v5_prompt(self, symbol: str, data_1: dict, data_4: dict, data_1d: dict,
                          analysis_summary: dict, key_values: dict) -> str:
        """
        ROBUST V5 PROMPT GENERATOR
        Generates AI prompt with complete error resistance
        """
        try:
            # Safe symbol handling
            safe_symbol = str(symbol) if symbol else "UNKNOWN"

            # Build prompt parts safely
            prompt_parts = []

            # Header
            prompt_parts.append("🔥 ANALISIS TEKNIKAL MENDALAM 🔥")
            prompt_parts.append("Pair: " + safe_symbol)
            prompt_parts.append("")

            # Market Data Section
            prompt_parts.append("💰 DATA PASAR:")
            prompt_parts.append("• Harga: $" + self._safe_get(key_values, 'price_close', 8))
            prompt_parts.append("• Volume 24h: " + self._safe_get(key_values, 'volume_24h', 0))
            prompt_parts.append("• Perubahan 24h: " + self._safe_get(key_values, 'price_change_24h', 2) + "%")
            prompt_parts.append("")

            # Technical Indicators - 1 Hour
            prompt_parts.append("📈 INDIKATOR TEKNIKAL (1H):")
            prompt_parts.append("• RSI: " + self._safe_get(data_1, 'rsi', 2))
            prompt_parts.append("• MACD: " + self._safe_get_nested(data_1, ['macd', 'macd'], 6))
            prompt_parts.append("• MACD Signal: " + self._safe_get_nested(data_1, ['macd', 'signal'], 6))
            prompt_parts.append("• MACD Histogram: " + self._safe_get_nested(data_1, ['macd', 'histogram'], 6))
            prompt_parts.append("• StochRSI Fast K: " + self._safe_get_nested(data_1, ['stochrsi', 'k'], 2))
            prompt_parts.append("• StochRSI Fast D: " + self._safe_get_nested(data_1, ['stochrsi', 'd'], 2))
            prompt_parts.append("• ADX: " + self._safe_get(data_1, 'adx', 2))
            prompt_parts.append("• ATR: " + self._safe_get(data_1, 'atr', 6))
            prompt_parts.append("")

            # Technical Indicators - 4 Hour
            prompt_parts.append("📊 INDIKATOR TEKNIKAL (4H):")
            prompt_parts.append("• RSI: " + self._safe_get(data_4, 'rsi', 2))
            prompt_parts.append("• MACD: " + self._safe_get_nested(data_4, ['macd', 'macd'], 6))
            prompt_parts.append("• MACD Signal: " + self._safe_get_nested(data_4, ['macd', 'signal'], 6))
            prompt_parts.append("• StochRSI Fast K: " + self._safe_get_nested(data_4, ['stochrsi', 'k'], 2))
            prompt_parts.append("• StochRSI Fast D: " + self._safe_get_nested(data_4, ['stochrsi', 'd'], 2))
            prompt_parts.append("• ADX: " + self._safe_get(data_4, 'adx', 2))
            prompt_parts.append("")

            # Technical Indicators - Daily
            prompt_parts.append("📅 INDIKATOR TEKNIKAL (1D):")
            prompt_parts.append("• RSI: " + self._safe_get(data_1d, 'rsi', 2))
            prompt_parts.append("• MACD: " + self._safe_get_nested(data_1d, ['macd', 'macd'], 6))
            prompt_parts.append("• MACD Signal: " + self._safe_get_nested(data_1d, ['macd', 'signal'], 6))
            prompt_parts.append("• StochRSI Fast K: " + self._safe_get_nested(data_1d, ['stochrsi', 'k'], 2))
            prompt_parts.append("• StochRSI Fast D: " + self._safe_get_nested(data_1d, ['stochrsi', 'd'], 2))
            prompt_parts.append("")

            # Moving Averages
            prompt_parts.append("📈 MOVING AVERAGES:")
            prompt_parts.append("• EMA 9: $" + self._safe_get(key_values, 'ema_9', 6))
            prompt_parts.append("• EMA 20: $" + self._safe_get(key_values, 'ema_20', 6))
            prompt_parts.append("• EMA 50: $" + self._safe_get(key_values, 'ema_50', 6))
            prompt_parts.append("• SMA 200: $" + self._safe_get(key_values, 'sma_200', 6))
            prompt_parts.append("")

            # Support and Resistance
            prompt_parts.append("🎯 SUPPORT & RESISTANCE:")
            prompt_parts.append("• Fibonacci Support: $" + self._safe_get(key_values, 'fibonacci_support', 6))
            prompt_parts.append("• Fibonacci Resistance: $" + self._safe_get(key_values, 'fibonacci_resistance', 6))
            prompt_parts.append("• Volume POC: $" + self._safe_get(key_values, 'volume_poc', 6))
            prompt_parts.append("")

            # Analysis Summary
            if isinstance(analysis_summary, dict):
                reasons = analysis_summary.get('reasons', [])
                if isinstance(reasons, list) and reasons:
                    prompt_parts.append("🔍 ANALISIS SUMMARY:")
                    for reason in reasons[:5]:  # Limit to 5 reasons
                        if isinstance(reason, str):
                            prompt_parts.append("• " + str(reason))
                    prompt_parts.append("")

            # Join all parts
            prompt = "\n".join(prompt_parts)

            # Add footer
            prompt += "\n🤖 Berdasarkan data teknikal di atas, berikan analisis mendalam dan rekomendasi trading yang akurat!"
            prompt += "\n\n📝 Generated by IntelliTrader X V5 Enhanced AI"

            return prompt

        except Exception as e:
            # Fallback prompt if everything fails
            return "Error generating prompt for " + str(symbol) + ": " + str(e)
