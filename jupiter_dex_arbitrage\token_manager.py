"""
Token manager module for Jupiter DEX Arbitrage Analyzer
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any

import aiohttp

import config
import utils

logger = logging.getLogger("jupiter_arbitrage")

class TokenManager:
    """Manages token information and filtering"""

    def __init__(self, session: aiohttp.ClientSession):
        self.session = session
        self.tokens_cache: Dict[str, Any] = {}
        self.tradable_tokens: List[str] = []
        self.verified_tokens: List[str] = []
        self.sol_price_usd: Optional[float] = None

    async def initialize(self) -> None:
        """Initialize token manager by fetching necessary token data"""
        logger.info("Initializing token manager...")

        # Get SOL price in USD
        await self.update_sol_price()

        # Get tradable tokens
        await self.fetch_tradable_tokens()

        # Get verified tokens
        await self.fetch_verified_tokens()

        logger.info(f"Token manager initialized. Found {len(self.tradable_tokens)} tradable tokens and {len(self.verified_tokens)} verified tokens.")

    async def update_sol_price(self) -> None:
        """Update SOL price in USD"""
        try:
            url = f"{config.JUPITER_PRICE_API_URL}?ids={config.SOL_MINT}"
            async with self.session.get(url, timeout=config.REQUEST_TIMEOUT) as response:
                if response.status == 200:
                    data = await response.json()
                    if data and "data" in data and config.SOL_MINT in data["data"]:
                        self.sol_price_usd = float(data["data"][config.SOL_MINT]["price"])
                        logger.info(f"SOL price updated: ${self.sol_price_usd:.2f}")
                    else:
                        logger.error("Failed to parse SOL price from response")
                else:
                    logger.error(f"Failed to get SOL price. Status: {response.status}")
        except Exception as e:
            logger.error(f"Error updating SOL price: {str(e)}")
            self.sol_price_usd = None

    async def fetch_tradable_tokens(self) -> None:
        """Fetch list of tradable tokens from Jupiter API"""
        try:
            url = f"{config.JUPITER_TOKEN_API_URL}/mints/tradable"
            async with self.session.get(url, timeout=config.REQUEST_TIMEOUT) as response:
                if response.status == 200:
                    self.tradable_tokens = await response.json()
                    logger.info(f"Fetched {len(self.tradable_tokens)} tradable tokens")
                else:
                    logger.error(f"Failed to fetch tradable tokens. Status: {response.status}")
        except Exception as e:
            logger.error(f"Error fetching tradable tokens: {str(e)}")
            self.tradable_tokens = []

    async def fetch_verified_tokens(self) -> None:
        """Fetch list of verified tokens from Jupiter API"""
        try:
            url = f"{config.JUPITER_TOKEN_API_URL}/tagged/verified"
            async with self.session.get(url, timeout=config.REQUEST_TIMEOUT) as response:
                if response.status == 200:
                    tokens_data = await response.json()
                    self.verified_tokens = [token["address"] for token in tokens_data]

                    # Also cache the token info
                    for token in tokens_data:
                        self.tokens_cache[token["address"]] = token

                    logger.info(f"Fetched {len(self.verified_tokens)} verified tokens")
                else:
                    logger.error(f"Failed to fetch verified tokens. Status: {response.status}")
        except Exception as e:
            logger.error(f"Error fetching verified tokens: {str(e)}")
            self.verified_tokens = []

    async def get_token_info(self, token_mint: str) -> Optional[Dict[str, Any]]:
        """Get token information for a specific token mint"""
        # Return from cache if available
        if token_mint in self.tokens_cache:
            return self.tokens_cache[token_mint]

        try:
            url = f"{config.JUPITER_TOKEN_API_URL}/token/{token_mint}"
            async with self.session.get(url, timeout=config.REQUEST_TIMEOUT) as response:
                if response.status == 200:
                    token_info = await response.json()
                    self.tokens_cache[token_mint] = token_info
                    return token_info
                else:
                    logger.warning(f"Failed to get token info for {token_mint}. Status: {response.status}")
                    return None
        except Exception as e:
            logger.error(f"Error getting token info for {token_mint}: {str(e)}")
            return None

    async def get_token_price(self, token_mint: str) -> Optional[float]:
        """Get token price in USD"""
        try:
            url = f"{config.JUPITER_PRICE_API_URL}?ids={token_mint}"
            async with self.session.get(url, timeout=config.REQUEST_TIMEOUT) as response:
                if response.status == 200:
                    data = await response.json()
                    if data and "data" in data and token_mint in data["data"]:
                        return float(data["data"][token_mint]["price"])
                    else:
                        logger.warning(f"No price data found for token {token_mint}")
                        return None
                else:
                    logger.warning(f"Failed to get price for {token_mint}. Status: {response.status}")
                    return None
        except Exception as e:
            logger.error(f"Error getting price for {token_mint}: {str(e)}")
            return None

    async def get_batch_token_prices(self, token_mints: List[str]) -> Dict[str, Optional[float]]:
        """Get prices for multiple tokens in a single request"""
        result = {}

        if not token_mints:
            return result

        try:
            # Split into batches of 100 (API limit)
            batch_size = 100
            for i in range(0, len(token_mints), batch_size):
                batch = token_mints[i:i+batch_size]
                ids_param = ",".join(batch)

                url = f"{config.JUPITER_PRICE_API_URL}?ids={ids_param}"
                async with self.session.get(url, timeout=config.REQUEST_TIMEOUT) as response:
                    if response.status == 200:
                        data = await response.json()
                        if data and "data" in data:
                            for token_mint in batch:
                                if token_mint in data["data"]:
                                    result[token_mint] = float(data["data"][token_mint]["price"])
                                else:
                                    result[token_mint] = None
                    else:
                        logger.warning(f"Failed to get batch prices. Status: {response.status}")
                        for token_mint in batch:
                            result[token_mint] = None
        except Exception as e:
            logger.error(f"Error getting batch token prices: {str(e)}")
            for token_mint in token_mints:
                result[token_mint] = None

        return result

    def filter_tokens(self, tokens_info: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Filter tokens based on liquidity, volume, and other criteria"""
        filtered_tokens = []

        for token_info in tokens_info:
            # Skip if token info is None
            if token_info is None:
                continue

            # Skip if token is SOL or USDC
            if token_info.get("address") in [config.SOL_MINT, config.USDC_MINT]:
                continue

            # Get token details
            token_address = token_info.get("address")
            token_symbol = token_info.get("symbol", "???")
            daily_volume = token_info.get("daily_volume")
            tags = token_info.get("tags", [])

            # Check daily volume with more lenient criteria for verified tokens
            if token_address in self.verified_tokens:
                # For verified tokens, use the configured minimum volume
                if daily_volume is None or daily_volume < config.MIN_VOLUME_USD:
                    logger.debug(f"Skipping verified token {token_symbol}: Low volume (${daily_volume if daily_volume else 0})")
                    continue
            else:
                # For non-verified tokens, apply stricter filters
                if daily_volume is None or daily_volume < config.MIN_VOLUME_USD * 2:
                    logger.debug(f"Skipping non-verified token {token_symbol}: Low volume (${daily_volume if daily_volume else 0})")
                    continue

            # Check for suspicious tags
            if any(tag in ["scam", "fake", "honeypot"] for tag in tags):
                logger.debug(f"Skipping token {token_symbol}: Suspicious tag found in {tags}")
                continue

            # Skip stablecoins (they usually don't have good arbitrage opportunities)
            if "stablecoin" in tags or any(stable in token_symbol.lower() for stable in ["usd", "usdt", "usdc", "dai", "busd"]):
                logger.debug(f"Skipping token {token_symbol}: Likely a stablecoin")
                continue

            # Add token to filtered list
            filtered_tokens.append(token_info)

        # Sort by volume (descending) to prioritize high-volume tokens
        filtered_tokens.sort(key=lambda x: x.get("daily_volume", 0), reverse=True)

        return filtered_tokens

    async def get_filtered_tokens(self, limit: int = 200) -> List[Dict[str, Any]]:
        """Get filtered list of tokens suitable for arbitrage"""
        # Ensure we have tradable tokens
        if not self.tradable_tokens:
            await self.fetch_tradable_tokens()

        # If we still don't have tradable tokens, use verified tokens directly
        selected_tokens = []
        if not self.tradable_tokens and self.verified_tokens:
            logger.info("Using verified tokens as tradable tokens not available")
            # Use verified tokens directly, limited to the requested amount
            selected_tokens = self.verified_tokens[:limit]
        else:
            # Prioritize verified tokens with high volume
            # Get token info for top verified tokens to check volume
            top_verified_tokens = [mint for mint in self.tradable_tokens if mint in self.verified_tokens][:300]

            if top_verified_tokens:
                logger.info(f"Getting info for {len(top_verified_tokens)} verified tokens to check volume")
                token_info_tasks = [self.get_token_info(mint) for mint in top_verified_tokens]
                verified_tokens_info = await asyncio.gather(*token_info_tasks)

                # Sort by volume (descending)
                verified_tokens_info = [t for t in verified_tokens_info if t is not None]
                # Handle None values in daily_volume
                for token in verified_tokens_info:
                    if token.get("daily_volume") is None:
                        token["daily_volume"] = 0
                verified_tokens_info.sort(key=lambda x: x.get("daily_volume", 0), reverse=True)

                # Take top tokens by volume
                high_volume_tokens = [t.get("address") for t in verified_tokens_info[:limit]]
                selected_tokens = high_volume_tokens

                # If we need more tokens, add some non-verified ones
                if len(selected_tokens) < limit:
                    other_tokens = [mint for mint in self.tradable_tokens if mint not in self.verified_tokens]
                    selected_tokens.extend(other_tokens[:limit - len(selected_tokens)])
            else:
                # Fallback to original method
                priority_tokens = [mint for mint in self.tradable_tokens if mint in self.verified_tokens]
                other_tokens = [mint for mint in self.tradable_tokens if mint not in self.verified_tokens]

                # Combine and limit
                selected_tokens = priority_tokens[:limit]
                if len(selected_tokens) < limit:
                    selected_tokens.extend(other_tokens[:limit - len(selected_tokens)])

        # Get token info for selected tokens (if not already fetched)
        token_info_tasks = [self.get_token_info(mint) for mint in selected_tokens]
        tokens_info = await asyncio.gather(*token_info_tasks)

        # Filter tokens
        filtered_tokens = self.filter_tokens(tokens_info)

        logger.info(f"Selected {len(filtered_tokens)} tokens for arbitrage analysis")
        return filtered_tokens
