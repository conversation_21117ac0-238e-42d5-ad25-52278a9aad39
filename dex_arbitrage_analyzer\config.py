"""
Konfigurasi untuk program analisis arbitrase DEX.
"""

from additional_tokens_final import ALL_ADDITIONAL_TOKENS
from additional_tokens_small_cap import SMALL_CAP_TOKENS
from trending_tokens_2024 import TRENDING_TOKENS_2024

# Konfigurasi umum
DEFAULT_CAPITAL = 37  # Modal default dalam USD (± Rp 1.500.000)
TOP_OPPORTUNITIES = 15  # Jumlah peluang teratas yang ditampi<PERSON>an (ditingkatkan untuk melihat lebih banyak peluang)
RATE_LIMIT_DELAY = 0.7  # Delay antar request API dalam detik (ditingkatkan untuk menghindari rate limit)
MAX_CONCURRENT_REQUESTS = 5  # Jumlah maksimum request API bersamaan (dikurangi untuk menghindari rate limit)
USE_ASYNC = True  # Gunakan pengambilan data asinkron
MAX_RETRIES = 3  # Jumlah maksimum percobaan ulang jika terjadi rate limit
BACKOFF_FACTOR = 2.0  # <PERSON>aktor penggandaan delay saat retry

# Daftar query token untuk pencarian
TOKEN_QUERIES = [
    # Major Cryptocurrencies
    "ETH", "BTC", "USDC", "USDT", "DAI", "WBTC", "AVAX", "MATIC", "BNB",
    "DOT", "LINK", "UNI", "AAVE", "SUSHI", "CAKE", "CRV", "YFI", "SNX", "COMP",
    "MKR", "BAL", "1INCH", "PERP", "ALPHA", "BADGER", "RUNE", "LUNA", "FTM", "ATOM",

    # Layer 1 & Layer 2 Tokens
    "ADA", "XRP", "DOGE", "LTC", "TRX", "EOS", "XLM", "XTZ", "ALGO", "NEAR",
    "HBAR", "ONE", "EGLD", "FLOW", "XMR", "ZEC", "DASH", "BCH", "BSV", "ETC",
    "ZIL", "QTUM", "ICX", "ONT", "WAVES", "KSM", "KAVA", "CELO", "MINA", "ICP",
    "AR", "FIL", "XDC", "THETA", "VET", "HIVE", "STEEM", "SC", "DGB", "RVN",
    "KDA", "CFX", "ROSE", "CSPR", "OP", "ARB", "IMX", "ZKS", "METIS", "BOBA",

    # DeFi Tokens
    "LIDO", "LDO", "AAVE", "CAKE", "UNI", "SUSHI", "COMP", "MKR", "YFI", "SNX",
    "CRV", "CVX", "BAL", "REN", "RUNE", "LUNA", "OSMO", "SCRT", "INJ", "KAVA",
    "ANC", "ASTRO", "JUNO", "STARS", "KUJI", "RAY", "SRM", "FIDA", "MNGO", "ORCA",
    "JOE", "SPELL", "TIME", "BIFI", "QI", "QUICK", "DFYN", "DYDX", "PERP", "GMX",
    "GNS", "APE", "VELA", "PENDLE", "DODO", "VELO", "BEETS", "SOLID", "SPIRIT", "LQTY",

    # Stablecoins & Wrapped Tokens
    "USDC", "USDT", "DAI", "BUSD", "TUSD", "USDD", "USDP", "GUSD", "LUSD", "FRAX",
    "WETH", "WBTC", "WBNB", "WAVAX", "WMATIC", "WFTM", "WONE", "WGLMR", "WMOVR",

    # Gaming & Metaverse
    "AXS", "SAND", "MANA", "ENJ", "GALA", "ILV", "YGG", "ALICE", "TLM", "HERO",
    "ATLAS", "POLIS", "STAR", "SPS", "GODS", "IMX", "WAXP", "UFO", "RFOX", "BLOK",
    "WILD", "CWAR", "DERC", "LOKA", "MAGIC", "MC", "REALM", "SIDUS", "FALCONS", "GAFI",

    # NFT & Social Tokens
    "APE", "BAYC", "MAYC", "BAKC", "AZUKI", "DOODLE", "COOL", "PUNKS", "MEEBITS", "LOOKS",
    "BLUR", "X2Y2", "RARE", "SUPER", "CHZ", "RARI", "OGN", "AUDIO", "RAD", "MASK",

    # DAO & Governance
    "ENS", "GTC", "TRIBE", "INDEX", "BANK", "INST", "TOKE", "FOLD", "CREAM", "BOND",
    "API3", "DYDX", "PERP", "DODO", "BADGER", "PICKLE", "ALPHA", "ROOK", "FARM", "ALCX",

    # Emerging Sectors
    "GRT", "OCEAN", "NMR", "FET", "AGIX", "RNDR", "ANKR", "STORJ", "FIL", "AR",
    "HNT", "MOBILE", "IOT", "IOTA", "XYO", "POKT", "FLUX", "GLM", "RLC", "KEEP",
    "NKN", "NU", "PRQ", "POND", "ALEPH", "DUSK", "SCRT", "ORAI", "ROSE", "PHALA",

    # Exchange Tokens
    "BNB", "CRO", "FTT", "KCS", "OKB", "HT", "GT", "LEO", "NEXO", "CEL",
    "VGX", "ASD", "WOO", "DYDX", "SRM", "PERP", "DODO", "ZRX", "LRC", "BAKE",

    # Meme Coins
    "DOGE", "SHIB", "ELON", "FLOKI", "SAMO", "HOGE", "CATE", "KISHU", "AKITA", "LEASH",
    "BABYDOGE", "SAFEMOON", "DOGELON", "CORGI", "HUSKY", "KUMA", "SAITAMA", "MONONOKE", "TAMA", "CUMMIES",

    # Yield Farming & Liquidity
    "FARM", "PICKLE", "ALPHA", "ROOK", "ALCX", "BIFI", "QI", "QUICK", "DFYN", "SUSHI",
    "CAKE", "UNI", "BAL", "CRV", "CVX", "YFI", "AAVE", "COMP", "MKR", "SNX",

    # Cross-Chain & Interoperability
    "ATOM", "DOT", "KSM", "RUNE", "REN", "LUNA", "OSMO", "SCRT", "INJ", "KAVA",
    "ANC", "ASTRO", "JUNO", "STARS", "KUJI",

    # Privacy Coins
    "XMR", "ZEC", "DASH", "SCRT", "ROSE", "PHALA", "KEEP", "NU", "DUSK", "BEAM",
    "GRIN", "MWC", "ARRR", "XHV", "FIRO", "ZEN", "XVG", "PIVX", "NAV", "PART",

    # Trending Tokens
    "PEPE", "BOME", "TURBO", "POPCAT", "MOG", "TOSHI", "BRETT", "SLERF",
    "TRUMP", "MAGA", "DYM", "SEI", "SUI", "PYTH", "STRK", "ZETA", "ONDO",
    "ETHFI", "CYBER", "NEIRO", "PIXEL", "TOAD", "DEGEN", "MEME", "WOJAK", "COMET", "PUMP",

    # Additional Tokens - Part 1
    "AERO", "BLUR", "BLUR", "BOBA", "BONE", "BTRST", "C98", "CELR", "CHR", "CLV",
    "COTI", "CTSI", "CTX", "CUBE", "DAR", "DREP", "DUSK", "EDEN", "ERN",
    "FLOKI", "FORT", "GALA", "GAL", "GHST", "GLMR", "HIFI", "HIGH", "HOOK", "HOPR",
    "ILV", "JASMY", "KP3R", "LINA", "LIT", "LOKA", "LOOM", "LPT", "LRC", "MAGIC",
    "MBOX", "MEDIA", "MINA", "MLN", "MOVR", "MTL", "NEST", "NKN", "NULS", "OGN",
    "OMG", "OOKI", "ORN", "PAXG", "PEOPLE", "PERL", "PLA", "POWR", "PROM", "QNT",
    "RAD", "RARE", "REEF", "RGT", "RIF", "RLC", "RNDR", "RSR", "SANTOS", "SHPING",
    "SKL", "SLP", "SNT", "STMX", "STX", "SUN", "SUPER", "SUSHI", "SXP", "TLM",
    "TRB", "TROY", "TRU", "TVK", "TWT", "UFT", "UNFI", "UTK", "VOXEL", "WAXP",
    "WNCG", "WOO", "XEC", "XEM", "XNO", "XVG", "XVS", "YGG", "ZEN", "ZRX",

    # Additional Tokens - Part 2
    "1INCH", "AAVE", "ACH", "ADA", "AGIX", "AGLD", "AIOZ", "AKRO", "ALCX", "ALGO",
    "ALICE", "ALPACA", "ALPHA", "ALPINE", "AMP", "ANKR", "ANT", "APE", "API3", "APT",
    "ARB", "ARPA", "ASR", "ASTR", "ATA", "ATM", "ATOM", "AUCTION", "AUDIO", "AUTO",

    # Additional Tokens - Part 3 (New Trending Tokens 2024)
    "BOME", "BRETT", "CAT", "COMET", "CYBER", "DEGEN", "DOGS", "ETHENA", "ETHFI", "FRIEND",
    "GALA", "GROK", "HPOS", "JESUS", "KIZUNA", "LADYS", "MAGA", "MEME", "MILEI", "MOG",
    "NEIRO", "NOTCOIN", "ONDO", "PIXEL", "POPCAT", "PYTH", "RNDR", "SLERF", "STRK", "TOSHI",
    "TRUMP", "TURBO", "WOJAK", "ZETA", "BOOK", "BALD", "BEPE", "BODEN",

    # Additional Tokens - Part 4 (Popular DeFi Tokens)
    "AURA", "BALANCER", "CONVEX", "CURVE", "FRAX", "GAMMA", "LIDO", "MAKER", "PENDLE", "RADIANT",
    "STARGATE", "SYNTHETIX", "VELODROME", "YEARN", "ANGLE", "BEEFY", "BENQI", "COMPOUND", "DOPEX", "ELLIPSIS",
    "GEIST", "GMXV2", "HASHFLOW", "INSTADAPP", "JONES", "KWENTA", "LYRA", "MORPHO", "NOTIONAL", "OLYMPUS",
    "PARASWAP", "QIDAO", "RIBBON", "SADDLE", "TAROT", "UMAMI", "VECTOR", "WOMBAT", "XTOKEN", "YIELDYAK",

    # Additional Tokens - Part 5 (Popular Chain-Specific Tokens)
    "APTOS", "BASE", "BLAST", "CELESTIA", "COSMOS", "CRONOS", "EVMOS", "GNOSIS", "HARMONY", "INJECTIVE",
    "JUNO", "KAVA", "LINEA", "MANTLE", "NEAR", "OSMOSIS", "POLKADOT", "QUANT", "RONIN", "STARKNET",
    "TEZOS", "THORCHAIN", "UMEE", "VELAS", "WEMIX", "XDAI", "ZETACHAIN", "AGORIC", "BIFROST", "CANTO",
    "DYDX", "EIGENLAYER", "FUEL", "GRAVITY", "HEDERA", "INTERNET", "KASPA", "LISK", "MANTA",
    "NERVOS", "OASIS", "PERSISTENCE", "QUICKSILVER", "RADIX", "SCROLL", "TAIKO", "ULTRON", "VENOM",
]

# Tambahkan token-token tambahan dari additional_tokens_final.py
TOKEN_QUERIES.extend(ALL_ADDITIONAL_TOKENS)

# Tambahkan token small cap dengan potensi arbitrase tinggi
TOKEN_QUERIES.extend(SMALL_CAP_TOKENS)

# Tambahkan token trending terbaru 2024
TOKEN_QUERIES.extend(TRENDING_TOKENS_2024)

# Hapus duplikat
TOKEN_QUERIES = list(set(TOKEN_QUERIES))

# Log jumlah token yang digunakan
print(f"Total token queries: {len(TOKEN_QUERIES)}")

# Filter validasi
MIN_LIQUIDITY_USD = 9000.0  # Likuiditas minimum dalam USD (diturunkan lebih lanjut untuk menemukan lebih banyak peluang)
MAX_LIQUIDITY_RATIO = 10000.0  # Rasio maksimum antara likuiditas pool beli dan jual (ditingkatkan untuk menemukan lebih banyak peluang)
MAX_TRADE_TO_LIQUIDITY_RATIO = 1.0  # Rasio maksimum antara ukuran trade dan likuiditas (ditingkatkan untuk menemukan lebih banyak peluang)
MIN_PRICE_DIFFERENCE_PERCENTAGE = 0.01  # Perbedaan harga minimum dalam persen (diturunkan untuk menemukan lebih banyak peluang)
MAX_PRICE_DIFFERENCE_PERCENTAGE = 1000.0  # Perbedaan harga maksimum dalam persen (ditingkatkan untuk menemukan lebih banyak peluang)
MIN_PROFIT_USD = 0.001  # Profit minimum dalam USD (diturunkan untuk menemukan lebih banyak peluang)
MAX_ROI_PERCENTAGE = 10000.0  # ROI maksimum dalam persen (ditingkatkan untuk menemukan lebih banyak peluang)
SKIP_SAME_DEX_OPPORTUNITIES = True  # Izinkan peluang di DEX yang sama untuk menemukan lebih banyak peluang
SKIP_STABLECOIN_PAIRS = False  # Izinkan peluang antara stablecoin untuk menemukan lebih banyak peluang
MAX_STABLECOIN_PRICE_DIFF = 50.0  # Perbedaan harga maksimum untuk stablecoin (ditingkatkan untuk menemukan lebih banyak peluang)
SUSPICIOUS_PRICE_THRESHOLD = 1000.0  # Perbedaan harga yang mencurigakan (ditingkatkan untuk menemukan lebih banyak peluang)
STRICT_STABLECOIN_FILTERING = False  # Nonaktifkan filter stablecoin yang lebih ketat untuk menemukan lebih banyak peluang

# Validasi token aktif
VALIDATE_TOKEN_ACTIVE = False  # Validasi apakah token aktif diperdagangkan (dinonaktifkan untuk menemukan lebih banyak peluang)
MIN_TRADE_VOLUME_USD = 1.0  # Volume perdagangan minimum dalam 24 jam (USD) (diturunkan untuk menemukan lebih banyak peluang)
MIN_UPDATED_AT_MINUTES = 1440  # Token harus diperbarui dalam 24 jam terakhir (diperlonggar untuk menemukan lebih banyak peluang)
VERIFY_PAIR_EXISTS = False  # Nonaktifkan verifikasi pasangan untuk menemukan lebih banyak peluang

# Konfigurasi stablecoin
STABLECOIN_ADDRESSES = {
    "ethereum": [
        "******************************************",  # USDT
        "******************************************",  # USDC
        "******************************************",  # DAI
    ],
    "bsc": [
        "******************************************",  # USDT
        "******************************************",  # USDC
        "******************************************",  # DAI
    ],
    "polygon": [
        "******************************************",  # USDT
        "******************************************",  # USDC
        "******************************************",  # DAI
    ],
    "avalanche": [
        "******************************************",  # USDT
        "******************************************",  # USDC
        "******************************************",  # DAI
    ],
    "arbitrum": [
        "******************************************",  # USDT
        "******************************************",  # USDC
        "0xda10009cbd5d07dd0cecc66161fc93d7c9000da1",  # DAI
    ],
    "optimism": [
        "0x94b008aa00579c1307b0ef2c499ad98a8ce58e58",  # USDT
        "0x7f5c764cbc14f9669b88837ca1490cca17c31607",  # USDC
        "0xda10009cbd5d07dd0cecc66161fc93d7c9000da1",  # DAI
    ],
    "fantom": [
        "0x049d68029688eabf473097a2fc38ef61633a3c7a",  # USDT
        "******************************************",  # USDC
        "******************************************",  # DAI
    ],
}

STABLECOIN_PRICE_TOLERANCE = 0.1  # Toleransi harga stablecoin (ditingkatkan dari 5% ke 10%)
STABLECOIN_PRICE_DIFF_TOLERANCE = 1.0  # Toleransi perbedaan harga stablecoin (ditingkatkan dari 0.5% ke 1.0%)

# Estimasi biaya gas per chain (dalam USD) - diturunkan untuk menemukan lebih banyak peluang
ESTIMATED_GAS_COSTS = {
    "ethereum": 3.0,
    "bsc": 0.2,
    "polygon": 0.05,
    "avalanche": 0.1,
    "arbitrum": 0.1,
    "optimism": 0.1,
    "fantom": 0.05,
}

# Estimasi biaya bridge antar chain (dalam USD)
ESTIMATED_BRIDGE_COSTS_USD = {
    ("ethereum", "polygon"): 3.0,
    ("ethereum", "arbitrum"): 3.0,
    ("ethereum", "optimism"): 3.0,
    ("ethereum", "bsc"): 5.0,
    ("ethereum", "avalanche"): 5.0,
    ("ethereum", "fantom"): 5.0,
    ("polygon", "ethereum"): 10.0,
    ("polygon", "bsc"): 3.0,
    ("polygon", "avalanche"): 3.0,
    ("polygon", "arbitrum"): 3.0,
    ("polygon", "optimism"): 3.0,
    ("polygon", "fantom"): 3.0,
    ("bsc", "ethereum"): 10.0,
    ("bsc", "polygon"): 3.0,
    ("bsc", "avalanche"): 3.0,
    ("bsc", "arbitrum"): 5.0,
    ("bsc", "optimism"): 5.0,
    ("bsc", "fantom"): 3.0,
    ("avalanche", "ethereum"): 10.0,
    ("avalanche", "polygon"): 3.0,
    ("avalanche", "bsc"): 3.0,
    ("avalanche", "arbitrum"): 5.0,
    ("avalanche", "optimism"): 5.0,
    ("avalanche", "fantom"): 3.0,
    ("arbitrum", "ethereum"): 5.0,
    ("arbitrum", "polygon"): 3.0,
    ("arbitrum", "bsc"): 5.0,
    ("arbitrum", "avalanche"): 5.0,
    ("arbitrum", "optimism"): 3.0,
    ("arbitrum", "fantom"): 5.0,
    ("optimism", "ethereum"): 5.0,
    ("optimism", "polygon"): 3.0,
    ("optimism", "bsc"): 5.0,
    ("optimism", "avalanche"): 5.0,
    ("optimism", "arbitrum"): 3.0,
    ("optimism", "fantom"): 5.0,
    ("fantom", "ethereum"): 10.0,
    ("fantom", "polygon"): 3.0,
    ("fantom", "bsc"): 3.0,
    ("fantom", "avalanche"): 3.0,
    ("fantom", "arbitrum"): 5.0,
    ("fantom", "optimism"): 5.0,
}

# Default bridge cost jika tidak ada dalam kamus - diturunkan untuk menemukan lebih banyak peluang
DEFAULT_BRIDGE_COST_USD = 7.0

# Fee DEX default jika tidak tersedia dari API - diturunkan untuk menemukan lebih banyak peluang
DEFAULT_DEX_FEE_PERCENTAGE = 0.3  # Diturunkan dari 0.3% ke 0.2%

# Kurs IDR ke USD
IDR_TO_USD_RATE = 16900  # Kurs 1 USD = 16900 IDR (perkiraan)

# Konfigurasi slippage
DEFAULT_SLIPPAGE_PERCENTAGE = 3.0  # Slippage default dalam persen (diturunkan dari 5% ke 3%)
USE_FIXED_SLIPPAGE = True  # Gunakan slippage tetap daripada menghitung berdasarkan likuiditas

# Konfigurasi deduplikasi peluang
REMOVE_DUPLICATE_OPPORTUNITIES = True  # Hapus peluang duplikat
SIMILARITY_THRESHOLD = 0.5  # Ambang batas kesamaan untuk menentukan duplikasi (diturunkan untuk mendeteksi lebih banyak duplikat)
TOKEN_PAIR_SIMILARITY_WEIGHT = 0.7  # Bobot kesamaan pasangan token (ditingkatkan karena ini faktor paling penting)
DEX_SIMILARITY_WEIGHT = 0.2  # Bobot kesamaan DEX
PRICE_SIMILARITY_WEIGHT = 0.1  # Bobot kesamaan harga
ENABLE_DEDUPLICATION_LOGGING = True  # Aktifkan logging deduplikasi untuk debugging

# Konfigurasi verifikasi token
STRICT_TOKEN_VERIFICATION = True  # Aktifkan verifikasi token yang lebih ketat
VERIFY_TOKEN_ADDRESS = True  # Verifikasi alamat kontrak token jika di chain yang sama
VERIFY_TOKEN_NAME = True  # Verifikasi nama token
VERIFY_TOKEN_DECIMALS = True  # Verifikasi decimals token
NAME_SIMILARITY_THRESHOLD = 0.8  # Ambang batas kesamaan nama token (0.0-1.0)
ENABLE_TOKEN_VERIFICATION_LOGGING = True  # Aktifkan logging verifikasi token untuk debugging

# Konfigurasi filter token dengan issue
FILTER_TOKENS_WITH_ISSUES = True  # Aktifkan filter token dengan issue
ENABLE_ISSUE_DETECTION_LOGGING = True  # Aktifkan logging deteksi issue untuk debugging

# Daftar issue token yang diketahui (berdasarkan flag di Dexscreener)
TOKEN_ISSUES = [
    "honeypot",           # Token yang tidak dapat dijual (honeypot)
    "scam",               # Token yang diidentifikasi sebagai scam
    "blacklisted",        # Token yang masuk daftar hitam
    "high_tax",           # Token dengan pajak transaksi tinggi
    "abandoned",          # Proyek yang ditinggalkan
    "locked_liquidity",   # Likuiditas terkunci
    "mint_function",      # Fungsi mint yang dapat disalahgunakan
    "proxy_contract",     # Kontrak proxy yang dapat diubah
    "rebase",             # Token dengan mekanisme rebase
    "transfer_paused",    # Transfer token dinonaktifkan
    "trading_paused",     # Perdagangan token dinonaktifkan
    "ownership_not_renounced", # Kepemilikan kontrak tidak dilepaskan
    "high_risk",          # Token berisiko tinggi
    "fake_token",         # Token palsu
    "impersonation",      # Token yang meniru token lain
    "rugpull_risk",       # Risiko rugpull
    "suspicious_contract", # Kontrak mencurigakan
    "no_liquidity",       # Tidak ada likuiditas
    "low_liquidity",      # Likuiditas rendah
    "extreme_volatility", # Volatilitas ekstrem
    "flash_loan_attack",  # Serangan flash loan
    "price_manipulation", # Manipulasi harga
]

# Daftar token yang diketahui memiliki simbol yang sama tetapi berbeda
# Format: {simbol: [nama_lengkap1, nama_lengkap2, ...]}
AMBIGUOUS_TOKENS = {
    "GOLD": ["Gold Coin", "Golden Token", "Gold Standard", "Digital Gold"],
    "SILVER": ["Silver Coin", "Silver Token", "Digital Silver"],
    "BTC": ["Bitcoin", "Bitcoin BEP2", "Bitcoin BEP20", "Wrapped Bitcoin"],
    "ETH": ["Ethereum", "Ethereum BEP2", "Ethereum BEP20", "Wrapped Ethereum"],
    "USDT": ["Tether", "Tether USD", "Tether BEP2", "Tether BEP20", "Wrapped USDT"],
    "USDC": ["USD Coin", "USD Coin BEP2", "USD Coin BEP20", "Wrapped USDC"],
    "BUSD": ["Binance USD", "Binance USD BEP2", "Binance USD BEP20"],
    "DAI": ["Dai", "Dai Stablecoin", "Dai BEP2", "Dai BEP20", "Wrapped DAI"],
    "SHIB": ["Shiba Inu", "Shiba Inu BEP2", "Shiba Inu BEP20", "Wrapped SHIB"],
    "DOGE": ["Dogecoin", "Dogecoin BEP2", "Dogecoin BEP20", "Wrapped DOGE"],
    "PEPE": ["Pepe", "Pepe Coin", "Pepe Token", "Wrapped PEPE"],
    "FLOKI": ["Floki", "Floki Inu", "Floki Token", "Wrapped FLOKI"],
    "CAKE": ["PancakeSwap", "PancakeSwap Token", "Cake Token"],
    "UNI": ["Uniswap", "Uniswap Token", "Uni Token"],
    "SUSHI": ["SushiSwap", "SushiSwap Token", "Sushi Token"],
    "AAVE": ["Aave", "Aave Token", "Aave Interest Bearing Token"],
    "LINK": ["Chainlink", "Chainlink Token", "Link Token"],
    "CRV": ["Curve DAO Token", "Curve Finance", "Curve Token"],
    "BAL": ["Balancer", "Balancer Token", "Bal Token"],
    "MATIC": ["Polygon", "Polygon Token", "Matic Token", "Wrapped MATIC"],
    "FTM": ["Fantom", "Fantom Token", "Wrapped FTM"],
    "AVAX": ["Avalanche", "Avalanche Token", "Wrapped AVAX"],
    "BNB": ["Binance Coin", "Binance Token", "Wrapped BNB"],
}

# Cetak jumlah token yang digunakan
print(f"Jumlah token yang digunakan: {len(TOKEN_QUERIES)}")
