# Cryptocurrency Arbitrage Finder

A comprehensive Python program that functions as a real-time cryptocurrency arbitrage opportunity finder across 20 centralized exchanges (CEXs). The program continuously runs, identifying and reporting potential profit opportunities after accounting for all related fees.

## Features

- Connects to WebSocket APIs of 20 different cryptocurrency exchanges to get real-time market data
- Uses public APIs only (no authentication required)
- Normalizes data from different exchanges into a standard format
- Identifies cross-exchange arbitrage opportunities
- Calculates accurate profit after accounting for:
  - Trading fees (taker fees)
  - Withdrawal fees
  - Network compatibility
- Runs continuously and reports potential opportunities in real-time

## Supported Exchanges

The program supports the following exchanges:

1. Binance
2. Coinbase Pro (Advanced Trade)
3. Kraken
4. Bybit
5. OKX
6. KuCoin
7. <PERSON><PERSON><PERSON> (HTX)
8. Gate.io
9. Bitget
10. MEXC
11. Bitstamp
12. Bitfinex
13. Crypto.com Exchange
14. Phemex
15. AscendEX (BitMax)
16. LBank
17. BingX
18. WhiteBIT
19. CoinEx
20. Deepcoin

## Project Structure

```
crypto_arbitrage_finder/
├── config/
│   ├── endpoints.json         # WebSocket endpoints for exchanges
│   └── fees_networks.json     # Trading fees, withdrawal fees, and network information
├── src/
│   ├── clients/               # Exchange-specific WebSocket clients
│   │   ├── __init__.py
│   │   ├── base_client.py     # Base class for all exchange clients
│   │   ├── binance_client.py  # Binance WebSocket client
│   │   └── client_factory.py  # Factory for creating exchange clients
│   ├── core/                  # Core functionality
│   │   ├── __init__.py
│   │   ├── arbitrage_finder.py # Main arbitrage finding logic
│   │   └── data_processor.py   # Data normalization and processing
│   ├── utils/                 # Utility functions
│   │   ├── __init__.py
│   │   ├── config_loader.py   # Configuration loading utilities
│   │   └── logger_setup.py    # Logging configuration
│   └── __init__.py
├── logs/                      # Log files (created at runtime)
├── main.py                    # Entry point for the application
├── requirements.txt           # Python dependencies
└── README.md                  # Project documentation
```

## Installation

1. Clone the repository:
   ```
   git clone https://github.com/yourusername/crypto_arbitrage_finder.git
   cd crypto_arbitrage_finder
   ```

2. Create a virtual environment (optional but recommended):
   ```
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. Install the dependencies:
   ```
   pip install -r requirements.txt
   ```

## Usage

Run the program:

```
python main.py
```

The program will:
1. Connect to the WebSocket APIs of the supported exchanges
2. Subscribe to order book data for common trading pairs
3. Continuously scan for arbitrage opportunities
4. Log potential opportunities to the console and log files

## Arbitrage Calculation

The program calculates potential profit for cross-exchange arbitrage as follows:

1. Buy asset X on Exchange A (with lower ask price)
2. Transfer asset X from Exchange A to Exchange B (accounting for withdrawal fees)
3. Sell asset X on Exchange B (with higher bid price)

The profit calculation includes:
- Taker fees on both exchanges
- Withdrawal fees for transferring the asset
- Verification of network compatibility between exchanges

## Logging

The program logs information to both the console and log files in the `logs` directory. Log files are named with timestamps for easy tracking.

## Extending the Program

To add support for additional exchanges:

1. Create a new client class in `src/clients/` that extends `BaseClient`
2. Implement the required methods for the new exchange
3. Add the exchange to the `ClientFactory` class
4. Update the configuration files with the new exchange's information

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Disclaimer

This software is for educational purposes only. Use it at your own risk. The authors are not responsible for any financial losses incurred from using this software. Cryptocurrency trading involves significant risk and you should never invest more than you can afford to lose.

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.
