#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Cryptocurrency Arbitrage Finder
-------------------------------
This program connects to WebSocket APIs of 20 different cryptocurrency exchanges,
collects real-time market data, and identifies arbitrage opportunities.
"""

import asyncio
import logging
import os
from rich.console import Console
from src.core.arbitrage_finder import ArbitrageFinder
from src.ui.main_ui import MainUI
from src.utils.logger_setup import setup_logger
from src.utils.display_utils import clear_screen

# Create logs directory if it doesn't exist
os.makedirs('logs', exist_ok=True)

# Setup logger
logger = setup_logger()

async def main():
    """Main entry point for the application."""
    # Create rich console
    console = Console()

    # Clear the screen
    clear_screen()

    # Display startup message
    console.print("[bold green]Starting Cryptocurrency Arbitrage Finder...[/bold green]")
    logger.info("Starting Cryptocurrency Arbitrage Finder...")

    # Create ArbitrageFinder instance
    arbitrage_finder = ArbitrageFinder()

    # Create MainUI instance
    ui = MainUI(arbitrage_finder)

    try:
        # Start the UI
        await ui.start()
    except asyncio.CancelledError:
        pass
    except KeyboardInterrupt:
        console.print("\n[bold yellow]Program interrupted by user. Exiting...[/bold yellow]")
        logger.info("Program interrupted by user. Exiting...")
    except Exception as e:
        console.print(f"\n[bold red]Unexpected error: {e}[/bold red]")
        logger.exception(f"Unexpected error: {e}")
    finally:
        # Stop the UI
        await ui.stop()
        console.print("[bold green]Thank you for using Cryptocurrency Arbitrage Finder![/bold green]")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        # This should be caught by the try/except in main()
        pass
