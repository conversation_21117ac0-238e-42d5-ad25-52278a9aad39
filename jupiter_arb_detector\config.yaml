# Konfigurasi untuk Jupiter Arbitrage Detector

# Pengaturan umum
settings:
  check_interval_seconds: 10  # Seber<PERSON> sering memer<PERSON><PERSON> peluang (lebih sering untuk menangkap peluang cepat)
  log_level: "INFO"  # DEBUG, INFO, WARNING, ERROR
  slippage_percentage: 0.5  # Persentase slippage yang digunakan dalam perhitungan (0.5% lebih realistis)
  min_profit_percentage: 0.5  # Persentase profit minimum (0.5% untuk menangkap lebih banyak peluang)
  base_token_amount: 1.0  # Jumlah SOL untuk simulasi swap

  # Pengaturan API
  api_base_url: "https://quote-api.jup.ag"  # API endpoint yang lebih baru dan akurat
  token_api_url: "/v6/tokens"  # v6 API untuk token
  price_api_url: "/v6/price"  # v6 API untuk harga
  quote_api_url: "/v6/quote"  # v6 API untuk quote

  # Pengaturan token
  base_token: "So11111111111111111111111111111111111111112"  # SOL
  usdc_token: "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"  # USDC

  # Pengaturan filter
  max_tokens: 1000  # Jumlah maksimum token untuk dianalisis (lebih banyak token)
  min_daily_volume: 10000  # Volume harian minimum dalam USD (lebih tinggi untuk token yang lebih likuid)
  min_liquidity: 5000  # Likuiditas minimum dalam USD
  exclude_tags: ["meme", "scam", "test", "deprecated"]  # Tag token yang akan dilewati
  include_only_verified: true  # Hanya token terverifikasi

  # Pengaturan swap
  max_slippage_bps: 50  # Slippage maksimum dalam basis poin (0.5%)
  fee_buffer_percentage: 0.3  # Buffer untuk biaya transaksi (0.3%)
  gas_buffer_percentage: 0.2  # Buffer untuk biaya gas (0.2%)

  # Pengaturan validasi
  validate_liquidity: true  # Validasi likuiditas token
  validate_price_impact: true  # Validasi dampak harga
  max_price_impact_percentage: 1.0  # Dampak harga maksimum (1%)

  # Pengaturan cache
  cache_ttl_seconds: 60  # Time-to-live untuk cache (1 menit, lebih pendek untuk data yang lebih segar)

  # Pengaturan HTTP
  request_timeout_seconds: 15  # Timeout untuk permintaan HTTP (lebih lama untuk menunggu respons)
  max_retries: 5  # Jumlah maksimum percobaan ulang untuk permintaan HTTP (lebih banyak percobaan)
  retry_delay_seconds: 2  # Waktu tunggu antara percobaan ulang (lebih lama untuk menghindari rate limiting)

  # Pengaturan rute
  only_direct_routes: false  # Izinkan rute tidak langsung
  max_route_hops: 2  # Jumlah maksimum hop dalam rute

  # Pengaturan analisis
  analyze_historical_data: true  # Analisis data historis
  historical_window_minutes: 30  # Jendela waktu untuk data historis (30 menit)

  # Pengaturan notifikasi
  enable_notifications: true  # Aktifkan notifikasi
  min_profit_for_notification: 1.0  # Profit minimum untuk notifikasi (1%)
