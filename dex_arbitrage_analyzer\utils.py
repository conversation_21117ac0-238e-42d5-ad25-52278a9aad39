"""
Modul utilitas untuk program analisis arbitrase DEX.
"""

import logging
from typing import Dict, List, Any, Optional
import time
import json
from datetime import datetime

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("utils")

def format_address(address: str, length: int = 8) -> str:
    """
    Memformat alamat kontrak untuk tampilan yang lebih pendek.
    
    Args:
        address: Alamat kontrak.
        length: Panjang karakter yang ditampilkan di awal dan akhir.
        
    Returns:
        Alamat yang diformat.
    """
    if not address or len(address) <= length * 2:
        return address
    
    return f"{address[:length]}...{address[-length:]}"

def format_price(price: float, decimals: int = 6) -> str:
    """
    Memformat harga dengan jumlah desimal yang ditentukan.
    
    Args:
        price: Harga yang akan diformat.
        decimals: Jumlah desimal.
        
    Returns:
        Harga yang diformat.
    """
    if price is None:
        return "N/A"
    
    if price < 0.000001:
        return f"{price:.8e}"
    
    return f"{price:.{decimals}f}"

def format_percentage(percentage: float, decimals: int = 2) -> str:
    """
    Memformat persentase dengan jumlah desimal yang ditentukan.
    
    Args:
        percentage: Persentase yang akan diformat.
        decimals: Jumlah desimal.
        
    Returns:
        Persentase yang diformat.
    """
    if percentage is None:
        return "N/A"
    
    return f"{percentage:.{decimals}f}%"

def format_usd(amount: float, decimals: int = 2) -> str:
    """
    Memformat jumlah USD dengan jumlah desimal yang ditentukan.
    
    Args:
        amount: Jumlah USD yang akan diformat.
        decimals: Jumlah desimal.
        
    Returns:
        Jumlah USD yang diformat.
    """
    if amount is None:
        return "N/A"
    
    return f"${amount:.{decimals}f}"

def format_idr(amount: float, decimals: int = 0) -> str:
    """
    Memformat jumlah IDR dengan jumlah desimal yang ditentukan.
    
    Args:
        amount: Jumlah IDR yang akan diformat.
        decimals: Jumlah desimal.
        
    Returns:
        Jumlah IDR yang diformat.
    """
    if amount is None:
        return "N/A"
    
    return f"Rp {amount:,.{decimals}f}"

def get_dexscreener_pair_url(pair: Dict[str, Any]) -> str:
    """
    Mendapatkan URL Dexscreener untuk pasangan token.
    
    Args:
        pair: Data pasangan token.
        
    Returns:
        URL Dexscreener.
    """
    return pair.get("url", "")

def save_opportunities_to_file(opportunities: List[Dict[str, Any]], filename: str) -> None:
    """
    Menyimpan peluang arbitrase ke file JSON.
    
    Args:
        opportunities: List peluang arbitrase.
        filename: Nama file.
    """
    try:
        with open(filename, 'w') as f:
            json.dump(opportunities, f, indent=2)
        logger.info(f"Peluang berhasil disimpan ke {filename}")
    except Exception as e:
        logger.error(f"Error saat menyimpan peluang ke file: {e}")

def load_opportunities_from_file(filename: str) -> List[Dict[str, Any]]:
    """
    Memuat peluang arbitrase dari file JSON.
    
    Args:
        filename: Nama file.
        
    Returns:
        List peluang arbitrase.
    """
    try:
        with open(filename, 'r') as f:
            opportunities = json.load(f)
        logger.info(f"Peluang berhasil dimuat dari {filename}")
        return opportunities
    except Exception as e:
        logger.error(f"Error saat memuat peluang dari file: {e}")
        return []

def generate_timestamp() -> str:
    """
    Menghasilkan timestamp untuk penamaan file.
    
    Returns:
        String timestamp.
    """
    return datetime.now().strftime("%Y%m%d_%H%M%S")

def calculate_statistics(
    all_pairs: List[Dict[str, Any]],
    same_chain_opportunities: List[Dict[str, Any]],
    cross_chain_opportunities: List[Dict[str, Any]]
) -> Dict[str, Any]:
    """
    Menghitung statistik penyaringan.
    
    Args:
        all_pairs: Semua pasangan token yang diambil.
        same_chain_opportunities: Peluang arbitrase sesama chain.
        cross_chain_opportunities: Peluang arbitrase beda chain.
        
    Returns:
        Dict berisi statistik.
    """
    return {
        "total_pairs": len(all_pairs),
        "unique_chains": len(set(pair.get("chainId", "") for pair in all_pairs)),
        "unique_dexes": len(set(pair.get("dexId", "") for pair in all_pairs)),
        "same_chain_opportunities": len(same_chain_opportunities),
        "cross_chain_opportunities": len(cross_chain_opportunities),
        "total_opportunities": len(same_chain_opportunities) + len(cross_chain_opportunities),
        "timestamp": datetime.now().isoformat()
    }

def get_execution_instructions(opportunity: Dict[str, Any]) -> List[str]:
    """
    Menghasilkan instruksi eksekusi manual untuk peluang arbitrase.
    
    Args:
        opportunity: Data peluang arbitrase.
        
    Returns:
        List instruksi eksekusi.
    """
    instructions = []
    
    if opportunity["type"] == "same_chain":
        chain_id = opportunity["chain_id"]
        base_symbol = opportunity["base_token"]["symbol"]
        quote_symbol = opportunity["quote_token"]["symbol"]
        buy_dex = opportunity["buy_dex"]
        sell_dex = opportunity["sell_dex"]
        
        instructions = [
            f"1. Hubungkan wallet Anda ke jaringan {chain_id.upper()}.",
            f"2. Kunjungi DEX {buy_dex.upper()} (gunakan tautan Dexscreener untuk menemukan URL resmi).",
            f"3. Swap {opportunity['capital']} USD worth of {quote_symbol} untuk mendapatkan {base_symbol}.",
            f"4. Setelah transaksi selesai, kunjungi DEX {sell_dex.upper()}.",
            f"5. Swap semua {base_symbol} yang Anda dapatkan kembali ke {quote_symbol}.",
            "6. Verifikasi bahwa Anda menerima jumlah yang diharapkan setelah biaya."
        ]
    else:  # cross_chain
        source_chain = opportunity["source_chain"]
        target_chain = opportunity["target_chain"]
        base_symbol = opportunity["base_token"]["symbol"]
        quote_symbol = opportunity["quote_token"]["symbol"]
        buy_dex = opportunity["buy_dex"]
        sell_dex = opportunity["sell_dex"]
        
        instructions = [
            f"1. Hubungkan wallet Anda ke jaringan {source_chain.upper()}.",
            f"2. Kunjungi DEX {buy_dex.upper()} (gunakan tautan Dexscreener untuk menemukan URL resmi).",
            f"3. Swap {opportunity['capital']} USD worth of {quote_symbol} untuk mendapatkan {base_symbol}.",
            f"4. Gunakan bridge yang mendukung {base_symbol} dari {source_chain.upper()} ke {target_chain.upper()}.",
            "   PENTING: Verifikasi bahwa token dapat di-bridge sebelum melanjutkan!",
            f"5. Setelah token tiba di jaringan {target_chain.upper()}, kunjungi DEX {sell_dex.upper()}.",
            f"6. Swap semua {base_symbol} yang Anda dapatkan kembali ke {quote_symbol}.",
            "7. Verifikasi bahwa Anda menerima jumlah yang diharapkan setelah biaya."
        ]
    
    # Tambahkan informasi biaya
    instructions.append("\nINFORMASI BIAYA:")
    instructions.append(f"- Biaya Slippage: {format_usd(opportunity['slippage_cost'])} ({format_percentage(opportunity['buy_slippage'])} beli, {format_percentage(opportunity['sell_slippage'])} jual)")
    instructions.append(f"- Fee DEX Beli: {format_usd(opportunity['buy_fee'])}")
    instructions.append(f"- Fee DEX Jual: {format_usd(opportunity['sell_fee'])}")

    if opportunity["type"] == "same_chain":
        instructions.append(f"- Biaya Gas: {format_usd(opportunity['gas_cost'])}")
    else:  # cross_chain
        instructions.append(f"- Biaya Gas (Source): {format_usd(opportunity['source_gas_cost'])}")
        instructions.append(f"- Biaya Gas (Target): {format_usd(opportunity['target_gas_cost'])}")
        instructions.append(f"- Biaya Bridge: {format_usd(opportunity['bridge_cost'])}")

    instructions.append(f"- Total Biaya: {format_usd(opportunity['total_cost'])}")
    instructions.append(f"- Profit Bersih: {format_usd(opportunity['net_profit'])} ({format_percentage(opportunity['roi_percentage'])} ROI)")

    # Tambahkan peringatan
    instructions.append("\nPERINGATAN PENTING:")
    instructions.append("- Selalu verifikasi alamat kontrak resmi untuk menghindari scam.")
    instructions.append("- Harga dapat berubah dengan cepat, peluang mungkin tidak lagi menguntungkan.")
    instructions.append("- Estimasi biaya dan slippage adalah perkiraan dan dapat berbeda dalam praktiknya.")
    instructions.append("- Lakukan riset mandiri sebelum melakukan transaksi apa pun.")
    
    return instructions
