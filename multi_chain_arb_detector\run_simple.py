"""
File sederhana untuk menjalankan program.
"""
import os
import sys
import yaml

def main():
    """
    Fungsi utama untuk menjalankan program.
    """
    print("Menjalankan program sederhana...")
    
    # Dapatkan direktori script
    script_dir = os.path.dirname(os.path.abspath(__file__))
    print(f"Direktori script: {script_dir}")
    
    # Cek apakah file config.yaml ada
    config_path = os.path.join(script_dir, "config.yaml")
    print(f"Path konfigurasi: {config_path}")
    
    if os.path.exists(config_path):
        print("File config.yaml ditemukan!")
        
        # Coba memuat konfigurasi
        try:
            with open(config_path, 'r') as file:
                config = yaml.safe_load(file)
            print("Konfigurasi berhasil dimuat.")
            
            # Tampilkan jaringan yang dikonfigurasi
            print("Jaringan yang Dikonfigurasi:")
            for network_name in config['networks'].keys():
                print(f"  • {network_name}")
            
            # Coba terhubung ke jaringan Ethereum
            print("\nMencoba terhubung ke Ethereum...")
            
            # Impor Web3
            from web3 import Web3
            
            # Dapatkan RPC URL Ethereum
            ethereum_config = config['networks']['ethereum']
            rpc_url = ethereum_config['rpc_url']
            print(f"RPC URL: {rpc_url}")
            
            # Inisialisasi Web3
            w3 = Web3(Web3.HTTPProvider(rpc_url))
            
            # Cek koneksi
            if w3.is_connected():
                print("Berhasil terhubung ke Ethereum!")
                
                # Dapatkan nomor blok terbaru
                latest_block = w3.eth.block_number
                print(f"Nomor blok terbaru: {latest_block}")
                
                # Dapatkan harga gas
                gas_price = w3.eth.gas_price
                print(f"Harga gas: {gas_price} wei")
                
                print("\nProgram berjalan dengan baik!")
            else:
                print("Gagal terhubung ke Ethereum.")
        except Exception as e:
            print(f"Error: {e}")
            import traceback
            traceback.print_exc()
    else:
        print("File config.yaml tidak ditemukan!")

if __name__ == "__main__":
    main()
