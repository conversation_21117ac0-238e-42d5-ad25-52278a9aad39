# 🚀 Binance Hybrid Signal AI (Async)

<div align="center">

![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)
![PySide6](https://img.shields.io/badge/PySide6-GUI-green.svg)
![CCXT](https://img.shields.io/badge/CCXT-Async-orange.svg)
![License](https://img.shields.io/badge/License-MIT-yellow.svg)

**Advanced Multi-Timeframe Trading Signal Generator untuk Binance Futures**

*Menggunakan 60+ Technical Indicators, Smart Money Concepts, dan Async Processing*

</div>

## ✨ Fitur Utama

### 🎯 **Analisis Multi-Timeframe**
- **3 Timeframe Simultan**: 1h (HTF), 30m (MTF), 15m (LTF)
- **Weighted Confirmation**: HTF 50%, MTF 30%, LTF 20%
- **300 Candles** per timeframe untuk analisis mendalam

### 📊 **Technical Analysis Canggih**
- **60+ Indikator** dari TA library (RSI, MACD, Bollinger Bands, ADX, dll)
- **Smart Money Concepts**: Order Blocks, Fair Value Gaps (FVG), Divergences
- **Custom Weighting** berdasarkan kategori indikator
- **Advanced Pattern Detection**

### 🧠 **Sistem Sinyal Hybrid**
- **5 Level Sinyal**: KUAT BELI, BELI, NETRAL, JUAL, KUAT JUAL
- **Confidence Scoring** dengan threshold filtering
- **Real-time Consolidation** multi-timeframe

### ⚡ **Performa Optimal**
- **Async Processing** dengan CCXT Async
- **ThreadPoolExecutor** dengan 10 workers
- **Network Timeout Protection**
- **Graceful Error Handling**

### 🎨 **UI Modern & Responsif**
- **Futuristic Design** dengan warna pastel ungu
- **Progress Tracking** dengan ETR (Estimated Time Remaining)
- **Collapsible Cards** untuk detail analisis
- **AI Prompt Generator** untuk setiap sinyal

### 📝 **Enhanced Logging**
- **Dual Output**: Console + GUI + File
- **Real-time Progress** monitoring
- **Professional Format** dengan emoji
- **Error Tracking** yang komprehensif

## 🚀 Quick Start

### 1. **Installation**
```bash
# Clone atau download folder ini
cd binance_hybrid_signal_ai

# Install dependencies (otomatis)
python run.py --install

# Atau manual
pip install -r requirements.txt
```

### 2. **Menjalankan Program**
```bash
# Method 1: Python launcher (recommended)
python run.py

# Method 2: Direct run
python "binance3timeframe (1).py"

# Method 3: Windows batch (double-click)
run.bat
```

### 3. **Troubleshooting & Testing**
```bash
# Cek dependencies
python run.py --check

# Troubleshooting guide
python run.py --troubleshoot

# Demo mode (tanpa koneksi exchange)
python run.py --demo

# Test logging
python run.py --test
```

## 🚫 Geo-blocking Issues?

### **Jika mendapat Error 451:**
```
ExchangeNotAvailable - Service unavailable from a restricted location
```

### **Solusi Cepat:**
1. **🌐 Gunakan VPN** (ExpressVPN, NordVPN)
2. **🔄 Program otomatis coba exchange alternatif** (Bybit, OKX)
3. **🎭 Gunakan demo mode** untuk testing: `python run.py --demo`

### **Panduan Lengkap:**
Baca file `TROUBLESHOOTING.md` untuk solusi detail!

## 📊 Output Examples

### **Console Output**
```
================================================================================
🚀 BINANCE HYBRID SIGNAL AI (ASYNC) - STARTING UP
================================================================================
2025-05-28 01:30:25,448 - INFO - MainThread - 📊 Fitur: Multi-timeframe analysis, Smart Money Concepts, 60+ Technical Indicators
2025-05-28 01:30:25,448 - INFO - MainThread - ⚡ Engine: Async processing dengan ThreadPoolExecutor untuk performa optimal

2025-05-28 01:30:25,466 - INFO - MainThread - 🎯 SINYAL DITEMUKAN: BTCUSDT - KUAT BELI (Score: 8.50)
2025-05-28 01:30:25,466 - INFO - MainThread - 🎯 SINYAL DITEMUKAN: ETHUSDT - BELI (Score: 6.20)

2025-05-28 01:30:25,466 - INFO - MainThread - 📊 RINGKASAN ANALISIS HYBRID ENGINE ASYNC:
2025-05-28 01:30:25,466 - INFO - MainThread -    • Total pairs dianalisa: 156
2025-05-28 01:30:25,466 - INFO - MainThread -    • Sinyal kuat & confident: 5
2025-05-28 01:30:25,466 - INFO - MainThread - ✅ ANALISIS HYBRID ENGINE ASYNC SELESAI!
```

### **GUI Interface**
- Modern card-based layout
- Real-time progress bar dengan ETR
- Detailed signal cards dengan confidence scores
- AI prompt generator untuk setiap sinyal

## 🔧 Konfigurasi

### **Konstanta Utama**
```python
MAX_PAIRS_DISPLAY = 7                    # Maksimal sinyal yang ditampilkan
CANDLE_LIMIT_PER_TIMEFRAME = 300        # Jumlah candle per timeframe
TIMEFRAMES_TO_ANALYZE = ['1h', '30m', '15m']  # Timeframe yang dianalisa
MAX_WORKERS_ANALYSIS = 10               # Jumlah worker threads
```

### **Threshold Sinyal**
```python
SIGNAL_SCORES = {
    "Sangat Bullish": 2, "Bullish": 1, "Netral": 0,
    "Bearish": -1, "Sangat Bearish": -2,
    "Bullish OB Strong": 2.5, "Bearish OB Strong": -2.5,
    # ... dan lainnya
}
```

## 📁 Struktur File

```
binance_hybrid_signal_ai/
├── 📄 Core Program
│   ├── binance3timeframe (1).py          # Program utama (Enhanced)
│   ├── binance_signal_prompt_generator.py # AI prompt generator
│   ├── test_logging.py                   # Test logging
│   └── config.py                         # Konfigurasi exchange (BARU)
│
├── 📚 Documentation
│   ├── README.md                         # Dokumentasi lengkap
│   ├── TROUBLESHOOTING.md                # Panduan troubleshooting (BARU)
│   ├── LOGGING_IMPROVEMENTS.md           # Detail logging changes
│   ├── README_binance_signal.md          # Dokumentasi teknis
│   └── MIGRATION_SUMMARY.md              # Summary migration
│
├── ⚙️ Setup & Runners
│   ├── requirements.txt                  # Python dependencies
│   ├── run.py                           # Python launcher (Enhanced)
│   ├── run_demo.py                      # Demo mode runner (BARU)
│   ├── run.bat                          # Windows launcher
│   └── install.bat                      # Windows installer
│
└── 📝 Logs & Data
    └── test_binance_signals.log          # Sample log file
```

## 🎯 Cara Kerja

1. **Koneksi**: Program terhubung ke Binance Futures via CCXT Async
2. **Data Fetching**: Mengambil data OHLCV untuk semua timeframe secara async
3. **Technical Analysis**: Menghitung 60+ indikator menggunakan TA library
4. **Smart Money Analysis**: Deteksi Order Blocks, FVG, dan Divergences
5. **Signal Consolidation**: Menggabungkan sinyal dari semua timeframe
6. **Confidence Scoring**: Memberikan skor confidence untuk setiap sinyal
7. **Output**: Menampilkan top sinyal di GUI dan console

## 🛠️ Troubleshooting

### **Common Issues**
- **Import Error**: Pastikan semua dependencies terinstall
- **Network Timeout**: Periksa koneksi internet
- **GUI Not Showing**: Pastikan PySide6 terinstall dengan benar

### **Logging**
- Console output untuk monitoring real-time
- File log (`binance_signals.log`) untuk debugging
- GUI log area untuk user-friendly display

## 📈 Performance Tips

- Program menggunakan async processing untuk speed optimal
- ThreadPoolExecutor dengan 10 workers untuk parallel analysis
- Network timeout protection untuk stability
- Memory-efficient data handling

## 🤝 Contributing

Contributions welcome! Please:
1. Fork the repository
2. Create feature branch
3. Make changes
4. Test thoroughly
5. Submit pull request

## 📄 License

MIT License - Feel free to use and modify

## 🙏 Credits

- **CCXT**: Cryptocurrency exchange connectivity
- **TA Library**: Technical analysis indicators
- **PySide6**: Modern GUI framework
- **Pandas**: Data manipulation and analysis

---

<div align="center">

**🌟 Happy Trading! 🌟**

*"HYBRID Engine Async Signal by Bobacheese - Trade with Insight!"*

</div>
