"""
Konfigurasi untuk Jupiter DEX Arbitrage Analyzer
"""

# API Endpoints
JUPITER_BASE_URL = "https://quote-api.jup.ag/v6"
JUPITER_PRICE_API_URL = "https://lite-api.jup.ag/price/v2"
JUPITER_TOKEN_API_URL = "https://lite-api.jup.ag/tokens/v1"

# Token Configuration
SOL_MINT = "So11111111111111111111111111111111111111112"
USDC_MINT = "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"

# Arbitrage Configuration
MIN_PROFIT_PERCENTAGE = 0.01  # Minimum profit percentage to consider
CAPITAL_AMOUNT_SOL = 1.0  # Amount of SOL to use for arbitrage
SLIPPAGE_BPS = 50  # 0.5% slippage
SLIPPAGE_COST_PERCENTAGE = 0.2  # 0.2% slippage cost for profit calculation

# Token Filter Configuration
MIN_LIQUIDITY_USD = 1000  # Minimum liquidity in USD
MIN_VOLUME_USD = 500  # Minimum 24h volume in USD
MAX_PRICE_DIFFERENCE_PERCENTAGE = 50  # Maximum price difference percentage

# Request Configuration
REQUEST_TIMEOUT = 30  # Timeout for API requests in seconds
MAX_RETRIES = 3  # Maximum number of retries for API requests
BATCH_SIZE = 10  # Number of tokens to process in parallel

# Display Configuration
MAX_OPPORTUNITIES_TO_DISPLAY = 20  # Maximum number of opportunities to display
REFRESH_INTERVAL = 300  # Refresh interval in seconds (5 minutes)
