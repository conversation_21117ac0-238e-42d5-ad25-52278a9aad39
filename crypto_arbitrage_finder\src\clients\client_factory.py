#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Factory for creating exchange WebSocket clients.
"""

import json
import logging
import os
from .binance_client import BinanceClient
from .kucoin_client import KuCoinClient
# Import other exchange clients as they are implemented

class ClientFactory:
    """Factory for creating exchange WebSocket clients."""

    def __init__(self, logger=None):
        """
        Initialize the client factory.

        Args:
            logger (logging.Logger, optional): Logger instance
        """
        self.logger = logger or logging.getLogger('crypto_arbitrage.client_factory')
        self.endpoints = self._load_endpoints()
        self.clients = {}

    def _load_endpoints(self):
        """
        Load WebSocket endpoints from configuration file.

        Returns:
            dict: Dictionary of exchange endpoints
        """
        try:
            # Use absolute path for config directory
            base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            config_path = os.path.join(base_dir, 'config', 'endpoints.json')
            with open(config_path, 'r') as f:
                endpoints = json.load(f)

            self.logger.info(f"Loaded endpoints for {len(endpoints)} exchanges")
            return endpoints
        except Exception as e:
            self.logger.error(f"Failed to load endpoints: {e}")
            return {}

    def create_client(self, exchange_id):
        """
        Create a WebSocket client for the specified exchange.

        Args:
            exchange_id (str): The ID of the exchange

        Returns:
            BaseClient: An instance of the appropriate exchange client
        """
        if exchange_id in self.clients:
            self.logger.warning(f"Client for {exchange_id} already exists")
            return self.clients[exchange_id]

        if exchange_id not in self.endpoints:
            self.logger.error(f"No endpoint configuration found for {exchange_id}")
            return None

        endpoint = self.endpoints[exchange_id]
        websocket_url = endpoint.get('websocket_url')

        if not websocket_url:
            self.logger.error(f"No WebSocket URL found for {exchange_id}")
            return None

        try:
            # Create the appropriate client based on the exchange ID
            if exchange_id == 'binance':
                client = BinanceClient(websocket_url, self.logger)
            elif exchange_id == 'kucoin':
                client = KuCoinClient(websocket_url, self.logger)
            # For all other exchanges, use BinanceClient as a simulation
            else:
                # Use BinanceClient for all other exchanges (simulation)
                client = BinanceClient(websocket_url, self.logger)
                # Override exchange_id to make it clear this is a different exchange
                client.exchange_id = exchange_id

                # Add some randomness to the simulated exchange data
                # This will be handled in the BaseClient.get_orderbook_data method

            self.clients[exchange_id] = client
            self.logger.info(f"Created client for {exchange_id}")
            return client
        except Exception as e:
            self.logger.error(f"Failed to create client for {exchange_id}: {e}")
            return None

    def get_client(self, exchange_id):
        """
        Get an existing client for the specified exchange.

        Args:
            exchange_id (str): The ID of the exchange

        Returns:
            BaseClient: The exchange client instance, or None if not found
        """
        return self.clients.get(exchange_id)

    def get_all_clients(self):
        """
        Get all created clients.

        Returns:
            dict: Dictionary of exchange clients
        """
        return self.clients

    async def start_all_clients(self):
        """Start all created clients."""
        for exchange_id, client in self.clients.items():
            try:
                await client.start()
            except Exception as e:
                self.logger.error(f"Failed to start client for {exchange_id}: {e}")

    async def stop_all_clients(self):
        """Stop all created clients."""
        for exchange_id, client in self.clients.items():
            try:
                await client.stop()
            except Exception as e:
                self.logger.error(f"Failed to stop client for {exchange_id}: {e}")

    def get_supported_exchanges(self):
        """
        Get a list of supported exchanges.

        Returns:
            list: List of supported exchange IDs
        """
        # List of exchanges that have actual implementations
        implemented_exchanges = ['binance', 'kucoin']

        # Filter the endpoints to only include implemented exchanges
        supported_exchanges = [exchange for exchange in implemented_exchanges if exchange in self.endpoints]

        if not supported_exchanges:
            self.logger.warning("No supported exchanges found in configuration")
            # Default to Binance if no supported exchanges are found
            return ['binance'] if 'binance' in self.endpoints else []

        return supported_exchanges
