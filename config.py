"""
Configuration file for Jupiter Opportunity Hunter.
"""
import os
from dotenv import load_dotenv
from decimal import Decimal

# Load environment variables
load_dotenv()

# Solana RPC endpoint
RPC_URL = os.getenv("RPC_URL", "https://api.mainnet-beta.solana.com")

# Jupiter API endpoints
JUPITER_API_BASE = "https://quote-api.jup.ag/v6"
JUPITER_QUOTE_ENDPOINT = f"{JUPITER_API_BASE}/quote"
JUPITER_TOKENS_ENDPOINT = "https://lite-api.jup.ag/tokens/v1/mints/tradable"
JUPITER_TOKEN_INFO_ENDPOINT = "https://lite-api.jup.ag/tokens/v1/token"
JUPITER_PRICE_ENDPOINT = "https://lite-api.jup.ag/price/v2"

# Arbitrage parameters
# Get MIN_PROFIT_THRESHOLD from environment or use default
profit_threshold_str = os.getenv("MIN_PROFIT_THRESHOLD", "0.001")
try:
    MIN_PROFIT_THRESHOLD = Decimal(profit_threshold_str)  # Minimum profit percentage (0.1%)
except:
    MIN_PROFIT_THRESHOLD = Decimal("0.001")  # Fallback to 0.1%
SLIPPAGE_BPS = int(os.getenv("SLIPPAGE_BPS", "50"))  # 0.5% slippage
MAX_CONCURRENT_REQUESTS = int(os.getenv("MAX_CONCURRENT_REQUESTS", "10"))  # Increased to process more tokens
REQUEST_TIMEOUT = int(os.getenv("REQUEST_TIMEOUT", "30"))  # Increased timeout to 30 seconds
REFRESH_INTERVAL = int(os.getenv("REFRESH_INTERVAL", "10"))  # Increased refresh interval to 10 seconds

# Token amount for quote (in lamports/smallest unit)
QUOTE_AMOUNT = int(os.getenv("QUOTE_AMOUNT", "100000000"))  # 0.1 SOL in lamports (increased for better results)

# UI Configuration
UI_REFRESH_RATE = float(os.getenv("UI_REFRESH_RATE", "1"))  # UI refresh rate in seconds
MAX_OPPORTUNITIES_DISPLAYED = int(os.getenv("MAX_OPPORTUNITIES_DISPLAYED", "10"))

# Token lists
# Base tokens to use for arbitrage (SOL, USDC, etc.)
BASE_TOKENS = [
    "So11111111111111111111111111111111111111112",  # SOL
    "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",  # USDC
    "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB",  # USDT
    "7dHbWXmci3dT8UFYWYZweBLXgycu7Y3iL6trKn1Y7ARj",  # stSOL
    "mSoLzYCxHdYgdzU16g5QSh3i5K3z3KZK7ytfqcJm7So",  # mSOL
    "7vfCXTUXx5WJV5JADk17DUJ4ksgau7utNKj4b963voxs",  # ETH (Wormhole)
    "DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263",  # BONK
    "JUPyiwrYJFskUPiHa7hkeR8VUtAeFoSYbKedZNsDvCN",  # JUP
    "7i5KKsX2weiTkry7jA4ZwSuXGhs5eJBEjY8vVxR4pfRx",  # GMT
    "AFbX8oGjGpmVFywbVouvhQSRmiW2aR1mohfahi4Y2AdB",  # GST
    "kinXdEcpDQeHPEuQnqmUgtYykqKGVFq6CeVX5iAHJq6",  # KIN
    "4k3Dyjzvzp8eMZWUXbBCjEvwSkkk59S5iCNLY3QrkX6R",  # RAY
    "SRMuApVNdxXokk5GT7XD5cUUgXMBCoAz2LHeuAoKWRt",  # SRM
    "orcaEKTdK7LKz57vaAYr9QeNsVEPfiu6QeMU1kektZE",  # ORCA
    "9m21fukdvCPBgEjmeeshbskPwbaa5RH8HTHQGeTnGd2t",  # PANDA
    "J9qyPtHnS4FJSfJ8Xok5oSv8JtyiMEBZTZD44xSJxUae",  # BULLANA
    "7xKXtg2CW87d97TXJSDpbD5jBkheTqA83TZRuJosgAsU",  # SAMO
    "9n4nbM75f5Ui33ZbPYXn59EwSgE8CGsHtAeTH5YFeJ9E",  # soBTC
    "2FPyTwcZLUg1MDrwsyoP4D6s1tM7hAkHYRjkNb5w6Pxk",  # soETH
    "MangoCzJ36AjZyKwVj3VnYU4GTonjfVEnJmvvWaxLac",  # MNGO
    "5oVNBeEEQvYi1cX3ir8Dx5n1P7pdxydbGF2X4TxVusJm",  # INF
    "HmLspvjpQtQEnArUyJoBSFGS38gNJwBuxAeqSV9SZ66K",  # RARE
    "BzjsQpgKjxZedFawUV9GZMExhr7VbvsasQv12v2PVxSt",  # PYTH
    "********************************************",  # XCOPE
    "Saber2gLauYim4Mvftnrasomsv6NvAuncvMEZwcLpD1",  # SBR
    "HZRCwxP2Vq9PCpPXooayhJ2bxTpo5xfpQrwB1svh332p",  # WIF
    "HfYFjMKNZygfMC8LsQ8LtpPsPxEJoXJx4M6tqi75Hajo",  # HADES
    "EchesyfXePKdLtoiZSL8pBe8Myagyy8ZRqsACNCFGnvp",  # FIDA
    "EPeUFDgHRxs9xxEPVaL6kfGQvCon7jmAWKVUHuux1Tpz",  # BAT
    "HxhWkVpk5NS4Ltg5nij2G671CKXFRKPK8vy271Ub4uEK",  # HXRO
    "7xKXtg2CW87d97TXJSDpbD5jBkheTqA83TZRuJosgAsU",  # SAMO
    "4dmKkXNHdgYsXqBHCuMikNQWwVomZURhYvkkX5c4pQ7y",  # SNY
    "StepAscQoEioFxxWGnh2sLBDFp9d8rvKz2Yp39iDpyT",  # STEP
    "DUSTawucrTsGU8hcqRdHDCbuYhCPADMLM2VcCb8VnFnQ",  # DUST
    "Basis9oJw9j8cw53oMV7iqsgo6ihi9ALw4QR31rcjUJa",  # BASIS
    "7dHbWXmci3dT8UFYWYZweBLXgycu7Y3iL6trKn1Y7ARj",  # stSOL
    "7Q2afV64in6N6SeZsAAB81TJzwDoD6zpqmHkzi9Dcavn",  # jSOL
    "bSo13r4TkiE4KumL71LsHTPpL2euBYLFx6h9HP3piy1",  # bSOL
    "7vfCXTUXx5WJV5JADk17DUJ4ksgau7utNKj4b963voxs",  # wETH
    "A9mUU4qviSctJVPJdBJWkb28deg915LYJKrzQ19ji3FM",  # USDCet
    "Dn4noZ5jgGfkntzcQSUZ8czkreiZ1ForXWoVCtq8zJA8",  # USDTet
    "FR87nWEUxVgerFGhZM8Y4AggKGLnaXswr1Pd8wZ4kZcp",  # FRAX
    "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",  # USDC
    "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB",  # USDT
    "AGFEad2et2ZJif9jaGpdMixQqvW5i81aBdvKe7PHNfz3",  # FTT
    "EcQCUYv57C4V6RoPxkVUiDwtX1SP8y8FP5AEToYL8Az",  # DINO
    "EKpQGSJtjMFqKZ9KQanSqYXRcF8fBopzLHYxdM65zcjm",  # WOOF
    "FoRGERiW7odcCBGU1bztZi16osPBHjxharvDathL5eds",  # FORGE
    "HNpdP2rL6FR6ckRJVxdmLdVW8hJyBWqZeRHLwsGbVcDN",  # HONEY
    "4wjPQJ6PrkC4dHhYghwJzGBVP78DkBzA2U3kHoFNBuhj",  # LIQ
    "hntyVP6YFm1Hg25TN9WGLqM12b8TQmcknKrdu1oxWux",  # HNT
    "EsPKhGTMf3bGoy4Qm7pCv3UCcWqAmbC1UGHBTDxRjjD4",  # RENDER
    "5tN42n9vMi6ubp67Uy4NnmM5DMZYN8aS8GeB3bEDHr6E",  # GARI
    "E5rk3nmgLUuKUiS94gg4bpWwWwdUVyk299fVNsJv6pMG",  # SOLAPE
    "5P3giWpPBrVKL8QP8roKM7NsLdi3ie1Nc2b5r9mGtvwb",  # OOGI
    "5fTwKZP2AK39LtFN9Ayppu6hdCVKfMGVm79F2EgHCtsi",  # NANA
    "4ThReWAbAVZjNVgs5Ui9Pk3cZ5TYaD9u6Y89fp6EFzoF",  # SLIM
    "4dmKkXNHdgYsXqBHCuMikNQWwVomZURhYvkkX5c4pQ7y",  # SNY
    "StepAscQoEioFxxWGnh2sLBDFp9d8rvKz2Yp39iDpyT",  # STEP
    "DFL1zNkaGPWm1BwrQQ5vCdTnM3MFMZ1pHHATVtdUDeP",  # DFL
    "HCgybxq5Upy8Mccihrp7EsmwwFqYZtrHrsmsKwtGXLgW",  # STAR
    "METAewgxyPbgwsseH8T16a39CQ5VyVxZi9zXiDPY18m",  # META
    "kiGenopAScF8VF31Zbtx2Hg8qA5ArGqvnVtXb83sotc",  # KI
    "GENEtH5amGSi8kHAtQoezp1XEXwZJ8vcuePYnXdKrMYz",  # GENE
    "CASHVDm2wsJXfhj6VWxb7GiMdoLc17Du7paH4bNr5woT",  # CASH
    "7xKXtg2CW87d97TXJSDpbD5jBkheTqA83TZRuJosgAsU",  # SAMO
    "F9CpWoyeBJfoRB8f2pBe2ZNPbPnSgKEuNyxECmkhRiZB",  # NINJA
    "HovGjrBGTfna4dvg6exkMxXuexB3tUfEZKcut8AWowXj",  # HOVA
    "4wjPQJ6PrkC4dHhYghwJzGBVP78DkBzA2U3kHoFNBuhj",  # LIQ
    "5fTwKZP2AK39LtFN9Ayppu6hdCVKfMGVm79F2EgHCtsi",  # NANA
    "5P3giWpPBrVKL8QP8roKM7NsLdi3ie1Nc2b5r9mGtvwb",  # OOGI
    "E5rk3nmgLUuKUiS94gg4bpWwWwdUVyk299fVNsJv6pMG",  # SOLAPE
    "EsPKhGTMf3bGoy4Qm7pCv3UCcWqAmbC1UGHBTDxRjjD4",  # RENDER
    "hntyVP6YFm1Hg25TN9WGLqM12b8TQmcknKrdu1oxWux",  # HNT
    "HNpdP2rL6FR6ckRJVxdmLdVW8hJyBWqZeRHLwsGbVcDN",  # HONEY
    "FoRGERiW7odcCBGU1bztZi16osPBHjxharvDathL5eds",  # FORGE
    "EKpQGSJtjMFqKZ9KQanSqYXRcF8fBopzLHYxdM65zcjm",  # WOOF
    "EcQCUYv57C4V6RoPxkVUiDwtX1SP8y8FP5AEToYL8Az",  # DINO
    "AGFEad2et2ZJif9jaGpdMixQqvW5i81aBdvKe7PHNfz3",  # FTT
    "DUSTawucrTsGU8hcqRdHDCbuYhCPADMLM2VcCb8VnFnQ",  # DUST
    "Basis9oJw9j8cw53oMV7iqsgo6ihi9ALw4QR31rcjUJa",  # BASIS
    "7Q2afV64in6N6SeZsAAB81TJzwDoD6zpqmHkzi9Dcavn",  # jSOL
    "bSo13r4TkiE4KumL71LsHTPpL2euBYLFx6h9HP3piy1",  # bSOL
    "A9mUU4qviSctJVPJdBJWkb28deg915LYJKrzQ19ji3FM",  # USDCet
    "Dn4noZ5jgGfkntzcQSUZ8czkreiZ1ForXWoVCtq8zJA8",  # USDTet
    "FR87nWEUxVgerFGhZM8Y4AggKGLnaXswr1Pd8wZ4kZcp",  # FRAX
    "DFL1zNkaGPWm1BwrQQ5vCdTnM3MFMZ1pHHATVtdUDeP",  # DFL
    "HCgybxq5Upy8Mccihrp7EsmwwFqYZtrHrsmsKwtGXLgW",  # STAR
    "METAewgxyPbgwsseH8T16a39CQ5VyVxZi9zXiDPY18m",  # META
    "kiGenopAScF8VF31Zbtx2Hg8qA5ArGqvnVtXb83sotc",  # KI
    "GENEtH5amGSi8kHAtQoezp1XEXwZJ8vcuePYnXdKrMYz",  # GENE
    "CASHVDm2wsJXfhj6VWxb7GiMdoLc17Du7paH4bNr5woT",  # CASH
    "F9CpWoyeBJfoRB8f2pBe2ZNPbPnSgKEuNyxECmkhRiZB",  # NINJA
    "HovGjrBGTfna4dvg6exkMxXuexB3tUfEZKcut8AWowXj",  # HOVA
]

# Maximum number of tokens to check for arbitrage
MAX_TOKENS_TO_CHECK = int(os.getenv("MAX_TOKENS_TO_CHECK", "500"))  # Increased to 500 to find more opportunities

# Throttling parameters
RATE_LIMIT_PER_SECOND = float(os.getenv("RATE_LIMIT_PER_SECOND", "10"))  # Increased to handle more requests
