@echo off
REM Binance Hybrid Signal AI - Installation Script
REM ===============================================

title Binance Hybrid Signal AI - Installation

echo.
echo ================================================================================
echo                    BINANCE HYBRID SIGNAL AI - INSTALLATION
echo ================================================================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python tidak ditemukan! Pastikan Python 3.8+ terinstall.
    echo 💡 Download Python dari: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python ditemukan!
python --version

REM Check if pip is available
pip --version >nul 2>&1
if errorlevel 1 (
    echo ❌ pip tidak ditemukan! Pastikan pip terinstall.
    pause
    exit /b 1
)

echo ✅ pip ditemukan!
pip --version

REM Check if requirements.txt exists
if not exist "requirements.txt" (
    echo ❌ File requirements.txt tidak ditemukan!
    echo 💡 Pastikan Anda berada di folder binance_hybrid_signal_ai
    pause
    exit /b 1
)

echo ✅ File requirements.txt ditemukan!

echo.
echo 📦 Memulai instalasi dependencies...
echo ================================================================================
echo.

REM Upgrade pip first
echo 🔄 Upgrading pip...
python -m pip install --upgrade pip

echo.
echo 📥 Installing dependencies dari requirements.txt...
echo.

REM Install requirements
pip install -r requirements.txt

REM Check if installation was successful
if errorlevel 1 (
    echo.
    echo ❌ Instalasi gagal!
    echo 💡 Coba jalankan sebagai Administrator atau cek koneksi internet
    pause
    exit /b 1
)

echo.
echo ================================================================================
echo                              INSTALASI SELESAI!
echo ================================================================================
echo.
echo ✅ Semua dependencies berhasil diinstall!
echo.
echo 🚀 Cara menjalankan program:
echo    1. Double-click run.bat
echo    2. Atau jalankan: python "binance3timeframe (1).py"
echo    3. Atau jalankan: python run.py
echo.
echo 🧪 Test logging:
echo    python test_logging.py
echo.
echo 📚 Baca dokumentasi di README.md untuk info lebih lanjut
echo.
echo ================================================================================
echo.
pause
