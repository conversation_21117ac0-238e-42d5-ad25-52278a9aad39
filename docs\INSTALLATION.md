# 🚀 IntelliTrader X - Installation Guide

## 📋 System Requirements

### Minimum Requirements
- **Operating System**: Windows 10/11, macOS 10.15+, Linux Ubuntu 18.04+
- **Python**: 3.8 or higher (Recommended: 3.11)
- **RAM**: 4GB minimum
- **Storage**: 2GB free space
- **Internet**: Stable connection for API calls

### Recommended Requirements
- **RAM**: 8GB or more
- **CPU**: Multi-core processor (4+ cores)
- **Display**: 1920x1080 resolution
- **Internet**: High-speed broadband

## 🛠️ Installation Steps

### Method 1: Quick Install (Recommended)

```bash
# 1. Clone the repository
git clone https://github.com/yourusername/intellitrader-x.git
cd intellitrader-x

# 2. Create virtual environment
python -m venv .venv

# 3. Activate virtual environment
# Windows:
.venv\Scripts\activate
# macOS/Linux:
source .venv/bin/activate

# 4. Install dependencies
pip install -r requirements.txt

# 5. Run IntelliTrader X
python "binance_hybrid_signal_ai/binance3timeframe (1).py"
```

### Method 2: Manual Installation

1. **Download Python 3.11**
   - Visit [python.org](https://python.org)
   - Download and install Python 3.11
   - Make sure to check "Add Python to PATH"

2. **Download IntelliTrader X**
   - Download ZIP from GitHub
   - Extract to desired folder

3. **Install Dependencies**
   ```bash
   pip install PySide6 numpy pandas aiohttp requests
   ```

4. **Launch Application**
   ```bash
   python "binance_hybrid_signal_ai/binance3timeframe (1).py"
   ```

## 🔧 Configuration

### First Run Setup

1. **Launch IntelliTrader X**
2. **Click "⚙️ Pengaturan" (Settings)**
3. **Configure your preferences**:
   - Select timeframes for analysis
   - Set confidence thresholds
   - Adjust performance settings
4. **Click "✅ Terapkan Konfigurasi" (Apply Configuration)**

### Performance Optimization

For optimal performance, adjust these settings:

```
Max Workers: 8-16 (based on CPU cores)
Concurrent Downloads: 50-100
API Request Delay: 50ms
Confidence Threshold: 0.60-0.70
```

## 🚨 Troubleshooting

### Common Issues

**Issue**: `ModuleNotFoundError: No module named 'PySide6'`
**Solution**: 
```bash
pip install PySide6
```

**Issue**: Application runs slowly
**Solution**: 
- Reduce number of timeframes
- Increase API request delay
- Lower concurrent downloads

**Issue**: No signals detected
**Solution**:
- Lower confidence threshold
- Enable Emergency Mode
- Check internet connection

### Getting Help

- 📖 [User Manual](USER_MANUAL.md)
- 💬 [Discord Community](https://discord.gg/intellitrader)
- 📧 Email: <EMAIL>

## ✅ Verification

To verify installation is successful:

1. Launch IntelliTrader X
2. You should see the main interface
3. Click "🚀 Mulai Analisa" to test
4. Check for any error messages

If everything works correctly, you're ready to start trading! 🎉
