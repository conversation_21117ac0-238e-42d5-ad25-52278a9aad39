# Binance Signal Prompt Generator

Program pencari peluang pada semua pairs di pasar futures Binance dan generator prompt untuk AI.

## Deskripsi

Program ini menghubungkan ke Binance Futures, menganalisis semua pasangan trading yang tersedia, dan mengidentifikasi peluang trading berdasarkan analisis teknikal yang komprehensif. Program menggunakan 60+ indikator teknikal untuk menghasilkan sinyal trading yang kuat dan menghasilkan prompt terperinci untuk AI.

## Fitur

- Koneksi ke Binance Futures tanpa memerlukan API key
- Analisis 250 candle terakhir dalam timeframe 1 jam untuk setiap pair
- Implementasi 60+ indikator teknikal dengan pengaturan optimal
- Identifikasi area support dan resistance
- Deteksi pola chart
- Identifikasi candle orderblock
- Analisis paralel menggunakan threading untuk kecepatan
- Tampilan UI yang cantik dan interaktif dengan warna pastel
- Generator prompt untuk AI dengan data teknikal lengkap

## Persyaratan

- Python 3.7+
- Paket Python yang diperlukan (lihat `requirements.txt`)

## Instalasi

1. Pastikan Python 3.7+ sudah terinstal di sistem Anda
2. Clone atau download repository ini
3. Instal dependensi yang diperlukan:

```bash
pip install -r requirements.txt
```

## Penggunaan

Jalankan program dengan perintah:

```bash
python binance_signal_prompt_generator.py
```

### Alur Program

1. Program akan terhubung ke Binance Futures
2. Mengambil data untuk semua pasangan trading
3. Menganalisis setiap pasangan dengan 60+ indikator teknikal
4. Menampilkan 5 pasangan dengan sinyal terkuat
5. Pengguna dapat memilih pasangan untuk dibuatkan prompt AI
6. Prompt yang dihasilkan dapat disalin ke clipboard
7. Pengguna dapat kembali ke menu, mengulang analisis, atau keluar

## Contoh Prompt yang Dihasilkan

Prompt yang dihasilkan akan berisi:

- Data pasar (pair, harga terakhir, sinyal, kekuatan sinyal)
- Level support dan resistance
- Pola chart yang terdeteksi
- Informasi orderblock
- Data 20+ indikator teknikal
- Instruksi untuk AI untuk memberikan analisis dan rekomendasi

## Catatan

- Program ini hanya untuk tujuan pendidikan dan informasi
- Keputusan trading sepenuhnya tanggung jawab pengguna
- Pastikan Anda memahami risiko trading di pasar cryptocurrency

## Pengembang

Dikembangkan oleh bobacheese

## Lisensi

MIT License
