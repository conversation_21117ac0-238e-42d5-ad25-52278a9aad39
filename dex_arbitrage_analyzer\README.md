# 🚀 DEX Arbitrage Analyzer

![DEX Arbitrage](https://i.imgur.com/XZxYnvL.png)

## 📊 Deskripsi

**DEX Arbitrage Analyzer** adalah program Python tingkat lanjut untuk mengidentifikasi, men<PERSON><PERSON><PERSON>, dan melaporkan potensi peluang arbitrase Decentralized Exchange (DEX), baik di dalam jaringan blockchain yang sama (same-chain) maupun antar jaringan blockchain yang berbeda (cross-chain).

Program ini menggunakan data dari API publik Dexscreener untuk menemukan perbedaan harga antar DEX yang dapat dimanfaatkan untuk arbitrase. Dengan perhitungan biaya yang komprehensif dan validasi berlapis, program ini memberikan analisis yang akurat dan detail tentang peluang arbitrase yang potensial.

## ✨ Fitur Utama

- **Dua Mode Operasi**:
  - Mode 1: Arbitrase <PERSON><PERSON><PERSON> (Same-Chain)
  - Mode 2: Arbitrase <PERSON><PERSON> (Cross-Chain)

- **Akuisisi Data Komprehensif**:
  - Pengambilan data dari API Dexscreener
  - Penanganan rate limit yang efektif
  - Pengambilan data volatilitas untuk analisis risiko

- **Analisis Arbitrase Canggih**:
  - Identifikasi pasangan token yang sama di DEX berbeda
  - Perhitungan biaya komprehensif (gas, fee DEX, slippage, bridge)
  - Estimasi profit bersih dan ROI

- **Validasi Peluang Berlapis**:
  - Filter likuiditas minimum
  - Validasi rasio likuiditas
  - Validasi ukuran trade vs likuiditas
  - Validasi harga stablecoin
  - Validasi profitabilitas bersih

- **Output Terstruktur dan Informatif**:
  - Tampilan konsol yang menarik dengan pustaka rich
  - Informasi detail untuk setiap peluang
  - Instruksi eksekusi manual step-by-step
  - Peringatan risiko yang jelas

## 🛠️ Persyaratan

- Python 3.6+
- Pustaka:
  - requests
  - rich
  - typing

## 📋 Instalasi

1. Clone repositori ini:
   ```bash
   git clone https://github.com/username/dex-arbitrage-analyzer.git
   cd dex-arbitrage-analyzer
   ```

2. Instal dependensi:
   ```bash
   pip install -r requirements.txt
   ```

## 🚀 Penggunaan

### Menjalankan Program

```bash
python main.py
```

### Opsi Command Line

```bash
python main.py --capital 100 --mode 3 --top 10 --save --async
```

- `--capital`: Modal dalam USD (default: 100.0)
- `--mode`: Mode operasi (1=Same-Chain, 2=Cross-Chain, 3=Keduanya)
- `--top`: Jumlah peluang teratas yang ditampilkan (default: 10)
- `--save`: Simpan hasil analisis ke file
- `--load`: Muat hasil analisis dari file
- `--async`: Gunakan pengambilan data asinkron (lebih cepat)
- `--sync`: Gunakan pengambilan data sinkron (lebih lambat tapi lebih stabil)

## 📁 Struktur Proyek

```
dex_arbitrage_analyzer/
├── main.py                # File utama program
├── config.py              # Konfigurasi program
├── dexscreener_api.py     # Modul untuk API Dexscreener (sinkron)
├── dexscreener_async_api.py # Modul untuk API Dexscreener (asinkron)
├── arbitrage_analyzer.py  # Logika analisis arbitrase
├── utils.py               # Fungsi-fungsi utilitas
├── output_formatter.py    # Formatter output dengan rich
├── requirements.txt       # Dependensi program
└── README.md              # Dokumentasi
```

## ⚙️ Konfigurasi

Anda dapat menyesuaikan parameter program di file `config.py`, termasuk:

- Modal default
- Daftar query token (500+ token)
- Mode pengambilan data (USE_ASYNC dan MAX_CONCURRENT_REQUESTS)
- Penanganan rate limit (RATE_LIMIT_DELAY, MAX_RETRIES, BACKOFF_FACTOR)
- Filter validasi (likuiditas minimum, rasio likuiditas, perbedaan harga, dll.)
- Filter untuk melewati peluang di DEX yang sama (SKIP_SAME_DEX_OPPORTUNITIES)
- Filter untuk melewati peluang antara stablecoin (SKIP_STABLECOIN_PAIRS)
- Filter untuk peluang dengan perbedaan harga mencurigakan (SUSPICIOUS_PRICE_THRESHOLD)
- Filter khusus untuk perbedaan harga stablecoin (MAX_STABLECOIN_PRICE_DIFF)
- Filter khusus untuk pasangan USDC/USDT (STRICT_STABLECOIN_FILTERING)
- Validasi token aktif (VALIDATE_TOKEN_ACTIVE, MIN_TRADE_VOLUME_USD, MIN_UPDATED_AT_MINUTES)
- Verifikasi keberadaan pasangan di Dexscreener (VERIFY_PAIR_EXISTS)
- Estimasi biaya gas dan bridge
- Dan banyak lagi

## ⚠️ Peringatan Risiko

Program ini ditujukan untuk analisis dan eksplorasi pasar. Arbitrase kripto memiliki risiko inheren yang signifikan:

- Harga dapat berubah dengan cepat, menghilangkan peluang arbitrase
- Estimasi biaya (terutama bridging dan slippage) memiliki keterbatasan akurasi
- Risiko smart contract dan bridge
- Volatilitas pasar

**Selalu lakukan riset mandiri sebelum melakukan transaksi apa pun.**

## 📝 Catatan Penting

- Program ini menggunakan API publik Dexscreener yang memiliki batasan rate limit
- Estimasi biaya bridge adalah perkiraan kasar dan tidak memperhitungkan jenis aset atau bridge spesifik
- Untuk arbitrase cross-chain, verifikasi manual diperlukan untuk memastikan aset dapat di-bridge antar jaringan
- Peluang di DEX yang sama dilewati secara default karena mungkin tidak valid (dapat dikonfigurasi di `config.py`)
- Peluang antara stablecoin (seperti USDC/USDT) dilewati secara default karena sering menunjukkan perbedaan harga yang tidak valid
- Peluang dengan perbedaan harga yang mencurigakan (>30%) akan diberi peringatan dan untuk stablecoin akan dilewati jika melebihi 1%
- Pasangan USDC/USDT secara khusus difilter karena sering menunjukkan perbedaan harga yang tidak valid
- Penanganan rate limit API yang lebih baik dengan retry dan backoff eksponensial
- Token yang tidak aktif diperdagangkan (volume rendah atau tidak diperbarui) dilewati
- Pasangan token yang tidak valid atau tidak ada di Dexscreener dilewati
- Logika pengelompokan pasangan token yang lebih baik untuk menemukan lebih banyak peluang
- Daftar token queries yang lebih lengkap (640+ token) termasuk token trending 2024

## 📜 Lisensi

MIT License

## 🤝 Kontribusi

Kontribusi, isu, dan permintaan fitur sangat diterima!

---

⭐ Jika Anda menyukai proyek ini, berikan bintang! ⭐
