#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test script untuk memeriksa validasi status deposit
"""

import json
from datetime import datetime

# Simulasi data jaringan dengan status deposit
def mock_find_common_networks(coin: str):
    """Simulasi fungsi find_common_networks dengan status deposit"""
    networks = {
        "binance": ["ERC20", "TRC20"],
        "kucoin": ["ERC20", "TRC20"],
        "bybit": ["ERC20", "TRC20"],
        "okx": ["ERC20", "TRC20"],
        "gate": ["ERC20", "TRC20"],
        "mexc": ["ERC20", "TRC20"],
        "htx": ["ERC20", "TRC20"],
        "common": ["ERC20", "TRC20"],
        "deposit_status": {
            "binance": True,
            "kucoin": True,
            "bybit": True,
            "okx": False,  # Deposit dinonaktifkan untuk OKX
            "gate": True,
            "mexc": True,
            "htx": True
        }
    }
    return networks

# Simulasi peluang arbitrase
def simulate_arbitrage_opportunities():
    """Simulasi peluang arbitrase dengan validasi status deposit"""
    opportunities = []
    
    # Simulasi pasangan trading
    pairs = [
        {"norm_pair": "CLV/USDT", "buy_exchange": "gate", "sell_exchange": "okx"},
        {"norm_pair": "BTC/USDT", "buy_exchange": "binance", "sell_exchange": "kucoin"},
        {"norm_pair": "ETH/USDT", "buy_exchange": "bybit", "sell_exchange": "okx"}
    ]
    
    for pair_info in pairs:
        norm_pair = pair_info["norm_pair"]
        buy_exchange = pair_info["buy_exchange"]
        sell_exchange = pair_info["sell_exchange"]
        
        # Ekstrak base dan quote asset
        base_asset, quote_asset = norm_pair.split("/")
        
        # Dapatkan jaringan yang didukung untuk base asset
        base_networks = mock_find_common_networks(base_asset)
        
        # Periksa status deposit untuk bursa penjualan
        deposit_status = base_networks.get("deposit_status", {})
        if sell_exchange in deposit_status and not deposit_status[sell_exchange]:
            print(f"⚠️ Deposit untuk {base_asset} di {sell_exchange.upper()} tidak diaktifkan, melewati peluang arbitrase")
            continue
        
        # Jika deposit diaktifkan, tambahkan ke peluang
        sell_deposit_enabled = True
        if "deposit_status" in base_networks and sell_exchange in base_networks["deposit_status"]:
            sell_deposit_enabled = base_networks["deposit_status"][sell_exchange]
        
        opportunity = {
            "pair": norm_pair,
            "buy_exchange": buy_exchange,
            "sell_exchange": sell_exchange,
            "base_asset": base_asset,
            "quote_asset": quote_asset,
            "deposit_enabled": sell_deposit_enabled,
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        
        opportunities.append(opportunity)
    
    return opportunities

# Tampilkan peluang arbitrase
def display_opportunities(opportunities):
    """Menampilkan peluang arbitrase"""
    print("\n=== PELUANG ARBITRASE ===")
    
    for i, opp in enumerate(opportunities, 1):
        print(f"{i}. {opp['pair']}")
        print(f"   Beli di: {opp['buy_exchange'].upper()}")
        print(f"   Jual di: {opp['sell_exchange'].upper()}")
        
        # Tampilkan informasi status deposit jika tersedia
        if "deposit_enabled" in opp and not opp["deposit_enabled"]:
            print(f"   ⚠️ PERINGATAN: Deposit untuk {opp['base_asset']} di {opp['sell_exchange'].upper()} tidak diaktifkan!")
        
        print()

# Main function
def main():
    """Fungsi utama"""
    print("Menguji validasi status deposit...")
    
    # Simulasi peluang arbitrase
    opportunities = simulate_arbitrage_opportunities()
    
    # Tampilkan peluang
    display_opportunities(opportunities)
    
    print("Test selesai")

if __name__ == "__main__":
    main()
