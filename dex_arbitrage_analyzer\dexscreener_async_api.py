"""
Modul untuk berinteraksi dengan API Dexscreener secara asinkron.
"""

import asyncio
import aiohttp
import json
import time
from typing import Dict, List, Any, Optional, Set
import logging
from aiohttp import ClientSession
from asyncio import Semaphore

from config import RATE_LIMIT_DELAY, MAX_CONCURRENT_REQUESTS, MAX_RETRIES, BACKOFF_FACTOR
from target_chains import TARGET_CHAINS, EXPENSIVE_CHAINS

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("dexscreener_async_api")

class DexscreenerAsyncAPI:
    """Kelas untuk berinteraksi dengan API Dexscreener secara asinkron."""

    BASE_URL = "https://api.dexscreener.com"

    def __init__(self):
        self.last_request_time = 0
        self.semaphore = Semaphore(MAX_CONCURRENT_REQUESTS)
        self.session = None

    async def _init_session(self):
        """Inisialisasi session aiohttp jika belum ada."""
        if self.session is None:
            self.session = aiohttp.ClientSession()

    async def close(self):
        """Menutup session aiohttp."""
        if self.session:
            await self.session.close()
            self.session = None

    async def _handle_rate_limit(self):
        """Menangani rate limit dengan menambahkan delay antar request."""
        current_time = time.time()
        time_since_last_request = current_time - self.last_request_time

        if time_since_last_request < RATE_LIMIT_DELAY:
            sleep_time = RATE_LIMIT_DELAY - time_since_last_request
            await asyncio.sleep(sleep_time)

        self.last_request_time = time.time()

    async def _make_request(self, endpoint: str) -> Dict[str, Any]:
        """
        Membuat request ke API Dexscreener secara asinkron dengan retry dan backoff eksponensial.

        Args:
            endpoint: Endpoint API yang akan diakses.

        Returns:
            Dict berisi respons dari API.

        Raises:
            Exception: Jika terjadi error saat melakukan request setelah semua percobaan.
        """
        await self._init_session()

        async with self.semaphore:
            url = f"{self.BASE_URL}{endpoint}"
            retry_count = 0
            current_delay = RATE_LIMIT_DELAY

            while retry_count <= MAX_RETRIES:
                try:
                    # Tunggu delay untuk rate limit
                    await self._handle_rate_limit()

                    async with self.session.get(url) as response:
                        if response.status == 429:  # Too Many Requests
                            retry_count += 1
                            if retry_count > MAX_RETRIES:
                                logger.error(f"Rate limit terlampaui untuk {url} setelah {MAX_RETRIES} percobaan")
                                raise Exception(f"Rate limit API terlampaui setelah {MAX_RETRIES} percobaan")

                            # Hitung delay dengan backoff eksponensial
                            backoff_delay = current_delay * (BACKOFF_FACTOR ** (retry_count - 1))
                            logger.warning(f"Rate limit terdeteksi, menunggu {backoff_delay:.2f} detik sebelum mencoba lagi (percobaan {retry_count}/{MAX_RETRIES})")
                            await asyncio.sleep(backoff_delay)
                            continue

                        response.raise_for_status()
                        return await response.json()

                except aiohttp.ClientResponseError as e:
                    if e.status == 429:  # Too Many Requests
                        retry_count += 1
                        if retry_count > MAX_RETRIES:
                            logger.error(f"Rate limit terlampaui untuk {url} setelah {MAX_RETRIES} percobaan")
                            raise Exception(f"Rate limit API terlampaui setelah {MAX_RETRIES} percobaan")

                        # Hitung delay dengan backoff eksponensial
                        backoff_delay = current_delay * (BACKOFF_FACTOR ** (retry_count - 1))
                        logger.warning(f"Rate limit terdeteksi, menunggu {backoff_delay:.2f} detik sebelum mencoba lagi (percobaan {retry_count}/{MAX_RETRIES})")
                        await asyncio.sleep(backoff_delay)
                    else:
                        logger.error(f"Error saat melakukan request ke {url}: {e}")
                        raise Exception(f"Error saat mengakses API Dexscreener: {e}")
                except aiohttp.ClientError as e:
                    logger.error(f"Error saat melakukan request ke {url}: {e}")
                    raise Exception(f"Error saat mengakses API Dexscreener: {e}")
                except json.JSONDecodeError as e:
                    logger.error(f"Error saat parsing JSON dari {url}: {e}")
                    raise Exception(f"Error saat parsing respons API: {e}")
                except asyncio.TimeoutError:
                    retry_count += 1
                    if retry_count > MAX_RETRIES:
                        logger.error(f"Timeout untuk {url} setelah {MAX_RETRIES} percobaan")
                        raise Exception(f"Timeout API setelah {MAX_RETRIES} percobaan")

                    # Hitung delay dengan backoff eksponensial
                    backoff_delay = current_delay * (BACKOFF_FACTOR ** (retry_count - 1))
                    logger.warning(f"Timeout terdeteksi, menunggu {backoff_delay:.2f} detik sebelum mencoba lagi (percobaan {retry_count}/{MAX_RETRIES})")
                    await asyncio.sleep(backoff_delay)

            # Jika kita sampai di sini, berarti semua percobaan gagal
            raise Exception(f"Gagal mengakses API setelah {MAX_RETRIES} percobaan")

    async def search_pairs(self, query: str) -> List[Dict[str, Any]]:
        """
        Mencari pasangan token berdasarkan query secara asinkron dan memfilter berdasarkan chain target.

        Args:
            query: Query pencarian.

        Returns:
            List berisi pasangan token yang ditemukan pada chain target.
        """
        endpoint = f"/latest/dex/search?q={query}"
        response = await self._make_request(endpoint)

        if "pairs" not in response:
            logger.warning(f"Tidak ada pasangan yang ditemukan untuk query: {query}")
            return []

        # Filter pairs berdasarkan chain target
        filtered_pairs = []
        for pair in response["pairs"]:
            chain_id = pair.get("chainId", "")
            if chain_id in TARGET_CHAINS:
                filtered_pairs.append(pair)
            elif chain_id in EXPENSIVE_CHAINS:
                # Log untuk chain mahal yang dilewati
                logger.debug(f"Melewati pair di chain mahal: {chain_id} untuk {pair.get('baseToken', {}).get('symbol', '')} / {pair.get('quoteToken', {}).get('symbol', '')}")

        if len(filtered_pairs) < len(response["pairs"]):
            logger.info(f"Memfilter {len(response['pairs']) - len(filtered_pairs)} pairs dari chain yang tidak ditargetkan untuk query: {query}")

        return filtered_pairs

    async def get_pair_details(self, chain_id: str, pair_address: str) -> Optional[Dict[str, Any]]:
        """
        Mendapatkan detail pasangan token berdasarkan chain ID dan alamat pair secara asinkron.

        Args:
            chain_id: ID chain.
            pair_address: Alamat pair.

        Returns:
            Dict berisi detail pasangan token atau None jika tidak ditemukan.
        """
        endpoint = f"/latest/dex/pairs/{chain_id}/{pair_address}"
        response = await self._make_request(endpoint)

        if "pairs" not in response or not response["pairs"]:
            logger.warning(f"Tidak ada detail yang ditemukan untuk pair: {chain_id}/{pair_address}")
            return None

        return response["pairs"][0]

    async def fetch_pair_volatility(self, chain_id: str, pair_address: str) -> Optional[float]:
        """
        Mengambil data volatilitas 1 jam untuk pasangan token secara asinkron.

        Args:
            chain_id: ID chain.
            pair_address: Alamat pair.

        Returns:
            Nilai volatilitas 1 jam atau None jika tidak tersedia.
        """
        pair_details = await self.get_pair_details(chain_id, pair_address)

        if not pair_details or "priceChange" not in pair_details:
            return None

        price_change = pair_details.get("priceChange", {})
        h1_change = price_change.get("h1")

        if h1_change is None:
            return None

        return abs(float(h1_change))

    async def fetch_all_pairs_for_queries(self, queries: List[str], progress_callback=None) -> List[Dict[str, Any]]:
        """
        Mengambil semua pasangan token untuk daftar query secara asinkron.

        Args:
            queries: List query pencarian.
            progress_callback: Callback untuk melaporkan kemajuan.

        Returns:
            List berisi semua pasangan token yang ditemukan.
        """
        tasks = []

        for i, query in enumerate(queries):
            task = asyncio.create_task(self._fetch_pairs_with_progress(query, i, len(queries), progress_callback))
            tasks.append(task)

        results = await asyncio.gather(*tasks, return_exceptions=True)

        all_pairs = []
        for result in results:
            if isinstance(result, Exception):
                logger.error(f"Error saat mengambil data: {result}")
            else:
                all_pairs.extend(result)

        # Menghapus duplikat berdasarkan pairAddress dan chainId
        unique_pairs = {}
        for pair in all_pairs:
            key = f"{pair.get('chainId', '')}-{pair.get('pairAddress', '')}"
            if key not in unique_pairs:
                unique_pairs[key] = pair

        return list(unique_pairs.values())

    async def _fetch_pairs_with_progress(self, query: str, index: int, total: int, progress_callback=None) -> List[Dict[str, Any]]:
        """
        Mengambil pasangan token untuk query dengan melaporkan kemajuan.

        Args:
            query: Query pencarian.
            index: Indeks query dalam daftar.
            total: Total jumlah query.
            progress_callback: Callback untuk melaporkan kemajuan.

        Returns:
            List berisi pasangan token yang ditemukan.
        """
        try:
            pairs = await self.search_pairs(query)

            if progress_callback:
                progress_callback(index, total, query, len(pairs))

            return pairs
        except Exception as e:
            logger.error(f"Error saat mengambil data untuk query '{query}': {e}")
            if progress_callback:
                progress_callback(index, total, query, 0, error=str(e))
            return []

    async def fetch_volatility_for_opportunities(self, opportunities: List[Dict[str, Any]], limit: int, progress_callback=None) -> List[Dict[str, Any]]:
        """
        Mengambil data volatilitas untuk peluang arbitrase secara asinkron.

        Args:
            opportunities: List peluang arbitrase.
            limit: Jumlah peluang yang diproses.
            progress_callback: Callback untuk melaporkan kemajuan.

        Returns:
            List peluang dengan data volatilitas.
        """
        if not opportunities:
            return opportunities

        # Batasi jumlah peluang yang diproses
        opportunities_to_process = opportunities[:limit]
        tasks = []

        for i, opportunity in enumerate(opportunities_to_process):
            task = asyncio.create_task(self._fetch_volatility_with_progress(opportunity, i, len(opportunities_to_process), progress_callback))
            tasks.append(task)

        await asyncio.gather(*tasks, return_exceptions=True)

        return opportunities

    async def _fetch_volatility_with_progress(self, opportunity: Dict[str, Any], index: int, total: int, progress_callback=None) -> None:
        """
        Mengambil data volatilitas untuk peluang dengan melaporkan kemajuan.

        Args:
            opportunity: Peluang arbitrase.
            index: Indeks peluang dalam daftar.
            total: Total jumlah peluang.
            progress_callback: Callback untuk melaporkan kemajuan.
        """
        try:
            if opportunity["type"] == "same_chain":
                chain_id = opportunity["chain_id"]
                pair_address = opportunity["buy_pair"]["pairAddress"]
            else:  # cross_chain
                chain_id = opportunity["source_chain"]
                pair_address = opportunity["buy_pair"]["pairAddress"]

            volatility = await self.fetch_pair_volatility(chain_id, pair_address)
            opportunity["volatility"] = volatility

            if progress_callback:
                progress_callback(index, total, f"Peluang #{index+1}", volatility is not None)
        except Exception as e:
            logger.error(f"Error saat mengambil data volatilitas untuk peluang #{index+1}: {e}")
            if progress_callback:
                progress_callback(index, total, f"Peluang #{index+1}", False, error=str(e))
