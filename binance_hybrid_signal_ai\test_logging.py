#!/usr/bin/env python3
"""
Test script untuk memverifikasi bahwa logging ke konsol berfungsi dengan baik
pada program Binance Hybrid Signal AI
"""

import sys
import os
import logging

# Setup logging yang sama seperti di program utama
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(threadName)s - %(message)s',
    handlers=[
        logging.StreamHandler(),  # Output ke konsol
        logging.FileHandler('test_binance_signals.log', encoding='utf-8')  # Output ke file
    ]
)

# Mock signal untuk testing
class MockSignal:
    def emit(self, message):
        print(f"[GUI MOCK] {message}")

# Helper function yang sama seperti di program utama
def log_to_console_and_gui(message, log_signal=None, level="INFO"):
    """
    Mengirim log ke konsol dan GUI secara bersamaan
    """
    if level.upper() == "INFO":
        logging.info(message)
    elif level.upper() == "WARNING":
        logging.warning(message)
    elif level.upper() == "ERROR":
        logging.error(message)
    elif level.upper() == "DEBUG":
        logging.debug(message)
    else:
        logging.info(message)

    # Kirim ke GUI jika signal tersedia
    if log_signal:
        log_signal.emit(message)

def test_logging_functionality():
    """Test fungsi logging dual (console + GUI)"""
    print("=" * 60)
    print("🧪 TESTING LOGGING FUNCTIONALITY")
    print("=" * 60)

    # Mock GUI signal
    mock_gui_signal = MockSignal()

    # Test berbagai level logging
    log_to_console_and_gui("🚀 Test INFO level logging", mock_gui_signal, "INFO")
    log_to_console_and_gui("⚠️ Test WARNING level logging", mock_gui_signal, "WARNING")
    log_to_console_and_gui("❌ Test ERROR level logging", mock_gui_signal, "ERROR")
    log_to_console_and_gui("🔍 Test DEBUG level logging", mock_gui_signal, "DEBUG")

    # Test tanpa GUI signal
    log_to_console_and_gui("📝 Test logging tanpa GUI signal", level="INFO")

    # Test simulasi progress analysis
    print("\n" + "=" * 60)
    print("📊 SIMULASI PROGRESS ANALYSIS")
    print("=" * 60)

    total_pairs = 100
    for i in range(0, total_pairs, 20):
        pair_name = f"BTC/USDT-{i}"
        log_to_console_and_gui(f"📈 Progress: Menganalisa pair {i+1}/{total_pairs} - {pair_name}", mock_gui_signal, "INFO")

    # Test simulasi sinyal ditemukan
    print("\n" + "=" * 60)
    print("🎯 SIMULASI SINYAL TRADING")
    print("=" * 60)

    test_signals = [
        ("BTCUSDT", "KUAT BELI", 8.5, 0.85),
        ("ETHUSDT", "BELI", 6.2, 0.72),
        ("ADAUSDT", "JUAL", -4.8, 0.68),
    ]

    for pair, signal_type, score, confidence in test_signals:
        log_to_console_and_gui(f"🎯 SINYAL DITEMUKAN: {pair} - {signal_type} (Score: {score:.2f})", mock_gui_signal, "INFO")

    # Test ringkasan hasil
    print("\n" + "=" * 60)
    print("📊 SIMULASI RINGKASAN HASIL")
    print("=" * 60)

    log_to_console_and_gui("📊 RINGKASAN ANALISIS HYBRID ENGINE ASYNC:", mock_gui_signal, "INFO")
    log_to_console_and_gui(f"   • Total pairs dianalisa: {total_pairs}", mock_gui_signal, "INFO")
    log_to_console_and_gui(f"   • Sinyal kandidat ditemukan: {len(test_signals)}", mock_gui_signal, "INFO")
    log_to_console_and_gui(f"   • Sinyal kuat & confident: 2", mock_gui_signal, "INFO")
    log_to_console_and_gui(f"   • Top sinyal untuk ditampilkan: {len(test_signals)}", mock_gui_signal, "INFO")

    log_to_console_and_gui("🏆 TOP SINYAL TRADING:", mock_gui_signal, "INFO")
    for i, (pair, signal_type, score, confidence) in enumerate(test_signals, 1):
        price = 45000.0 + (i * 1000)  # Mock price
        log_to_console_and_gui(f"   {i}. {pair} - {signal_type} (Score: {score:.2f}, Conf: {confidence*100:.0f}%, Price: ${price:.4f})", mock_gui_signal, "INFO")

    log_to_console_and_gui("✅ ANALISIS HYBRID ENGINE ASYNC SELESAI!", mock_gui_signal, "INFO")

    print("\n" + "=" * 60)
    print("✅ TEST LOGGING SELESAI!")
    print("📁 Cek file 'test_binance_signals.log' untuk output file")
    print("=" * 60)

if __name__ == "__main__":
    test_logging_functionality()
