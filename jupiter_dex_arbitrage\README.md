# 🚀 Jupiter DEX Arbitrage Analyzer

![Jupiter DEX](https://station.jup.ag/img/jupiter-logo.svg)

## 📊 Deskripsi

Program Python canggih untuk menemukan dan memanfaatkan peluang arbitrase pada Jupiter DEX Aggregator di blockchain Solana. Program ini secara khusus mendeteksi situasi di mana Anda dapat menukar 1 SOL ke token lain dan mendapatkan nilai USD yang lebih tinggi.

## ✨ Fitur Utama

- 🔍 **Deteksi Arbitrase Otomatis**: Menemukan peluang arbitrase antara SOL dan token lain
- 💰 **Perhitungan Profit Akurat**: Memperhitungkan slippage dan biaya transaksi
- 🔄 **Integrasi Jupiter API**: Menggunakan Jupiter API v6 terbaru
- 🛡️ **Validasi Peluang**: Memverifikasi bahwa peluang arbitrase adalah valid
- 📈 **Tampilan Futuristik**: Menggunakan library Rich untuk tampilan yang menarik
- 🔒 **Manajemen Risiko**: Filter token berdasarkan likuiditas dan volume

## 🛠️ Instalasi

1. Clone repository ini:
```bash
git clone https://github.com/username/jupiter_dex_arbitrage.git
cd jupiter_dex_arbitrage
```

2. Buat virtual environment:
```bash
python -m venv venv
```

3. Aktifkan virtual environment:
```bash
# Windows
venv\Scripts\activate
# Linux/Mac
source venv/bin/activate
```

4. Install dependensi:
```bash
pip install -r requirements.txt
```

## 🚀 Penggunaan

Jalankan program dengan perintah:

```bash
python main.py
```

Program akan:
1. Menghubungkan ke Jupiter API
2. Mengambil daftar token yang dapat diperdagangkan
3. Menganalisis peluang arbitrase
4. Menampilkan peluang yang ditemukan dengan detail profit

## ⚙️ Konfigurasi

Anda dapat menyesuaikan parameter di file `config.py`:

- `MIN_PROFIT_PERCENTAGE`: Minimum persentase profit untuk dipertimbangkan
- `CAPITAL_AMOUNT_SOL`: Jumlah SOL yang digunakan untuk arbitrase
- `SLIPPAGE_BPS`: Slippage dalam basis poin (1 bps = 0.01%)
- `SLIPPAGE_COST_PERCENTAGE`: Biaya slippage untuk perhitungan profit
- `MIN_LIQUIDITY_USD`: Minimum likuiditas dalam USD
- `MIN_VOLUME_USD`: Minimum volume 24 jam dalam USD

## 📋 Contoh Output

```
╭──────────────────────── JUPITER DEX ARBITRAGE ANALYZER ─────────────────────────╮
│ Mencari peluang arbitrase di Solana menggunakan Jupiter DEX Aggregator          │
╰──────────────────────────────────────────────────────────────────────────────────╯

Waktu Mulai: 2023-05-01 12:00:00

Memulai Jupiter DEX Arbitrage Analyzer...
Menghubungkan ke Jupiter DEX Aggregator API...
Harga SOL saat ini: $150.25
Modal: 1 SOL ($150.25)
Minimum profit: 1.0%
Slippage: 0.5%

Mencari peluang arbitrase...
Memproses token: 100/100 (100.0%)
Memvalidasi peluang arbitrase...

                              Peluang Arbitrage Terdeteksi                               
┏━━━━┳━━━━━━━━━━━━━━━━━━┳━━━━━━━━┳━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━┓
┃ No ┃ Token           ┃ Symbol ┃ Route             ┃ Harga Beli     ┃ Harga Jual     ┃
┡━━━━╇━━━━━━━━━━━━━━━━━━╇━━━━━━━━╇━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━┩
│ 1  │ Jupiter         │ JUP    │ SOL → JUP → SOL   │ $1.08          │ $1.10          │
│ 2  │ Raydium         │ RAY    │ SOL → RAY → SOL   │ $0.45          │ $0.46          │
└────┴──────────────────┴────────┴───────────────────┴────────────────┴────────────────┘

Total Peluang: 2
Harga SOL: $150.25
Modal: $150.25 (1 SOL)

Peluang arbitrase disimpan ke file: jupiter_arbitrage_20230501_120015.json

╭─────────────────────────────── Ringkasan ───────────────────────────────╮
│ Analisis Selesai                                                        │
│ Waktu: 2023-05-01 12:00:15                                              │
│ Total token dianalisis: 500                                             │
│ Total peluang ditemukan: 5                                              │
│ Total peluang valid: 2                                                  │
╰──────────────────────────────────────────────────────────────────────────╯

Program selesai.
```

## 📝 Catatan

- Program ini hanya untuk tujuan edukasi dan penelitian
- Selalu lakukan uji tuntas sebelum melakukan transaksi nyata
- Pasar kripto sangat volatil, peluang arbitrase dapat berubah dengan cepat

## 📜 Lisensi

MIT License

## 🙏 Kredit

- [Jupiter DEX Aggregator](https://jup.ag/)
- [Solana Blockchain](https://solana.com/)
- [Rich Library](https://github.com/Textualize/rich)
