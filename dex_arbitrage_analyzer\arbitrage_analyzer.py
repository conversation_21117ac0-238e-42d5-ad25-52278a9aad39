"""
Modul untuk menganalisis peluang arbitrase DEX.
"""

import logging
import time
from typing import Dict, List, Any, Tu<PERSON>, Optional
from collections import defaultdict
import math

from config import (
    DEFAULT_CAPITAL, MIN_LIQUIDITY_USD, MAX_LIQUIDITY_RATIO, MAX_TRADE_TO_LIQUIDITY_RATIO,
    MIN_PRICE_DIFFERENCE_PERCENTAGE, MAX_PRICE_DIFFERENCE_PERCENTAGE, MIN_PROFIT_USD,
    MAX_ROI_PERCENTAGE, STABLECOIN_ADDRESSES, STABLECOIN_PRICE_TOLERANCE,
    STABLECOIN_PRICE_DIFF_TOLERANCE, ESTIMATED_GAS_COSTS, ESTIMATED_BRIDGE_COSTS_USD,
    DEFAULT_BRIDGE_COST_USD, DEFAULT_DEX_FEE_PERCENTAGE, IDR_TO_USD_RATE,
    SKIP_SAME_DEX_OPPORTUNITIES, SKIP_STABLECOIN_PAIRS, MAX_STABLECOIN_PRICE_DIFF,
    SUSPICIOUS_PRICE_THRESHOLD, STRICT_STABLECOIN_FILTERING,
    VALIDATE_TOKEN_ACTIVE, MIN_TRADE_VOLUME_USD, MIN_UPDATED_AT_MINUTES, VERIFY_PAIR_EXISTS,
    DEFAULT_SLIPPAGE_PERCENTAGE, USE_FIXED_SLIPPAGE,
    REMOVE_DUPLICATE_OPPORTUNITIES, SIMILARITY_THRESHOLD, TOKEN_PAIR_SIMILARITY_WEIGHT,
    DEX_SIMILARITY_WEIGHT, PRICE_SIMILARITY_WEIGHT, ENABLE_DEDUPLICATION_LOGGING,
    STRICT_TOKEN_VERIFICATION, VERIFY_TOKEN_ADDRESS, VERIFY_TOKEN_NAME, VERIFY_TOKEN_DECIMALS,
    NAME_SIMILARITY_THRESHOLD, ENABLE_TOKEN_VERIFICATION_LOGGING, AMBIGUOUS_TOKENS,
    FILTER_TOKENS_WITH_ISSUES, ENABLE_ISSUE_DETECTION_LOGGING, TOKEN_ISSUES
)

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("arbitrage_analyzer")

class ArbitrageAnalyzer:
    """Kelas untuk menganalisis peluang arbitrase DEX."""

    def __init__(self, capital: float = DEFAULT_CAPITAL):
        """
        Inisialisasi analyzer dengan modal yang ditentukan.

        Args:
            capital: Modal dalam USD untuk simulasi arbitrase.
        """
        self.capital = capital

    def is_stablecoin(self, chain_id: str, token_address: str) -> bool:
        """
        Memeriksa apakah token adalah stablecoin.

        Args:
            chain_id: ID chain.
            token_address: Alamat token.

        Returns:
            True jika token adalah stablecoin, False jika tidak.
        """
        if chain_id in STABLECOIN_ADDRESSES:
            return token_address.lower() in [addr.lower() for addr in STABLECOIN_ADDRESSES[chain_id]]
        return False

    def is_stablecoin_pair(self, pair: Dict[str, Any]) -> bool:
        """
        Memeriksa apakah pasangan token adalah pasangan stablecoin.

        Args:
            pair: Data pasangan token.

        Returns:
            True jika kedua token adalah stablecoin, False jika tidak.
        """
        chain_id = pair.get("chainId", "")
        base_address = pair.get("baseToken", {}).get("address", "")
        quote_address = pair.get("quoteToken", {}).get("address", "")
        base_symbol = pair.get("baseToken", {}).get("symbol", "").upper()
        quote_symbol = pair.get("quoteToken", {}).get("symbol", "").upper()

        # Cek berdasarkan alamat kontrak
        is_stablecoin_by_address = (self.is_stablecoin(chain_id, base_address) and
                                   self.is_stablecoin(chain_id, quote_address))

        # Cek berdasarkan simbol (untuk chain yang mungkin tidak ada di daftar STABLECOIN_ADDRESSES)
        common_stablecoins = ["USDT", "USDC", "DAI", "BUSD", "TUSD", "USDD", "USDP", "GUSD", "LUSD", "FRAX", "UST"]
        is_stablecoin_by_symbol = (base_symbol in common_stablecoins and quote_symbol in common_stablecoins)

        return is_stablecoin_by_address or is_stablecoin_by_symbol

    def is_token_active(self, pair: Dict[str, Any]) -> bool:
        """
        Memeriksa apakah token aktif diperdagangkan berdasarkan volume dan waktu update terakhir.

        Args:
            pair: Data pasangan token.

        Returns:
            True jika token aktif, False jika tidak.
        """
        if not VALIDATE_TOKEN_ACTIVE:
            return True

        # Cek volume perdagangan
        volume_usd = pair.get("volume", {}).get("h24", 0)
        if volume_usd is None:
            volume_usd = 0

        # Jika volume tidak tersedia atau 0, coba cek volume h6 atau h1
        if volume_usd < MIN_TRADE_VOLUME_USD:
            volume_h6 = pair.get("volume", {}).get("h6", 0)
            if volume_h6 is None:
                volume_h6 = 0

            volume_h1 = pair.get("volume", {}).get("h1", 0)
            if volume_h1 is None:
                volume_h1 = 0

            # Estimasi volume 24 jam berdasarkan volume 6 jam atau 1 jam
            if volume_h6 > 0:
                estimated_volume_24h = volume_h6 * 4  # Estimasi 24 jam dari 6 jam
            elif volume_h1 > 0:
                estimated_volume_24h = volume_h1 * 24  # Estimasi 24 jam dari 1 jam
            else:
                estimated_volume_24h = 0

            # Gunakan estimasi volume jika lebih tinggi dari volume 24 jam yang dilaporkan
            if estimated_volume_24h > volume_usd:
                volume_usd = estimated_volume_24h

        if volume_usd < MIN_TRADE_VOLUME_USD:
            logger.info(f"Token tidak aktif: Volume 24 jam terlalu rendah (${volume_usd:.2f} < ${MIN_TRADE_VOLUME_USD:.2f})")
            return False

        # Cek waktu update terakhir
        updated_at = pair.get("updatedAt", 0)
        if updated_at == 0:
            # Jika tidak ada updatedAt, coba gunakan timestamp lain jika tersedia
            price_change_ts = pair.get("priceChange", {}).get("timestamp", 0)
            if price_change_ts > 0:
                updated_at = price_change_ts
            else:
                logger.info("Token tidak aktif: Tidak ada informasi waktu update")
                return False

        # Konversi timestamp ke menit yang lalu
        current_time = int(time.time() * 1000)  # Konversi ke milidetik
        minutes_since_update = (current_time - updated_at) / (1000 * 60)

        if minutes_since_update > MIN_UPDATED_AT_MINUTES:
            logger.info(f"Token tidak aktif: Terakhir diperbarui {minutes_since_update:.1f} menit yang lalu (> {MIN_UPDATED_AT_MINUTES} menit)")
            return False

        # Cek apakah token memiliki issue
        if not self.check_token_issues(pair):
            return False

        return True

    def check_token_issues(self, pair: Dict[str, Any]) -> bool:
        """
        Memeriksa apakah token memiliki issue yang terdeteksi di Dexscreener.

        Args:
            pair: Data pasangan token.

        Returns:
            True jika token tidak memiliki issue, False jika memiliki issue.
        """
        if not FILTER_TOKENS_WITH_ISSUES:
            return True

        # Cek flags pada base token
        base_token = pair.get("baseToken", {})
        base_symbol = base_token.get("symbol", "")
        base_flags = base_token.get("flags", [])

        # Cek flags pada quote token
        quote_token = pair.get("quoteToken", {})
        quote_symbol = quote_token.get("symbol", "")
        quote_flags = quote_token.get("flags", [])

        # Cek flags pada pair
        pair_flags = pair.get("flags", [])

        # Gabungkan semua flags
        all_flags = set(base_flags + quote_flags + pair_flags)

        # Cek apakah ada issue yang diketahui
        detected_issues = []
        for issue in TOKEN_ISSUES:
            if issue in all_flags:
                detected_issues.append(issue)

        # Jika ada issue yang terdeteksi, log dan kembalikan False
        if detected_issues:
            if ENABLE_ISSUE_DETECTION_LOGGING:
                logger.warning(f"Token {base_symbol}/{quote_symbol} memiliki issue: {', '.join(detected_issues)}")
            return False

        # Cek warning pada base token
        base_warnings = base_token.get("warnings", [])

        # Cek warning pada quote token
        quote_warnings = quote_token.get("warnings", [])

        # Cek warning pada pair
        pair_warnings = pair.get("warnings", [])

        # Gabungkan semua warnings
        all_warnings = set(base_warnings + quote_warnings + pair_warnings)

        # Jika ada warning yang terdeteksi, log tapi tetap kembalikan True
        if all_warnings and ENABLE_ISSUE_DETECTION_LOGGING:
            logger.warning(f"Token {base_symbol}/{quote_symbol} memiliki warning: {', '.join(all_warnings)}")

        return True

    def verify_pair_exists(self, pair: Dict[str, Any]) -> bool:
        """
        Memeriksa apakah pasangan token masih ada di Dexscreener.

        Args:
            pair: Data pasangan token.

        Returns:
            True jika pasangan ada, False jika tidak.
        """
        if not VERIFY_PAIR_EXISTS:
            return True

        # Cek apakah pasangan memiliki harga yang valid
        price_usd = pair.get("priceUsd")
        if price_usd is None or price_usd == "0" or price_usd == "":
            logger.info("Pasangan tidak valid: Tidak ada harga USD")
            return False

        # Cek apakah pasangan memiliki likuiditas yang valid
        liquidity_usd = pair.get("liquidity", {}).get("usd", 0)
        if liquidity_usd is None or liquidity_usd <= 0:
            logger.info("Pasangan tidak valid: Tidak ada likuiditas USD")
            return False

        # Cek apakah pasangan memiliki URL Dexscreener (opsional)
        dexscreener_url = pair.get("url", "")
        if not dexscreener_url:
            # Hanya log warning, tidak menolak pasangan
            logger.warning("Pasangan mungkin tidak valid: Tidak ada URL Dexscreener")

        # Cek apakah pasangan memiliki alamat kontrak (lebih fleksibel)
        pair_address = pair.get("pairAddress", "")
        if not pair_address:
            # Beberapa chain mungkin tidak memiliki alamat pair yang jelas
            # Hanya log warning, tidak menolak pasangan
            logger.warning("Pasangan mungkin tidak valid: Tidak ada alamat kontrak pair")

        # Cek apakah token base dan quote memiliki simbol
        base_symbol = pair.get("baseToken", {}).get("symbol", "")
        quote_symbol = pair.get("quoteToken", {}).get("symbol", "")

        if not base_symbol:
            logger.info("Pasangan tidak valid: Token base tidak memiliki simbol")
            return False

        if not quote_symbol:
            logger.info("Pasangan tidak valid: Token quote tidak memiliki simbol")
            return False

        # Cek apakah token base dan quote memiliki alamat kontrak (lebih fleksibel)
        base_address = pair.get("baseToken", {}).get("address", "")
        quote_address = pair.get("quoteToken", {}).get("address", "")

        if not base_address:
            # Beberapa chain mungkin tidak memiliki alamat token yang jelas
            # Hanya log warning, tidak menolak pasangan
            logger.warning(f"Pasangan mungkin tidak valid: Token base {base_symbol} tidak memiliki alamat kontrak")

        if not quote_address:
            # Beberapa chain mungkin tidak memiliki alamat token yang jelas
            # Hanya log warning, tidak menolak pasangan
            logger.warning(f"Pasangan mungkin tidak valid: Token quote {quote_symbol} tidak memiliki alamat kontrak")

        return True

    def verify_tokens_are_same(self, pair1: Dict[str, Any], pair2: Dict[str, Any]) -> bool:
        """
        Memverifikasi bahwa token di kedua pasangan benar-benar sama.

        Args:
            pair1: Data pasangan token pertama.
            pair2: Data pasangan token kedua.

        Returns:
            True jika token benar-benar sama, False jika tidak.
        """
        # Periksa chain ID - tolak jika salah satu adalah Solana
        chain_id1 = pair1.get("chainId", "")
        chain_id2 = pair2.get("chainId", "")

        # Tolak jika salah satu chain adalah Solana
        if chain_id1 == "solana" or chain_id2 == "solana":
            logger.info(f"Melewati peluang: Salah satu chain adalah Solana ({chain_id1} atau {chain_id2})")
            return False

        # Tolak jika salah satu DEX adalah DEX Solana
        dex_id1 = pair1.get("dexId", "").lower()
        dex_id2 = pair2.get("dexId", "").lower()
        solana_dexes = ["raydium", "orca", "pumpswap", "fluxbeam", "meteora", "jupiter", "openbook"]

        if dex_id1 in solana_dexes or dex_id2 in solana_dexes:
            logger.info(f"Melewati peluang: Salah satu DEX adalah DEX Solana ({dex_id1} atau {dex_id2})")
            return False

        # Dapatkan simbol token
        base_symbol1 = pair1.get("baseToken", {}).get("symbol", "").upper()
        base_symbol2 = pair2.get("baseToken", {}).get("symbol", "").upper()
        quote_symbol1 = pair1.get("quoteToken", {}).get("symbol", "").upper()
        quote_symbol2 = pair2.get("quoteToken", {}).get("symbol", "").upper()

        # Periksa kesamaan simbol (dalam urutan yang sama atau terbalik)
        same_symbol_order = (base_symbol1 == base_symbol2 and quote_symbol1 == quote_symbol2)
        reverse_symbol_order = (base_symbol1 == quote_symbol2 and quote_symbol1 == base_symbol2)

        # Jika simbol tidak sama, langsung kembalikan False
        if not (same_symbol_order or reverse_symbol_order):
            if ENABLE_TOKEN_VERIFICATION_LOGGING:
                logger.info(f"Simbol token tidak sama: {base_symbol1}/{quote_symbol1} vs {base_symbol2}/{quote_symbol2}")
            return False

        # Jika verifikasi ketat tidak diaktifkan, kembalikan True berdasarkan simbol saja
        if not STRICT_TOKEN_VERIFICATION:
            return True

        # Tentukan token yang akan dibandingkan berdasarkan urutan simbol
        if same_symbol_order:
            base_token1 = pair1.get("baseToken", {})
            base_token2 = pair2.get("baseToken", {})
            quote_token1 = pair1.get("quoteToken", {})
            quote_token2 = pair2.get("quoteToken", {})
        else:  # reverse_symbol_order
            base_token1 = pair1.get("baseToken", {})
            base_token2 = pair2.get("quoteToken", {})
            quote_token1 = pair1.get("quoteToken", {})
            quote_token2 = pair2.get("baseToken", {})

        # Verifikasi alamat kontrak jika di chain yang sama
        if VERIFY_TOKEN_ADDRESS and chain_id1 == chain_id2:
            base_address1 = base_token1.get("address", "").lower()
            base_address2 = base_token2.get("address", "").lower()
            quote_address1 = quote_token1.get("address", "").lower()
            quote_address2 = quote_token2.get("address", "").lower()

            # Jika alamat kontrak tersedia, verifikasi kesamaan alamat
            if base_address1 and base_address2 and quote_address1 and quote_address2:
                if base_address1 != base_address2 or quote_address1 != quote_address2:
                    if ENABLE_TOKEN_VERIFICATION_LOGGING:
                        logger.info(f"Alamat kontrak token berbeda meskipun simbol sama: {base_symbol1}/{quote_symbol1}")
                        logger.info(f"Base: {base_address1} vs {base_address2}")
                        logger.info(f"Quote: {quote_address1} vs {quote_address2}")
                    return False

        # Verifikasi nama token
        if VERIFY_TOKEN_NAME:
            base_name1 = base_token1.get("name", "").lower()
            base_name2 = base_token2.get("name", "").lower()
            quote_name1 = quote_token1.get("name", "").lower()
            quote_name2 = quote_token2.get("name", "").lower()

            # Jika nama token tersedia, verifikasi kesamaan nama
            if base_name1 and base_name2 and quote_name1 and quote_name2:
                # Periksa apakah token termasuk dalam daftar token ambigu
                base_symbol = base_symbol1
                quote_symbol = quote_symbol1

                # Jika token termasuk dalam daftar token ambigu, periksa apakah nama token cocok dengan salah satu nama yang diketahui
                if base_symbol in AMBIGUOUS_TOKENS:
                    base_name_match = False
                    for known_name in AMBIGUOUS_TOKENS[base_symbol]:
                        if known_name.lower() in base_name1.lower() and known_name.lower() in base_name2.lower():
                            base_name_match = True
                            break

                    if not base_name_match:
                        if ENABLE_TOKEN_VERIFICATION_LOGGING:
                            logger.info(f"Nama token base berbeda meskipun simbol sama: {base_symbol}")
                            logger.info(f"Nama: {base_name1} vs {base_name2}")
                        return False

                if quote_symbol in AMBIGUOUS_TOKENS:
                    quote_name_match = False
                    for known_name in AMBIGUOUS_TOKENS[quote_symbol]:
                        if known_name.lower() in quote_name1.lower() and known_name.lower() in quote_name2.lower():
                            quote_name_match = True
                            break

                    if not quote_name_match:
                        if ENABLE_TOKEN_VERIFICATION_LOGGING:
                            logger.info(f"Nama token quote berbeda meskipun simbol sama: {quote_symbol}")
                            logger.info(f"Nama: {quote_name1} vs {quote_name2}")
                        return False

                # Jika token tidak termasuk dalam daftar token ambigu, periksa kesamaan nama secara langsung
                if base_symbol not in AMBIGUOUS_TOKENS and base_name1 != base_name2:
                    # Hitung kesamaan nama menggunakan rasio kesamaan sederhana
                    name_similarity = self.calculate_name_similarity(base_name1, base_name2)
                    if name_similarity < NAME_SIMILARITY_THRESHOLD:
                        if ENABLE_TOKEN_VERIFICATION_LOGGING:
                            logger.info(f"Nama token base berbeda meskipun simbol sama: {base_symbol}")
                            logger.info(f"Nama: {base_name1} vs {base_name2} (kesamaan: {name_similarity:.2f})")
                        return False

                if quote_symbol not in AMBIGUOUS_TOKENS and quote_name1 != quote_name2:
                    # Hitung kesamaan nama menggunakan rasio kesamaan sederhana
                    name_similarity = self.calculate_name_similarity(quote_name1, quote_name2)
                    if name_similarity < NAME_SIMILARITY_THRESHOLD:
                        if ENABLE_TOKEN_VERIFICATION_LOGGING:
                            logger.info(f"Nama token quote berbeda meskipun simbol sama: {quote_symbol}")
                            logger.info(f"Nama: {quote_name1} vs {quote_name2} (kesamaan: {name_similarity:.2f})")
                        return False

        # Verifikasi decimals token
        if VERIFY_TOKEN_DECIMALS:
            base_decimals1 = base_token1.get("decimals")
            base_decimals2 = base_token2.get("decimals")
            quote_decimals1 = quote_token1.get("decimals")
            quote_decimals2 = quote_token2.get("decimals")

            # Jika decimals tersedia, verifikasi kesamaan decimals
            if (base_decimals1 is not None and base_decimals2 is not None and
                quote_decimals1 is not None and quote_decimals2 is not None):

                if base_decimals1 != base_decimals2 or quote_decimals1 != quote_decimals2:
                    if ENABLE_TOKEN_VERIFICATION_LOGGING:
                        logger.info(f"Decimals token berbeda meskipun simbol sama: {base_symbol1}/{quote_symbol1}")
                        logger.info(f"Base decimals: {base_decimals1} vs {base_decimals2}")
                        logger.info(f"Quote decimals: {quote_decimals1} vs {quote_decimals2}")
                    return False

        # Jika semua verifikasi berhasil, kembalikan True
        return True

    def calculate_name_similarity(self, name1: str, name2: str) -> float:
        """
        Menghitung kesamaan antara dua nama token.

        Args:
            name1: Nama token pertama.
            name2: Nama token kedua.

        Returns:
            Skor kesamaan antara 0.0 (tidak sama) dan 1.0 (identik).
        """
        # Jika salah satu nama kosong, kembalikan 0
        if not name1 or not name2:
            return 0.0

        # Jika nama sama persis, kembalikan 1
        if name1 == name2:
            return 1.0

        # Ubah ke lowercase dan hapus spasi
        name1 = name1.lower().replace(" ", "")
        name2 = name2.lower().replace(" ", "")

        # Jika nama sama setelah normalisasi, kembalikan 1
        if name1 == name2:
            return 1.0

        # Hitung kesamaan berdasarkan jumlah karakter yang sama
        shorter = min(name1, name2, key=len)
        longer = max(name1, name2, key=len)

        if len(shorter) == 0:
            return 0.0

        # Hitung jumlah karakter yang sama
        matches = sum(1 for i in range(len(shorter)) if shorter[i] == longer[i])

        # Hitung skor kesamaan
        return matches / len(longer)

    def estimate_slippage(self, trade_size: float, liquidity_usd: float) -> float:
        """
        Mengestimasi slippage berdasarkan ukuran trade dan likuiditas.

        Args:
            trade_size: Ukuran trade dalam USD.
            liquidity_usd: Likuiditas pool dalam USD.

        Returns:
            Estimasi slippage dalam persen.
        """
        if liquidity_usd <= 0:
            return 100.0  # Slippage maksimum jika likuiditas tidak valid

        # Formula heuristik yang lebih baik untuk estimasi slippage
        # Menggunakan pendekatan non-linear yang mempertimbangkan rasio trade_size terhadap likuiditas
        slippage_percentage = (trade_size / (liquidity_usd + trade_size)) * 100

        # Batasi slippage maksimum pada 50%
        return min(slippage_percentage, 50.0)

    def calculate_dex_fee(self, pair: Dict[str, Any], amount_usd: float) -> float:
        """
        Menghitung fee DEX untuk transaksi.

        Args:
            pair: Data pasangan token.
            amount_usd: Jumlah transaksi dalam USD.

        Returns:
            Fee DEX dalam USD.
        """
        fee_percentage = pair.get("fee", DEFAULT_DEX_FEE_PERCENTAGE)

        # Konversi dari persentase ke desimal
        if fee_percentage > 1:
            fee_percentage = fee_percentage / 100

        return amount_usd * fee_percentage / 100

    def estimate_gas_cost(self, chain_id: str) -> float:
        """
        Mengestimasi biaya gas untuk transaksi di chain tertentu.

        Args:
            chain_id: ID chain.

        Returns:
            Estimasi biaya gas dalam USD.
        """
        return ESTIMATED_GAS_COSTS.get(chain_id, 1.0)

    def estimate_bridge_cost(self, source_chain: str, target_chain: str) -> float:
        """
        Mengestimasi biaya bridge antar chain.

        Args:
            source_chain: Chain sumber.
            target_chain: Chain tujuan.

        Returns:
            Estimasi biaya bridge dalam USD.
        """
        key = (source_chain, target_chain)
        return ESTIMATED_BRIDGE_COSTS_USD.get(key, DEFAULT_BRIDGE_COST_USD)

    def analyze_same_chain_opportunities(self, pairs: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Menganalisis peluang arbitrase dalam chain yang sama.

        Args:
            pairs: List pasangan token.

        Returns:
            List peluang arbitrase yang tervalidasi.
        """
        # Mengelompokkan pasangan berdasarkan chainId dan pasangan token
        grouped_pairs = defaultdict(list)

        for pair in pairs:
            chain_id = pair.get("chainId", "")
            base_address = pair.get("baseToken", {}).get("address", "")
            quote_address = pair.get("quoteToken", {}).get("address", "")
            base_symbol = pair.get("baseToken", {}).get("symbol", "").upper()
            quote_symbol = pair.get("quoteToken", {}).get("symbol", "").upper()

            if not chain_id:
                continue

            # Coba gunakan alamat kontrak jika tersedia
            if base_address and quote_address:
                key = f"{chain_id}:{base_address.lower()}:{quote_address.lower()}"
                grouped_pairs[key].append(pair)

            # Juga coba gunakan simbol token sebagai fallback
            if base_symbol and quote_symbol:
                symbol_key = f"{chain_id}:{base_symbol}:{quote_symbol}"
                # Tambahkan ke grup berdasarkan simbol jika belum ada di grup berdasarkan alamat
                if key != symbol_key or not base_address or not quote_address:
                    grouped_pairs[symbol_key].append(pair)

        opportunities = []

        # Menganalisis peluang untuk setiap kelompok pasangan
        for key, group_pairs in grouped_pairs.items():
            # Lewati jika hanya ada 1 pasangan dalam kelompok
            if len(group_pairs) < 2:
                continue

            # Bandingkan setiap pasangan dengan setiap pasangan lainnya dalam grup
            for i, pair1 in enumerate(group_pairs):
                # Validasi apakah token aktif diperdagangkan
                if not self.is_token_active(pair1):
                    continue

                # Validasi apakah pasangan token masih ada di Dexscreener
                if not self.verify_pair_exists(pair1):
                    continue

                price1 = float(pair1.get("priceUsd", "0") or "0")
                if price1 <= 0:
                    continue

                for j, pair2 in enumerate(group_pairs):
                    if i == j:  # Lewati perbandingan dengan diri sendiri
                        continue

                    # Validasi apakah token aktif diperdagangkan
                    if not self.is_token_active(pair2):
                        continue

                    # Validasi apakah pasangan token masih ada di Dexscreener
                    if not self.verify_pair_exists(pair2):
                        continue

                    price2 = float(pair2.get("priceUsd", "0") or "0")
                    if price2 <= 0:
                        continue

                    # Tentukan pasangan beli dan jual berdasarkan harga
                    if price1 < price2:  # Beli di pair1, jual di pair2
                        buy_pair = pair1
                        sell_pair = pair2
                        buy_price = price1
                        sell_price = price2
                    else:  # Beli di pair2, jual di pair1
                        buy_pair = pair2
                        sell_pair = pair1
                        buy_price = price2
                        sell_price = price1

                    # Lewati jika DEX sama dan filter diaktifkan
                    if SKIP_SAME_DEX_OPPORTUNITIES and buy_pair.get("dexId", "") == sell_pair.get("dexId", ""):
                        continue

                    # Verifikasi bahwa token benar-benar sama antara kedua pasangan
                    if not self.verify_tokens_are_same(buy_pair, sell_pair):
                        logger.info(f"Melewati peluang: Token tidak sama antara {buy_pair.get('dexId', '')} dan {sell_pair.get('dexId', '')}")
                        continue

                    # Hitung perbedaan harga kotor
                    price_diff_percentage = ((sell_price - buy_price) / buy_price) * 100

                    # Lewati jika kedua token adalah stablecoin dan filter diaktifkan
                    is_stablecoin_pair_flag = self.is_stablecoin_pair(buy_pair)
                    if SKIP_STABLECOIN_PAIRS and is_stablecoin_pair_flag:
                        base_symbol = buy_pair.get('baseToken', {}).get('symbol', '').upper()
                        quote_symbol = buy_pair.get('quoteToken', {}).get('symbol', '').upper()
                        continue

                    # Filter tambahan untuk pasangan USDC/USDT yang sering menunjukkan perbedaan harga yang tidak valid
                    if STRICT_STABLECOIN_FILTERING:
                        base_symbol = buy_pair.get('baseToken', {}).get('symbol', '').upper()
                        quote_symbol = buy_pair.get('quoteToken', {}).get('symbol', '').upper()
                        if ((base_symbol == 'USDC' and quote_symbol == 'USDT') or
                            (base_symbol == 'USDT' and quote_symbol == 'USDC')):
                            continue

                    # Lewati jika perbedaan harga mencurigakan (terlalu tinggi)
                    if price_diff_percentage > SUSPICIOUS_PRICE_THRESHOLD:
                        # Jika stablecoin, terapkan filter yang lebih ketat
                        if is_stablecoin_pair_flag and price_diff_percentage > MAX_STABLECOIN_PRICE_DIFF:
                            continue
                        # Untuk token non-stablecoin, periksa juga tapi berikan peringatan saja
                        logger.warning(f"Peluang dengan perbedaan harga mencurigakan: {price_diff_percentage:.2f}%")

                    # Validasi perbedaan harga
                    if price_diff_percentage < MIN_PRICE_DIFFERENCE_PERCENTAGE:
                        continue

                    if price_diff_percentage > MAX_PRICE_DIFFERENCE_PERCENTAGE:
                        # Untuk stablecoin, terapkan validasi yang lebih ketat
                        if is_stablecoin_pair_flag:
                            if (abs(buy_price - 1.0) > STABLECOIN_PRICE_TOLERANCE or
                                abs(sell_price - 1.0) > STABLECOIN_PRICE_TOLERANCE or
                                price_diff_percentage > STABLECOIN_PRICE_DIFF_TOLERANCE):
                                continue
                        else:
                            continue

                    # Validasi likuiditas
                    buy_liquidity = buy_pair.get("liquidity", {}).get("usd", 0)
                    sell_liquidity = sell_pair.get("liquidity", {}).get("usd", 0)

                    if buy_liquidity < MIN_LIQUIDITY_USD or sell_liquidity < MIN_LIQUIDITY_USD:
                        continue

                    # Validasi rasio likuiditas
                    if min(buy_liquidity, sell_liquidity) > 0:
                        liquidity_ratio = max(buy_liquidity, sell_liquidity) / min(buy_liquidity, sell_liquidity)
                        if liquidity_ratio > MAX_LIQUIDITY_RATIO:
                            continue

                    # Validasi ukuran trade vs likuiditas
                    if (self.capital / buy_liquidity > MAX_TRADE_TO_LIQUIDITY_RATIO or
                        self.capital / sell_liquidity > MAX_TRADE_TO_LIQUIDITY_RATIO):
                        continue

                    # Gunakan slippage tetap 5%
                    buy_slippage = DEFAULT_SLIPPAGE_PERCENTAGE
                    sell_slippage = DEFAULT_SLIPPAGE_PERCENTAGE

                    # Biaya slippage tetap 5% dari modal
                    slippage_cost = self.capital * (DEFAULT_SLIPPAGE_PERCENTAGE / 100)

                    # Biaya DEX
                    buy_fee = self.calculate_dex_fee(buy_pair, self.capital)

                    # Jumlah token yang dibeli
                    tokens_bought = self.capital / (buy_price * (1 + buy_slippage / 100))

                    # Nilai dalam USD setelah menjual
                    sell_value = tokens_bought * sell_price * (1 - sell_slippage / 100)

                    sell_fee = self.calculate_dex_fee(sell_pair, sell_value)

                    # Biaya gas
                    gas_cost = self.estimate_gas_cost(buy_pair.get("chainId", "")) * 2  # Biaya gas untuk beli dan jual

                    # Total biaya (termasuk fee DEX, gas, dan slippage)
                    total_cost = buy_fee + sell_fee + gas_cost + slippage_cost

                    # Profit kotor (tanpa biaya)
                    gross_profit = sell_value - self.capital

                    # Profit bersih (setelah semua biaya termasuk slippage)
                    net_profit = gross_profit - total_cost

                    # Validasi profitabilitas
                    if net_profit < MIN_PROFIT_USD:
                        continue

                    # Hitung ROI
                    roi_percentage = (net_profit / self.capital) * 100

                    # Validasi ROI maksimum
                    if roi_percentage > MAX_ROI_PERCENTAGE:
                        continue

                    # Tambahkan peluang yang valid
                    opportunity = {
                        "type": "same_chain",
                        "chain_id": buy_pair.get("chainId", ""),
                        "base_token": buy_pair.get("baseToken", {}),
                        "quote_token": buy_pair.get("quoteToken", {}),
                        "buy_pair": buy_pair,
                        "sell_pair": sell_pair,
                        "buy_price": buy_price,
                        "sell_price": sell_price,
                        "buy_dex": buy_pair.get("dexId", ""),
                        "sell_dex": sell_pair.get("dexId", ""),
                        "buy_liquidity": buy_liquidity,
                        "sell_liquidity": sell_liquidity,
                        "price_diff_percentage": price_diff_percentage,
                        "buy_slippage": buy_slippage,
                        "sell_slippage": sell_slippage,
                        "slippage_cost": slippage_cost,
                        "buy_fee": buy_fee,
                        "sell_fee": sell_fee,
                        "gas_cost": gas_cost,
                        "total_cost": total_cost,
                        "gross_profit": gross_profit,
                        "net_profit": net_profit,
                        "net_profit_idr": net_profit * IDR_TO_USD_RATE,
                        "roi_percentage": roi_percentage,
                        "capital": self.capital,
                        "volatility": None  # Akan diisi nanti
                    }

                    opportunities.append(opportunity)

        return opportunities

    def analyze_cross_chain_opportunities(self, pairs: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Menganalisis peluang arbitrase antar chain yang berbeda.

        Args:
            pairs: List pasangan token.

        Returns:
            List peluang arbitrase yang tervalidasi.
        """
        # Mengelompokkan pasangan berdasarkan simbol token
        grouped_pairs = defaultdict(list)

        for pair in pairs:
            base_symbol = pair.get("baseToken", {}).get("symbol", "")
            quote_symbol = pair.get("quoteToken", {}).get("symbol", "")

            if not base_symbol or not quote_symbol:
                continue

            # Kelompokkan berdasarkan simbol token
            symbol_key = f"{base_symbol.upper()}:{quote_symbol.upper()}"
            grouped_pairs[symbol_key].append(pair)

            # Juga coba kelompokkan berdasarkan simbol token terbalik (untuk pasangan yang mungkin dibalik)
            reversed_key = f"{quote_symbol.upper()}:{base_symbol.upper()}"
            if reversed_key != symbol_key:  # Hindari duplikasi untuk pasangan seperti BTC:BTC
                grouped_pairs[reversed_key].append(pair)

        opportunities = []

        # Menganalisis peluang untuk setiap kelompok pasangan
        for _, group_pairs in grouped_pairs.items():
            # Lewati jika hanya ada pasangan dari 1 chain
            chains = set(pair.get("chainId", "") for pair in group_pairs)
            if len(chains) < 2:
                continue

            # Kelompokkan pasangan berdasarkan chain
            pairs_by_chain = defaultdict(list)
            for pair in group_pairs:
                chain_id = pair.get("chainId", "")
                if chain_id:
                    pairs_by_chain[chain_id].append(pair)

            # Cari peluang antar chain
            for source_chain, source_pairs in pairs_by_chain.items():
                for target_chain, target_pairs in pairs_by_chain.items():
                    if source_chain == target_chain:
                        continue

                    # Untuk setiap pasangan di chain sumber
                    for buy_pair in source_pairs:
                        # Validasi apakah token aktif diperdagangkan
                        if not self.is_token_active(buy_pair):
                            continue

                        # Validasi apakah pasangan token masih ada di Dexscreener
                        if not self.verify_pair_exists(buy_pair):
                            continue

                        buy_price = float(buy_pair.get("priceUsd", "0") or "0")
                        if buy_price <= 0:
                            continue

                        # Untuk setiap pasangan di chain tujuan
                        for sell_pair in target_pairs:
                            # Validasi apakah token aktif diperdagangkan
                            if not self.is_token_active(sell_pair):
                                continue

                            # Validasi apakah pasangan token masih ada di Dexscreener
                            if not self.verify_pair_exists(sell_pair):
                                continue

                            sell_price = float(sell_pair.get("priceUsd", "0") or "0")
                            if sell_price <= 0:
                                continue

                            # Lewati jika DEX sama dan filter diaktifkan
                            if SKIP_SAME_DEX_OPPORTUNITIES and buy_pair.get("dexId", "") == sell_pair.get("dexId", ""):
                                continue

                            # Verifikasi bahwa token benar-benar sama antara kedua pasangan
                            if not self.verify_tokens_are_same(buy_pair, sell_pair):
                                logger.info(f"Melewati peluang cross-chain: Token tidak sama antara {buy_pair.get('dexId', '')} di {source_chain} dan {sell_pair.get('dexId', '')} di {target_chain}")
                                continue

                            # Lewati jika kedua token adalah stablecoin dan filter diaktifkan
                            is_stablecoin_pair_flag = self.is_stablecoin_pair(buy_pair)
                            if SKIP_STABLECOIN_PAIRS and is_stablecoin_pair_flag:
                                continue

                            # Filter tambahan untuk pasangan USDC/USDT
                            if STRICT_STABLECOIN_FILTERING:
                                base_symbol = buy_pair.get('baseToken', {}).get('symbol', '').upper()
                                quote_symbol = buy_pair.get('quoteToken', {}).get('symbol', '').upper()
                                if ((base_symbol == 'USDC' and quote_symbol == 'USDT') or
                                    (base_symbol == 'USDT' and quote_symbol == 'USDC')):
                                    continue

                            # Hitung perbedaan harga kotor
                            price_diff_percentage = ((sell_price - buy_price) / buy_price) * 100

                            # Validasi perbedaan harga
                            if price_diff_percentage < MIN_PRICE_DIFFERENCE_PERCENTAGE:
                                continue

                            if price_diff_percentage > MAX_PRICE_DIFFERENCE_PERCENTAGE:
                                # Untuk stablecoin, terapkan validasi yang lebih ketat
                                if is_stablecoin_pair_flag:
                                    if (abs(buy_price - 1.0) > STABLECOIN_PRICE_TOLERANCE or
                                        abs(sell_price - 1.0) > STABLECOIN_PRICE_TOLERANCE or
                                        price_diff_percentage > STABLECOIN_PRICE_DIFF_TOLERANCE):
                                        continue
                                else:
                                    continue

                            # Lewati jika perbedaan harga mencurigakan (terlalu tinggi)
                            if price_diff_percentage > SUSPICIOUS_PRICE_THRESHOLD:
                                # Jika stablecoin, terapkan filter yang lebih ketat
                                if is_stablecoin_pair_flag and price_diff_percentage > MAX_STABLECOIN_PRICE_DIFF:
                                    continue
                                # Untuk token non-stablecoin, periksa juga tapi berikan peringatan saja
                                logger.warning(f"Peluang cross-chain dengan perbedaan harga mencurigakan: {price_diff_percentage:.2f}%")

                            # Validasi likuiditas
                            buy_liquidity = buy_pair.get("liquidity", {}).get("usd", 0)
                            sell_liquidity = sell_pair.get("liquidity", {}).get("usd", 0)

                            if buy_liquidity < MIN_LIQUIDITY_USD or sell_liquidity < MIN_LIQUIDITY_USD:
                                continue

                            # Validasi rasio likuiditas
                            if min(buy_liquidity, sell_liquidity) > 0:
                                liquidity_ratio = max(buy_liquidity, sell_liquidity) / min(buy_liquidity, sell_liquidity)
                                if liquidity_ratio > MAX_LIQUIDITY_RATIO:
                                    continue

                            # Validasi ukuran trade vs likuiditas
                            if (self.capital / buy_liquidity > MAX_TRADE_TO_LIQUIDITY_RATIO or
                                self.capital / sell_liquidity > MAX_TRADE_TO_LIQUIDITY_RATIO):
                                continue

                            # Gunakan slippage tetap 5%
                            buy_slippage = DEFAULT_SLIPPAGE_PERCENTAGE
                            sell_slippage = DEFAULT_SLIPPAGE_PERCENTAGE

                            # Biaya slippage tetap 5% dari modal
                            slippage_cost = self.capital * (DEFAULT_SLIPPAGE_PERCENTAGE / 100)

                            # Biaya DEX
                            buy_fee = self.calculate_dex_fee(buy_pair, self.capital)

                            # Jumlah token yang dibeli
                            tokens_bought = self.capital / (buy_price * (1 + buy_slippage / 100))

                            # Nilai dalam USD setelah menjual
                            sell_value = tokens_bought * sell_price * (1 - sell_slippage / 100)

                            sell_fee = self.calculate_dex_fee(sell_pair, sell_value)

                            # Biaya gas untuk transaksi di kedua chain
                            source_gas_cost = self.estimate_gas_cost(source_chain)
                            target_gas_cost = self.estimate_gas_cost(target_chain)

                            # Biaya bridge
                            bridge_cost = self.estimate_bridge_cost(source_chain, target_chain)

                            # Total biaya termasuk approve, bridge, dan slippage
                            total_cost = buy_fee + sell_fee + source_gas_cost * 2 + target_gas_cost + bridge_cost + slippage_cost

                            # Profit kotor (tanpa biaya)
                            gross_profit = sell_value - self.capital

                            # Profit bersih (setelah semua biaya termasuk slippage)
                            net_profit = gross_profit - total_cost

                            # Validasi profitabilitas
                            if net_profit < MIN_PROFIT_USD:
                                continue

                            # Hitung ROI
                            roi_percentage = (net_profit / self.capital) * 100

                            # Validasi ROI maksimum
                            if roi_percentage > MAX_ROI_PERCENTAGE:
                                continue

                            # Tambahkan peluang yang valid
                            opportunity = {
                                "type": "cross_chain",
                                "source_chain": source_chain,
                                "target_chain": target_chain,
                                "base_token": buy_pair.get("baseToken", {}),
                                "quote_token": buy_pair.get("quoteToken", {}),
                                "buy_pair": buy_pair,
                                "sell_pair": sell_pair,
                                "buy_price": buy_price,
                                "sell_price": sell_price,
                                "buy_dex": buy_pair.get("dexId", ""),
                                "sell_dex": sell_pair.get("dexId", ""),
                                "buy_liquidity": buy_liquidity,
                                "sell_liquidity": sell_liquidity,
                                "price_diff_percentage": price_diff_percentage,
                                "buy_slippage": buy_slippage,
                                "sell_slippage": sell_slippage,
                                "slippage_cost": slippage_cost,
                                "buy_fee": buy_fee,
                                "sell_fee": sell_fee,
                                "source_gas_cost": source_gas_cost,
                                "target_gas_cost": target_gas_cost,
                                "bridge_cost": bridge_cost,
                                "total_cost": total_cost,
                                "gross_profit": gross_profit,
                                "net_profit": net_profit,
                                "net_profit_idr": net_profit * IDR_TO_USD_RATE,
                                "roi_percentage": roi_percentage,
                                "capital": self.capital,
                                "volatility": None  # Akan diisi nanti
                            }

                            opportunities.append(opportunity)

        return opportunities

    def calculate_opportunity_similarity(self, opp1: Dict[str, Any], opp2: Dict[str, Any]) -> float:
        """
        Menghitung tingkat kesamaan antara dua peluang arbitrase.

        Args:
            opp1: Peluang arbitrase pertama.
            opp2: Peluang arbitrase kedua.

        Returns:
            Skor kesamaan antara 0.0 (tidak sama) dan 1.0 (identik).
        """
        # Jika tipe peluang berbeda, langsung kembalikan 0
        if opp1.get("type") != opp2.get("type"):
            if ENABLE_DEDUPLICATION_LOGGING:
                logger.debug(f"Tipe peluang berbeda: {opp1.get('type')} vs {opp2.get('type')}")
            return 0.0

        # Jika chain berbeda, langsung kembalikan 0
        if opp1.get("chain_id") != opp2.get("chain_id"):
            if ENABLE_DEDUPLICATION_LOGGING:
                logger.debug(f"Chain berbeda: {opp1.get('chain_id')} vs {opp2.get('chain_id')}")
            return 0.0

        # Hitung kesamaan pasangan token
        token_similarity = 0.0
        base1 = opp1.get("base_token", {}).get("symbol", "").upper()
        base2 = opp2.get("base_token", {}).get("symbol", "").upper()
        quote1 = opp1.get("quote_token", {}).get("symbol", "").upper()
        quote2 = opp2.get("quote_token", {}).get("symbol", "").upper()

        # Periksa kesamaan token (baik dalam urutan yang sama atau terbalik)
        same_order = (base1 == base2 and quote1 == quote2)
        reverse_order = (base1 == quote2 and quote1 == base2)

        if same_order or reverse_order:
            token_similarity = 1.0
            if ENABLE_DEDUPLICATION_LOGGING:
                logger.debug(f"Token sama: {base1}/{quote1} vs {base2}/{quote2}")
        else:
            if ENABLE_DEDUPLICATION_LOGGING:
                logger.debug(f"Token berbeda: {base1}/{quote1} vs {base2}/{quote2}")

        # Hitung kesamaan DEX
        dex_similarity = 0.0
        buy_dex1 = opp1.get("buy_dex", "").upper()
        sell_dex1 = opp1.get("sell_dex", "").upper()
        buy_dex2 = opp2.get("buy_dex", "").upper()
        sell_dex2 = opp2.get("sell_dex", "").upper()

        if buy_dex1 == buy_dex2 and sell_dex1 == sell_dex2:
            dex_similarity = 1.0
            if ENABLE_DEDUPLICATION_LOGGING:
                logger.debug(f"DEX sama: {buy_dex1}->{sell_dex1} vs {buy_dex2}->{sell_dex2}")
        elif buy_dex1 == sell_dex2 and sell_dex1 == buy_dex2:
            # Peluang yang sama tetapi arah berbeda
            dex_similarity = 0.8
            if ENABLE_DEDUPLICATION_LOGGING:
                logger.debug(f"DEX sama (arah berbeda): {buy_dex1}->{sell_dex1} vs {buy_dex2}->{sell_dex2}")
        else:
            if ENABLE_DEDUPLICATION_LOGGING:
                logger.debug(f"DEX berbeda: {buy_dex1}->{sell_dex1} vs {buy_dex2}->{sell_dex2}")

        # Hitung kesamaan harga
        price_similarity = 0.0
        buy_price1 = opp1.get("buy_price", 0)
        sell_price1 = opp1.get("sell_price", 0)
        buy_price2 = opp2.get("buy_price", 0)
        sell_price2 = opp2.get("sell_price", 0)

        if buy_price1 and buy_price2 and sell_price1 and sell_price2:
            # Hitung rasio perbedaan harga
            buy_price_ratio = min(buy_price1, buy_price2) / max(buy_price1, buy_price2) if max(buy_price1, buy_price2) > 0 else 0
            sell_price_ratio = min(sell_price1, sell_price2) / max(sell_price1, sell_price2) if max(sell_price1, sell_price2) > 0 else 0
            price_similarity = (buy_price_ratio + sell_price_ratio) / 2

            if ENABLE_DEDUPLICATION_LOGGING:
                logger.debug(f"Kesamaan harga: {price_similarity:.2f} (buy: {buy_price1} vs {buy_price2}, sell: {sell_price1} vs {sell_price2})")

        # Hitung skor kesamaan total berdasarkan bobot
        total_similarity = (
            token_similarity * TOKEN_PAIR_SIMILARITY_WEIGHT +
            dex_similarity * DEX_SIMILARITY_WEIGHT +
            price_similarity * PRICE_SIMILARITY_WEIGHT
        )

        if ENABLE_DEDUPLICATION_LOGGING:
            logger.debug(f"Skor kesamaan total: {total_similarity:.2f} (token: {token_similarity:.2f}, DEX: {dex_similarity:.2f}, harga: {price_similarity:.2f})")

        return total_similarity

    def remove_duplicate_opportunities(self, opportunities: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Menghapus peluang arbitrase yang duplikat.

        Args:
            opportunities: List peluang arbitrase.

        Returns:
            List peluang arbitrase tanpa duplikasi.
        """
        if not REMOVE_DUPLICATE_OPPORTUNITIES or len(opportunities) <= 1:
            return opportunities

        # Log jumlah peluang sebelum deduplikasi
        logger.info(f"Jumlah peluang sebelum deduplikasi: {len(opportunities)}")

        # Buat dictionary untuk mengelompokkan peluang berdasarkan pasangan token
        token_groups = {}

        # Kelompokkan peluang berdasarkan pasangan token
        for opp in opportunities:
            base_symbol = opp.get("base_token", {}).get("symbol", "").upper()
            quote_symbol = opp.get("quote_token", {}).get("symbol", "").upper()
            chain_id = opp.get("chain_id", "")

            # Buat kunci untuk mengelompokkan peluang
            key = f"{chain_id}:{base_symbol}:{quote_symbol}"

            if key not in token_groups:
                token_groups[key] = []

            token_groups[key].append(opp)

        # Log jumlah kelompok token
        if ENABLE_DEDUPLICATION_LOGGING:
            logger.info(f"Jumlah kelompok token: {len(token_groups)}")
            for key, group in token_groups.items():
                logger.info(f"Kelompok {key}: {len(group)} peluang")

        # List untuk menyimpan peluang yang sudah diperiksa
        unique_opportunities = []

        # Proses setiap kelompok token
        for key, group in token_groups.items():
            # Urutkan peluang dalam kelompok berdasarkan ROI
            sorted_group = sorted(group, key=lambda x: x.get("roi_percentage", 0), reverse=True)

            # List untuk menyimpan peluang unik dalam kelompok
            unique_group = []

            for opp in sorted_group:
                is_duplicate = False

                # Bandingkan dengan peluang yang sudah diperiksa dalam kelompok
                for unique_opp in unique_group:
                    similarity = self.calculate_opportunity_similarity(opp, unique_opp)

                    if similarity >= SIMILARITY_THRESHOLD:
                        is_duplicate = True
                        logger.info(f"Melewati peluang duplikat: {opp.get('base_token', {}).get('symbol')}/{opp.get('quote_token', {}).get('symbol')} "
                                   f"di {opp.get('buy_dex')} dan {opp.get('sell_dex')} (kesamaan: {similarity:.2f})")
                        break

                if not is_duplicate:
                    unique_group.append(opp)

            # Tambahkan peluang unik dalam kelompok ke list peluang unik
            unique_opportunities.extend(unique_group)

        # Urutkan peluang unik berdasarkan ROI
        unique_opportunities = sorted(unique_opportunities, key=lambda x: x.get("roi_percentage", 0), reverse=True)

        # Log jumlah peluang yang dihapus
        removed_count = len(opportunities) - len(unique_opportunities)
        logger.info(f"Menghapus {removed_count} peluang duplikat dari total {len(opportunities)} peluang")

        return unique_opportunities

    def sort_opportunities(self, opportunities: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Mengurutkan peluang berdasarkan volatilitas dan ROI, serta menghapus duplikasi.

        Args:
            opportunities: List peluang arbitrase.

        Returns:
            List peluang yang diurutkan dan tanpa duplikasi.
        """
        # Hapus duplikasi terlebih dahulu
        unique_opportunities = self.remove_duplicate_opportunities(opportunities)

        # Urutkan berdasarkan volatilitas (jika tersedia) dan ROI
        return sorted(
            unique_opportunities,
            key=lambda x: (
                -1 if x.get("volatility") is None else -abs(x.get("volatility", 0)),
                -x.get("roi_percentage", 0)
            )
        )
