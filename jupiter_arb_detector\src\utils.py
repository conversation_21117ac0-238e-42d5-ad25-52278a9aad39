"""
Modul untuk fungsi-fungsi utilitas.
"""
import os
import yaml
import logging
import time
import random
from typing import Dict, Any, List, Optional
from pathlib import Path
from datetime import datetime
from rich.console import Console

# Inisialisasi console untuk output yang lebih baik
console = Console()

def load_config(config_path: str) -> Dict[str, Any]:
    """
    Memuat konfigurasi dari file YAML.

    Args:
        config_path: Path ke file konfigurasi

    Returns:
        Dictionary berisi konfigurasi
    """
    try:
        with open(config_path, 'r') as file:
            config = yaml.safe_load(file)
        return config
    except Exception as e:
        raise Exception(f"Error saat memuat konfigurasi: {e}")

def setup_logging(log_level: str, log_dir: str) -> logging.Logger:
    """
    Setup logging.

    Args:
        log_level: Level log (DEBUG, INFO, WARNING, ERROR)
        log_dir: Direktori untuk file log

    Returns:
        Logger
    """
    # Buat direktori log jika belum ada
    os.makedirs(log_dir, exist_ok=True)

    # Dapatkan level log
    numeric_level = getattr(logging, log_level.upper(), None)
    if not isinstance(numeric_level, int):
        raise ValueError(f"Level log tidak valid: {log_level}")

    # Setup logging
    logger = logging.getLogger("jupiter_arb_detector")
    logger.setLevel(numeric_level)

    # Buat handler untuk file log
    log_file = os.path.join(log_dir, f"jupiter_arb_detector_{datetime.now().strftime('%Y-%m-%d')}.log")
    file_handler = logging.FileHandler(log_file)
    file_handler.setLevel(numeric_level)

    # Buat handler untuk console
    console_handler = logging.StreamHandler()
    console_handler.setLevel(numeric_level)

    # Buat formatter
    formatter = logging.Formatter(
        fmt="[%(asctime)s] %(levelname)-8s %(message)-80s %(filename)s:%(lineno)d",
        datefmt="%y/%m/%d %H:%M:%S"
    )
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)

    # Tambahkan handler ke logger
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)

    return logger

def exponential_backoff(attempt: int, base_delay: float = 1.0, max_delay: float = 60.0, jitter: bool = True) -> float:
    """
    Menghitung waktu tunggu dengan exponential backoff.

    Args:
        attempt: Nomor percobaan (0-based)
        base_delay: Waktu tunggu dasar dalam detik
        max_delay: Waktu tunggu maksimum dalam detik
        jitter: Apakah menambahkan jitter

    Returns:
        Waktu tunggu dalam detik
    """
    delay = min(base_delay * (2 ** attempt), max_delay)

    if jitter:
        # Tambahkan jitter (±10%)
        jitter_amount = delay * 0.1
        delay = delay + random.uniform(-jitter_amount, jitter_amount)

    return delay

def format_amount_with_decimals(amount: int, decimals: int) -> str:
    """
    Format jumlah dengan desimal.

    Args:
        amount: Jumlah dalam satuan terkecil (lamports)
        decimals: Jumlah desimal

    Returns:
        String berisi jumlah dengan desimal
    """
    if decimals == 0:
        return str(amount)

    amount_str = str(amount).zfill(decimals + 1)
    integer_part = amount_str[:-decimals] or "0"
    decimal_part = amount_str[-decimals:]

    # Hapus trailing zeros
    decimal_part = decimal_part.rstrip("0")

    if decimal_part:
        return f"{integer_part}.{decimal_part}"
    else:
        return integer_part

def display_opportunity(
    token_symbol: str,
    token_address: str,
    input_amount: float,
    input_value_usd: float,
    output_value_usd: float,
    profit_usd: float,
    profit_percentage: float,
    fee_usd: float = None,
    gas_usd: float = None,
    total_cost_usd: float = None
) -> None:
    """
    Menampilkan peluang swap.

    Args:
        token_symbol: Simbol token
        token_address: Alamat token
        input_amount: Jumlah input (SOL)
        input_value_usd: Nilai input dalam USD
        output_value_usd: Nilai output dalam USD
        profit_usd: Profit dalam USD
        profit_percentage: Profit dalam persentase
        fee_usd: Biaya transaksi dalam USD (opsional)
        gas_usd: Biaya gas dalam USD (opsional)
        total_cost_usd: Total biaya dalam USD (opsional)
    """
    # Tampilkan header dengan desain futuristik
    console.print("\n[bold white on blue]╔══════════════════════════════════════════════════════╗[/]")
    console.print("[bold white on blue]║              🚀 PELUANG SWAP DITEMUKAN! 🚀            ║[/]")
    console.print("[bold white on blue]╚══════════════════════════════════════════════════════╝[/]")

    # Tampilkan informasi token
    console.print(f"\n[bold cyan]📊 DETAIL SWAP:[/]")
    console.print(f"[bold]SOL → {token_symbol}[/bold] ([dim]{token_address[:8]}...{token_address[-6:]}[/dim])")

    # Tampilkan informasi nilai
    console.print(f"\n[bold cyan]💰 NILAI TRANSAKSI:[/]")
    console.print(f"Jumlah SOL: [cyan]{input_amount}[/cyan] SOL")
    console.print(f"Nilai USD awal: [yellow]${input_value_usd:.2f}[/yellow]")
    console.print(f"Nilai USD akhir: [yellow]${output_value_usd:.2f}[/yellow]")

    # Tampilkan informasi biaya jika tersedia
    if fee_usd is not None and gas_usd is not None and total_cost_usd is not None:
        console.print(f"\n[bold cyan]💸 BIAYA TRANSAKSI:[/]")
        console.print(f"Biaya swap: [yellow]${fee_usd:.4f}[/yellow]")
        console.print(f"Biaya gas: [yellow]${gas_usd:.4f}[/yellow]")
        console.print(f"Total biaya: [yellow]${total_cost_usd:.4f}[/yellow]")

    # Tampilkan informasi profit
    console.print(f"\n[bold cyan]✨ PROFIT:[/]")

    # Gunakan warna yang berbeda berdasarkan persentase profit
    profit_color = "green"
    if profit_percentage > 5.0:
        profit_color = "bright_green"
    elif profit_percentage > 2.0:
        profit_color = "green"
    elif profit_percentage > 1.0:
        profit_color = "green3"
    elif profit_percentage > 0.5:
        profit_color = "green4"

    console.print(f"Profit: [bold {profit_color}]${profit_usd:.4f}[/] ([bold {profit_color}]{profit_percentage:.4f}%[/])")

    # Tampilkan link ke Jupiter
    jupiter_link = f"https://jup.ag/swap/SOL-{token_symbol}"
    console.print(f"\n[bold cyan]🔗 LINK JUPITER:[/]")
    console.print(f"[link={jupiter_link}]{jupiter_link}[/link]")

    # Tampilkan footer
    console.print("\n[bold white on blue]╔══════════════════════════════════════════════════════╗[/]")
    console.print("[bold white on blue]║                 VALIDASI SEBELUM SWAP!                ║[/]")
    console.print("[bold white on blue]╚══════════════════════════════════════════════════════╝[/]")
