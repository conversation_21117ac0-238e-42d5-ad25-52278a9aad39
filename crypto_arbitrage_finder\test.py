#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test script for the Cryptocurrency Arbitrage Finder.
"""

import asyncio
import logging
import os
from src.clients.binance_client import BinanceClient
from src.utils.logger_setup import setup_logger

# Setup logger
logger = setup_logger(logging.DEBUG)

async def test_binance_client():
    """Test the Binance WebSocket client."""
    logger.info("Testing Binance WebSocket client")

    # Create Binance client
    client = BinanceClient("wss://stream.binance.com:9443/ws", logger)

    # Start the client
    await client.start()

    # Wait for connection
    await asyncio.sleep(2)

    # Get all symbols
    symbols = await client.get_all_symbols()
    logger.info(f"Found {len(symbols)} symbols on Binance")

    if symbols:
        # Subscribe to order book for a few specific symbols that we know work well
        test_symbols = ['BTC/USDT']
        logger.info(f"Testing with symbols: {test_symbols}")

        # Store the mapping between formatted symbols and original symbols
        symbol_map = {}
        for symbol in test_symbols:
            formatted = client._format_symbol(symbol)
            symbol_map[formatted] = symbol
            logger.info(f"Subscribing to {symbol} (formatted as {formatted})")
            await client.subscribe_to_orderbook(symbol)

        # Wait for some data
        logger.info("Waiting for order book data...")
        await asyncio.sleep(10)

        # Check if we received any data
        for symbol in test_symbols:
            orderbook = await client.get_orderbook_data(symbol)
            if orderbook:
                logger.info(f"Received order book data for {symbol}: bid={orderbook.get('bid')}, ask={orderbook.get('ask')}")
                logger.info(f"Order book bid size: {orderbook.get('bid_size')}, ask size: {orderbook.get('ask_size')}")
                logger.info(f"Order book timestamp: {orderbook.get('datetime')}")

                # Print the first few bids and asks
                bids = orderbook.get('bids', [])
                asks = orderbook.get('asks', [])
                if bids:
                    logger.info(f"Top 3 bids: {bids[:3]}")
                if asks:
                    logger.info(f"Top 3 asks: {asks[:3]}")
            else:
                logger.warning(f"No order book data received for {symbol}")

    # Stop the client
    await client.stop()

    logger.info("Binance WebSocket client test completed")

async def main():
    """Main test function."""
    # Create logs directory if it doesn't exist
    os.makedirs('logs', exist_ok=True)

    # Test Binance client
    await test_binance_client()

if __name__ == "__main__":
    asyncio.run(main())
