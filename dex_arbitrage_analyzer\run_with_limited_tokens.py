"""
Script untuk menjalankan program dengan jumlah token yang terbatas.
"""

import sys
import argparse
from test_config import TEST_TOKENS, TEST_CHAINS

# Override TOKEN_QUERIES dan TARGET_CHAINS
import config
config.TOKEN_QUERIES = TEST_TOKENS

# Override TARGET_CHAINS
import target_chains
target_chains.TARGET_CHAINS = TEST_CHAINS

if __name__ == "__main__":
    # Parse argumen
    parser = argparse.ArgumentParser(description="Program Analisis Arbitrase DEX dengan Token Terbatas")
    parser.add_argument("--mode", type=int, choices=[1, 2, 3], help="Mode analisis: 1=Same Chain, 2=Cross Chain, 3=Keduanya")
    args = parser.parse_args()

    # Import main setelah override konfigurasi
    from main import main

    # Jalankan program utama
    sys.exit(main())
