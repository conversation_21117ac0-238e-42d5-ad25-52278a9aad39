# User Interface
- User wants to improve the UI using the rich library by cleaning up log display and providing detailed output for arbitrage opportunities, including information about supported networks for withdrawals from Binance, Bybit, KuCoin, and OKX, and deposits to KuCoin and OKX.
- User prefers README files to be visually appealing with futuristic design, external images, and casual yet refined language.
- User wants to use the rich library for output in the DEX arbitrage analysis program.
- User prefers output formatted like futuristic WhatsApp trading group or trading signal messages.

# Exchange Connectivity
- User wants to connect to multiple exchanges, including Binance, Bybit, KuCoin, and OKX.
- User wants to add more exchanges to the crypto arbitrage program and suggests researching exchange documentation to use valid endpoints.
- User prefers using real exchange data instead of dummy data and wants thorough WebSocket connection debugging based on official exchange documentation.

# Arbitrage Detection & Execution
- User wants a Python program to find arbitrage opportunities between Binance, Bybit, KuCoin, and OKX exchanges.
- User wants a sophisticated Python program for DEX arbitrage analysis (same-chain and cross-chain) using Dexscreener API.
- User is interested in finding higher profit arbitrage opportunities of 5% or more.
- User prefers realistic arbitrage targets with specific consideration for profit percentage thresholds and capital requirements in Indonesian Rupiah, accounting for all applicable costs.
- User is interested in using 1.5 million Indonesian Rupiah (approximately $90 USD) as capital for crypto arbitrage, focusing on cheap chains with recommended settings and maximizing search queries.
- The arbitrage program should always fetch fresh data when started to ensure opportunities are current.
- User wants to enhance arbitrage detection logic to normalize and compare all trading pairs across exchanges.
- User wants to enhance arbitrage detection logic to verify that trading pairs still exist on all exchanges before displaying them.
- User wants to enhance the crypto arbitrage program by adding OKX exchange and updating the logic to handle 4 exchanges with matching trading pairs.
- User wants to enhance the crypto arbitrage program by improving the logic and adding more token queries since the current implementation found no opportunities despite analyzing many pairs.
- User wants to enhance the crypto arbitrage program with more accurate fee calculations.
- The crypto arbitrage program should validate that coins can be deposited and withdrawn on exchanges before showing arbitrage opportunities.
- User wants to add validation in the DEX arbitrage program to verify if tokens are actively traded and available before showing them as arbitrage opportunities.
- User wants to add a maximum price difference threshold of 50% for arbitrage opportunities to filter out potentially invalid opportunities.
- User wants thorough implementation of improvements with detailed focus on producing valid, executable results for real market trading.
- User prefers code to be written slowly and methodically.
- User wants comprehensive cost calculations in the DEX arbitrage analysis program.
- User wants detailed Indonesian language responses in the DEX arbitrage analysis program.
- User wants to expand the token queries list to 500 tokens in the DEX arbitrage analyzer program.
- User wants to filter out arbitrage opportunities that occur within the same DEX as they may not be valid.
- User wants to avoid stablecoin arbitrage opportunities and other potentially invalid opportunities in the DEX arbitrage analyzer.
- User confirms that arbitrage opportunities between stablecoins like USDC/USDT should be filtered out as they are not valid.
- User wants to filter arbitrage pairs to ensure tokens are actually the same between different DEXes, not just having the same name.
- User wants to optimize and speed up the data retrieval process.
- User prefers modifying existing files directly rather than creating new files for changes.