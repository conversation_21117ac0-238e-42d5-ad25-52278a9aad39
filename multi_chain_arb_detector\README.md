# 🚀 Multi-Chain Arbitrage Detector

![Arbitrage Banner](https://i.imgur.com/XYZ123.png)

## 📊 Deskripsi

**Multi-Chain Arbitrage Detector** adalah program Python canggih untuk mendeteksi peluang arbitrase real-time di berbagai jaringan EVM (Ethereum Virtual Machine). Program ini memantau peluang intra-chain (di dalam satu jaringan) pada beberapa jaringan secara bersamaan atau berurutan.

Program ini **HANYA** mendeteksi dan melaporkan peluang arbitrase, **TIDAK** mengeksekusi perdagangan secara otomatis.

## ✨ Fitur Utama

- 🌐 **Dukungan Multi-Jaringan**: Mendukung 7 jaringan EVM utama (Ethereum, Polygon, BSC, Avalanche, Fantom, Arbitrum, Optimism)
- 🔄 **Deteksi Arbitrase Intra-Chain**: Mendeteksi peluang arbitrase di dalam satu jaringan
- 💹 **Algoritma Arbitrase Canggih**: Mendukung siklus 2-DEX dan 3-DEX
- 💰 **Perhitungan Profit Akurat**: Memperhitungkan biaya gas, slippage, dan faktor lainnya
- ⚡ **Asynchronous**: Menggunakan asyncio untuk pemrosesan paralel
- 🛡️ **Penanganan Error Kuat**: Retry dengan exponential backoff untuk mengatasi masalah RPC
- 📈 **Tampilan Menarik**: Menggunakan Rich untuk tampilan yang informatif dan menarik

## 🔧 Jaringan yang Didukung

- Ethereum (Mainnet)
- Polygon PoS (Mainnet)
- BNB Smart Chain (BSC - Mainnet)
- Avalanche (C-Chain - Mainnet)
- Fantom Opera (Mainnet)
- Arbitrum One (Mainnet)
- Optimism (Mainnet)

## 📋 Persyaratan

- Python 3.8+
- Paket yang tercantum dalam `requirements.txt`

## 🚀 Instalasi

1. Clone repositori ini:
```bash
git clone https://github.com/username/multi_chain_arb_detector.git
cd multi_chain_arb_detector
```

2. Buat dan aktifkan virtual environment:
```bash
python -m venv venv
# Windows
venv\Scripts\activate
# Linux/Mac
source venv/bin/activate
```

3. Instal dependensi:
```bash
pip install -r requirements.txt
```

## ⚙️ Konfigurasi

Semua konfigurasi disimpan dalam file `config.yaml`. Anda dapat menyesuaikan:

- RPC URL untuk setiap jaringan
- Alamat kontrak DEX dan token
- Ambang profit minimum
- Interval pemeriksaan
- Persentase slippage
- Dan banyak lagi

## 🏃‍♂️ Penggunaan

Jalankan program dengan perintah:

```bash
python main.py
```

Program akan mulai memantau peluang arbitrase di semua jaringan yang dikonfigurasi. Tekan `Ctrl+C` untuk menghentikan program dengan aman.

## 📊 Contoh Output

```
╔═══════════════════════════════════════════════════════════════════════════╗
║                                                                           ║
║   Peluang Arbitrase di Ethereum                                           ║
║                                                                           ║
╚═══════════════════════════════════════════════════════════════════════════╝

┏━━━━━━━━┳━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━┓
┃ Langkah ┃ DEX        ┃ Token Masuk     ┃ Token Keluar    ┃
┡━━━━━━━━╇━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━┩
│ 1      │ uniswap_v2 │ 1.000000 WETH   │ 1950.123456 USDC │
│ 2      │ sushiswap  │ 1950.123456 USDC │ 1.020000 WETH   │
└────────┴────────────┴─────────────────┴─────────────────┘

┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
┃ Analisis Profit                                                            ┃
┡━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┩
│ 💰 Profit Kotor: $70.00                                                    │
│ ⛽ Biaya Gas: $15.50                                                       │
│ 💵 Profit Bersih: $54.50                                                   │
│ 📈 Persentase Profit: 2.00%                                                │
└──────────────────────────────────────────────────────────────────────────┘
```

## ⚠️ Catatan Penting

- Program ini menggunakan RPC publik gratis yang mungkin memiliki keterbatasan rate limit
- Untuk penggunaan produksi, disarankan menggunakan RPC berbayar atau node sendiri
- Selalu lakukan uji coba dengan jumlah kecil sebelum melakukan arbitrase dengan jumlah besar
- Harga dan likuiditas di blockchain dapat berubah dengan cepat, jadi peluang arbitrase mungkin tidak bertahan lama

## 📜 Lisensi

Proyek ini dilisensikan di bawah [MIT License](LICENSE).

## 🤝 Kontribusi

Kontribusi, isu, dan permintaan fitur sangat diterima!

## 📧 Kontak

Jika Anda memiliki pertanyaan atau saran, silakan hubungi kami di [<EMAIL>](mailto:<EMAIL>).
