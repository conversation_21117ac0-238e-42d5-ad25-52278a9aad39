#!/usr/bin/env python3
"""
Test Thread-Specific Exchange - Binance Hybrid Signal AI
========================================================

Script untuk testing apakah thread-specific exchange instances mengatasi event loop conflict.
"""

import asyncio
import threading
import time
import logging
from concurrent.futures import ThreadPoolExecutor
import ccxt.async_support as ccxt_async

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(threadName)s - %(message)s'
)

async def fetch_ohlcv_for_timeframe(exchange, pair_symbol, timeframe_str, limit):
    """Mock fetch function untuk testing"""
    try:
        # Simulate network delay
        await asyncio.sleep(0.1)
        
        # Try to fetch actual data (will fail if exchange issues)
        ohlcv = await exchange.fetch_ohlcv(pair_symbol, timeframe_str, limit=limit)
        
        if ohlcv and len(ohlcv) > 0:
            return f"Success: {len(ohlcv)} candles for {pair_symbol} {timeframe_str}"
        else:
            return f"No data for {pair_symbol} {timeframe_str}"
            
    except Exception as e:
        return f"Error: {e}"

async def test_thread_specific_exchange(pair_symbol, thread_id):
    """Test dengan exchange instance baru untuk setiap thread"""
    thread_exchange = None
    
    try:
        print(f"🧵 Thread {thread_id}: Creating new exchange instance for {pair_symbol}")
        
        # Buat exchange instance baru untuk thread ini
        thread_exchange = ccxt_async.binanceusdm({
            'enableRateLimit': True,
            'options': {'defaultType': 'future'},
            'timeout': 30000,
            'rateLimit': 1200,
        })
        
        # Test fetch untuk multiple timeframes
        timeframes = ['1h', '30m', '15m']
        tasks = []
        
        for tf in timeframes:
            task = fetch_ohlcv_for_timeframe(thread_exchange, pair_symbol, tf, 100)
            tasks.append(task)
        
        # Fetch semua timeframes secara async
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        print(f"✅ Thread {thread_id}: Results for {pair_symbol}:")
        for i, tf in enumerate(timeframes):
            result = results[i]
            if isinstance(result, Exception):
                print(f"   ❌ {tf}: {result}")
            else:
                print(f"   ✅ {tf}: {result}")
        
        return True
        
    except Exception as e:
        print(f"❌ Thread {thread_id}: Error for {pair_symbol}: {e}")
        return False
        
    finally:
        # Tutup exchange instance
        if thread_exchange:
            try:
                await thread_exchange.close()
                print(f"🧹 Thread {thread_id}: Exchange closed for {pair_symbol}")
            except Exception as close_e:
                print(f"⚠️ Thread {thread_id}: Error closing exchange: {close_e}")

def run_thread_test(pair_symbol, thread_id):
    """Wrapper untuk menjalankan async test dalam thread"""
    try:
        # Buat loop baru untuk thread ini
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            result = loop.run_until_complete(test_thread_specific_exchange(pair_symbol, thread_id))
            return result
        finally:
            # Cleanup loop
            try:
                if loop.is_running():
                    loop.stop()
                time.sleep(0.1)
                if not loop.is_closed():
                    loop.close()
            except Exception as cleanup_e:
                print(f"⚠️ Thread {thread_id}: Loop cleanup error: {cleanup_e}")
            finally:
                try:
                    asyncio.set_event_loop(None)
                except:
                    pass
                    
    except Exception as e:
        print(f"❌ Thread {thread_id}: Fatal error: {e}")
        return False

def test_concurrent_thread_exchanges():
    """Test concurrent processing dengan thread-specific exchanges"""
    print("=" * 60)
    print("🧪 TESTING THREAD-SPECIFIC EXCHANGE INSTANCES")
    print("=" * 60)
    
    # Test pairs
    test_pairs = [
        'BTC/USDT:USDT', 'ETH/USDT:USDT', 'ADA/USDT:USDT', 
        'BNB/USDT:USDT', 'SOL/USDT:USDT'
    ]
    
    max_workers = 3  # Reduced for testing
    
    print(f"📊 Testing {len(test_pairs)} pairs with {max_workers} workers")
    print("🎯 Each thread creates its own exchange instance")
    print()
    
    results = []
    
    with ThreadPoolExecutor(max_workers=max_workers, thread_name_prefix="ExchangeTest") as executor:
        futures = []
        for i, pair in enumerate(test_pairs):
            future = executor.submit(run_thread_test, pair, i+1)
            futures.append(future)
        
        for i, future in enumerate(futures):
            try:
                result = future.result(timeout=30)
                results.append(result)
                print(f"📈 Pair {i+1}: {'✅ Success' if result else '❌ Failed'}")
            except Exception as e:
                results.append(False)
                print(f"📈 Pair {i+1}: ❌ Exception - {e}")
    
    # Results summary
    print()
    print("=" * 60)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    success_count = sum(results)
    total_tests = len(test_pairs)
    success_rate = success_count / total_tests * 100
    
    print(f"✅ Success: {success_count}/{total_tests} ({success_rate:.1f}%)")
    print(f"❌ Failed:  {total_tests-success_count}/{total_tests} ({(100-success_rate):.1f}%)")
    
    print()
    if success_rate >= 80:
        print("🎉 THREAD-SPECIFIC EXCHANGE: PASSED (≥80% success rate)")
        print("💡 Thread-specific exchange instances are working well!")
    else:
        print("⚠️ THREAD-SPECIFIC EXCHANGE: NEEDS IMPROVEMENT (<80% success rate)")
        print("💡 Consider further optimization or fallback strategies")
    
    print()
    print("🔧 Technical Notes:")
    print("   • Each thread creates its own exchange instance")
    print("   • Each thread has its own event loop")
    print("   • Exchange instances are properly closed after use")
    print("   • This should prevent event loop conflicts")
    
    print("=" * 60)

def main():
    """Main test function"""
    print("🚀 THREAD-SPECIFIC EXCHANGE TESTING - BINANCE HYBRID SIGNAL AI")
    print("=" * 60)
    
    # Test concurrent processing
    test_concurrent_thread_exchanges()
    
    print()
    print("🎉 Testing completed!")
    print("📝 Check results above to verify thread-specific exchanges work")

if __name__ == "__main__":
    main()
