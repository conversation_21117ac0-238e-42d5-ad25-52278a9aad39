{"binance": {"websocket_url": "wss://stream.binance.com:9443/ws", "websocket_stream": "wss://stream.binance.com:9443/stream", "description": "Binance WebSocket API for market data", "docs_url": "https://developers.binance.com/docs/binance-spot-api-docs/web-socket-api"}, "coinbase": {"websocket_url": "wss://ws-feed.exchange.coinbase.com", "description": "Coinbase Pro WebSocket API for market data", "docs_url": "https://docs.cloud.coinbase.com/exchange/docs/websocket-overview"}, "kraken": {"websocket_url": "wss://ws.kraken.com", "description": "Kraken WebSocket API for market data", "docs_url": "https://docs.kraken.com/websockets/"}, "bybit": {"websocket_url": "wss://stream.bybit.com/v5/public/spot", "description": "Bybit WebSocket API for market data", "docs_url": "https://bybit-exchange.github.io/docs/v5/websocket/connect"}, "okx": {"websocket_url": "wss://ws.okx.com:8443/ws/v5/public", "description": "OKX WebSocket API for market data", "docs_url": "https://www.okx.com/docs-v5/en/#websocket-api"}, "kucoin": {"websocket_url": "https://api.kucoin.com", "description": "KuCoin WebSocket API for market data (requires token from REST API)", "docs_url": "https://docs.kucoin.com/#websocket-feed"}, "huobi": {"websocket_url": "wss://api.huobi.pro/ws", "description": "Huobi (HTX) WebSocket API for market data", "docs_url": "https://huobiapi.github.io/docs/spot/v1/en/#websocket-market-data"}, "gate": {"websocket_url": "wss://api.gateio.ws/ws/v4/", "description": "Gate.io WebSocket API for market data", "docs_url": "https://www.gate.io/docs/developers/apiv4/ws/en/"}, "bitget": {"websocket_url": "wss://ws.bitget.com/spot/v1/stream", "description": "Bitget WebSocket API for market data", "docs_url": "https://bitgetlimited.github.io/apidoc/en/spot/#websocket-market-streams"}, "mexc": {"websocket_url": "wss://wbs.mexc.com/ws", "description": "MEXC WebSocket API for market data", "docs_url": "https://mxcdevelop.github.io/apidocs/spot_v3_en/#websocket-market-streams"}, "bitstamp": {"websocket_url": "wss://ws.bitstamp.net", "description": "Bitstamp WebSocket API for market data", "docs_url": "https://www.bitstamp.net/websocket/v2/"}, "bitfinex": {"websocket_url": "wss://api-pub.bitfinex.com/ws/2", "description": "Bitfinex WebSocket API for market data", "docs_url": "https://docs.bitfinex.com/docs/ws-general"}, "crypto_com": {"websocket_url": "wss://stream.crypto.com/v2/market", "description": "Crypto.com Exchange WebSocket API for market data", "docs_url": "https://exchange-docs.crypto.com/spot/index.html#websocket-market-streams"}, "phemex": {"websocket_url": "wss://phemex.com/ws", "description": "Phemex WebSocket API for market data", "docs_url": "https://phemex-docs.github.io/#websocket-api"}, "ascendex": {"websocket_url": "wss://ascendex.com/api/pro/v1/stream", "description": "AscendEX (BitMax) WebSocket API for market data", "docs_url": "https://ascendex.github.io/ascendex-pro-api/#websocket"}, "lbank": {"websocket_url": "wss://www.lbkex.net/ws/V2/", "description": "LBank WebSocket API for market data", "docs_url": "https://www.lbank.com/en-US/docs/index.html#websocket-api-market-data"}, "bingx": {"websocket_url": "wss://open-api-swap.bingx.com/swap-market", "description": "BingX WebSocket API for market data", "docs_url": "https://bingx-api.github.io/docs/#websocket-market-streams"}, "whitebit": {"websocket_url": "wss://api.whitebit.com/ws", "description": "WhiteBIT WebSocket API for market data", "docs_url": "https://whitebit-exchange.github.io/api-docs/websocket/"}, "coinex": {"websocket_url": "wss://socket.coinex.com/", "description": "CoinEx WebSocket API for market data", "docs_url": "https://github.com/coinexcom/coinex_exchange_api/wiki/078WebSocket_API"}, "deepcoin": {"websocket_url": "wss://api.deepcoin.com/wss", "description": "Deepcoin WebSocket API for market data", "docs_url": "https://github.com/deepcoin-exchange/deepcoin-api-docs/blob/master/README.md"}}