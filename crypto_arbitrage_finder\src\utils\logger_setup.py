#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Logger setup module for the Cryptocurrency Arbitrage Finder.
"""

import logging
import os
import sys
from datetime import datetime
from rich.logging import <PERSON><PERSON><PERSON><PERSON>

def setup_logger(log_level=logging.INFO):
    """
    Set up and configure the logger for the application.

    Args:
        log_level: The logging level to use (default: logging.INFO)

    Returns:
        A configured logger instance
    """
    # Create logs directory if it doesn't exist
    os.makedirs('logs', exist_ok=True)

    # Create a logger
    logger = logging.getLogger('crypto_arbitrage')
    logger.setLevel(log_level)

    # Clear any existing handlers
    if logger.handlers:
        logger.handlers.clear()

    # Create Rich console handler for prettier output
    console_handler = RichHandler(
        rich_tracebacks=True,
        markup=True,
        show_time=False,  # We'll include time in our format
        show_path=False,  # Hide file path for cleaner output
        enable_link_path=False
    )
    console_handler.setLevel(log_level)

    # Create formatter for console
    console_formatter = logging.Formatter('%(message)s')
    console_handler.setFormatter(console_formatter)

    # Create file handler for logging to a file
    log_filename = f"logs/arbitrage_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
    file_handler = logging.FileHandler(log_filename)
    file_handler.setLevel(log_level)

    # Create formatter for file handler (without colors)
    file_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

    file_handler.setFormatter(file_formatter)

    # Add handlers to logger
    logger.addHandler(console_handler)
    logger.addHandler(file_handler)

    return logger
