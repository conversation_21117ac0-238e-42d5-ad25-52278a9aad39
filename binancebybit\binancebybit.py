import sys
import logging
import ccxt
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QPushButton,
    QLabel, QScrollArea, QFrame, QHBoxLayout, QGraphicsDropShadowEffect,
    QTextEdit, QDoubleSpinBox, QFormLayout
)
from PySide6.QtCore import QThread, Signal, Qt, QSize
from PySide6.QtGui import QColor, QFont, QTextCursor

# --- Handler Kustom untuk Logging ke GUI ---
class QtLogHandler(logging.Handler):
    """
    Custom logging handler that emits a signal for the GUI to display logs.
    """
    def __init__(self, parent):
        super().__init__()
        self.parent = parent

    def emit(self, record):
        msg = self.format(record)
        self.parent.log_signal.emit(msg)

class ArbitrageWorker(QThread):
    """
    Worker Thread to handle network requests (ccxt) to prevent freezing the GUI.
    Now uses the logging module for detailed progress updates.
    """
    opportunity = Signal(dict)
    finished = Signal(str)
    error = Signal(str)

    def __init__(self, profit_threshold=0.5):
        super().__init__()
        self.profit_threshold = profit_threshold
        self.logger = logging.getLogger(__name__)
        # Define exchanges here to be accessible throughout the class
        self.exchange1_name = 'Binance.US'
        self.exchange2_name = 'Bybit'
        self.exchange1 = None
        self.exchange2 = None


    def run(self):
        try:
            self.logger.info("Memulai sesi pemindaian baru.")
            self.logger.info(f"Ambang batas profit diatur ke: {self.profit_threshold}%")

            # --- KONEKSI BURSA ---
            self.logger.info(f"Menghubungkan ke {self.exchange1_name} dan {self.exchange2_name}...")
            
            # CATATAN PENTING: Binance.com memblokir akses dari beberapa negara (seperti AS).
            # Kode ini diubah untuk menggunakan 'binanceus' yang dirancang untuk pengguna AS.
            # Jika Anda berada di luar AS dan ingin menggunakan Binance utama, ubah 'ccxt.binanceus' menjadi 'ccxt.binance'.
            # Alternatif lain yang populer: ccxt.kraken(), ccxt.kucoin(), ccxt.coinbase()
            self.exchange1 = ccxt.binanceus({
                'options': {'defaultType': 'spot'},
                'enableRateLimit': True,
            })
            
            self.exchange2 = ccxt.bybit({
                'options': {'defaultType': 'spot'},
                'enableRateLimit': True,
            })

            # --- Opsi Tambahan: Menggunakan Proxy ---
            # Jika Anda tetap perlu mengakses bursa yang diblokir, Anda bisa menggunakan proxy.
            # Hilangkan komentar di bawah dan isi detail proxy Anda.
            # proxy_url = '*********************:port' # atau 'socks5h://user:pass@host:port'
            # self.exchange1.proxies = {'http': proxy_url, 'https': proxy_url}
            # self.exchange2.proxies = {'http': proxy_url, 'https': proxy_url}
            # self.logger.info("Menggunakan konfigurasi proxy.")
            
            self.logger.info(f"Berhasil terhubung ke {self.exchange1_name} dan {self.exchange2_name}.")

            self.logger.info(f"Mengambil data pasar dari {self.exchange1_name}...")
            exchange1_markets = self.exchange1.load_markets()
            self.logger.info(f"{self.exchange1_name}: Ditemukan {len(exchange1_markets)} pasar.")
            
            self.logger.info(f"Mengambil data pasar dari {self.exchange2_name}...")
            exchange2_markets = self.exchange2.load_markets()
            self.logger.info(f"{self.exchange2_name}: Ditemukan {len(exchange2_markets)} pasar.")

            self.logger.info("Mencari pasangan trading USDT yang sama...")
            common_symbols = set(exchange1_markets.keys()) & set(exchange2_markets.keys())
            spot_symbols = [s for s in common_symbols if s.endswith('/USDT') and 'UP/' not in s and 'DOWN/' not in s]
            self.logger.info(f"Menemukan {len(spot_symbols)} pasangan USDT yang sama untuk dianalisis.")
            
            self.logger.info("Mengambil semua data mata uang/jaringan (optimasi)...")
            exchange1_currencies = self.exchange1.fetch_currencies()
            exchange2_currencies = self.exchange2.fetch_currencies()
            self.logger.info("Data mata uang berhasil diambil.")

            count = 0
            opportunities_found = 0
            total_symbols = len(spot_symbols)
            for symbol in spot_symbols:
                count += 1
                self.logger.debug(f"Menganalisis ({count}/{total_symbols}): {symbol}")
                try:
                    # Fetch order book tickers for the most accurate bid/ask prices
                    ticker1 = self.exchange1.fetch_ticker(symbol)
                    ticker2 = self.exchange2.fetch_ticker(symbol)
                    
                    # --- Case 1: Buy on Exchange1, Sell on Exchange2 ---
                    buy_price_1_2 = ticker1.get('ask')
                    sell_price_1_2 = ticker2.get('bid')

                    if buy_price_1_2 and sell_price_1_2 and buy_price_1_2 > 0:
                        profit_percent = ((sell_price_1_2 - buy_price_1_2) / buy_price_1_2) * 100
                        if profit_percent > self.profit_threshold:
                            self.logger.info(f"Potensi ditemukan di {symbol} (Beli {self.exchange1_name}, Jual {self.exchange2_name}): {profit_percent:.2f}%")
                            if self.check_and_emit_opportunity(
                                symbol, self.exchange1_name, self.exchange2_name, buy_price_1_2, sell_price_1_2, profit_percent,
                                exchange1_currencies, exchange2_currencies
                            ):
                                opportunities_found += 1
                    
                    # --- Case 2: Buy on Exchange2, Sell on Exchange1 ---
                    buy_price_2_1 = ticker2.get('ask')
                    sell_price_2_1 = ticker1.get('bid')

                    if buy_price_2_1 and sell_price_2_1 and buy_price_2_1 > 0:
                        profit_percent = ((sell_price_2_1 - buy_price_2_1) / buy_price_2_1) * 100
                        if profit_percent > self.profit_threshold:
                             self.logger.info(f"Potensi ditemukan di {symbol} (Beli {self.exchange2_name}, Jual {self.exchange1_name}): {profit_percent:.2f}%")
                             if self.check_and_emit_opportunity(
                                symbol, self.exchange2_name, self.exchange1_name, buy_price_2_1, sell_price_2_1, profit_percent,
                                exchange2_currencies, exchange1_currencies
                            ):
                                opportunities_found += 1

                except ccxt.NetworkError as e:
                    self.logger.warning(f"Kesalahan jaringan saat mengambil {symbol}: {e}. Melanjutkan...")
                    continue
                except ccxt.BaseError as e:
                    self.logger.warning(f"Gagal mengambil ticker untuk {symbol}: {e}. Melanjutkan...")
                    continue
        
        except ccxt.BaseError as e:
            self.error.emit(f"Terjadi kesalahan fatal pada CCXT: {e}")
            self.finished.emit("Gagal")
            return
        except Exception as e:
            self.error.emit(f"Terjadi kesalahan tidak terduga: {e}")
            self.finished.emit("Gagal")
            return

        self.logger.info("Pemindaian Selesai.")
        self.finished.emit(f"Selesai! Ditemukan {opportunities_found} peluang yang valid.")
        
    def check_and_emit_opportunity(self, symbol, buy_exchange_name, sell_exchange_name, buy_price, sell_price, profit, buy_exchange_currencies, sell_exchange_currencies):
        base_currency_code = symbol.split('/')[0]
        
        buy_currency_details = buy_exchange_currencies.get(base_currency_code)
        sell_currency_details = sell_exchange_currencies.get(base_currency_code)

        if not buy_currency_details:
            self.logger.debug(f"Diabaikan: Tidak ada detail mata uang untuk {base_currency_code} di {buy_exchange_name}.")
            return False
        if not sell_currency_details:
            self.logger.debug(f"Diabaikan: Tidak ada detail mata uang untuk {base_currency_code} di {sell_exchange_name}.")
            return False

        withdraw_active = buy_currency_details.get('active') and buy_currency_details.get('withdraw')
        if not withdraw_active:
            self.logger.warning(f"Peluang {symbol} diabaikan: Penarikan dinonaktifkan di {buy_exchange_name}.")
            return False

        deposit_active = sell_currency_details.get('active') and sell_currency_details.get('deposit')
        if not deposit_active:
            self.logger.warning(f"Peluang {symbol} diabaikan: Deposit dinonaktifkan di {sell_exchange_name}.")
            return False
            
        buy_networks = set(buy_currency_details.get('networks', {}).keys())
        sell_networks = set(sell_currency_details.get('networks', {}).keys())
        common_networks = list(buy_networks & sell_networks)

        if not common_networks:
            self.logger.warning(f"Peluang {symbol} diabaikan: Tidak ditemukan jaringan transfer yang sama.")
            self.logger.debug(f"Jaringan {buy_exchange_name}: {buy_networks}")
            self.logger.debug(f"Jaringan {sell_exchange_name}: {sell_networks}")
            return False

        self.logger.info(f"VALIDASI BERHASIL untuk {symbol}! Jaringan sama: {common_networks}.")
        self.opportunity.emit({
            'symbol': symbol,
            'buy_exchange': buy_exchange_name,
            'sell_exchange': sell_exchange_name,
            'buy_price': buy_price,
            'sell_price': sell_price,
            'profit': profit,
            'common_networks': common_networks,
        })
        return True


class MainWindow(QMainWindow):
    log_signal = Signal(str)

    def __init__(self):
        super().__init__()
        self.setWindowTitle("Pemindai Arbitrase Pro")
        self.setGeometry(100, 100, 420, 800) 

        # --- Setup Logger ---
        self.log_handler = QtLogHandler(self)
        self.log_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
        logging.getLogger().addHandler(self.log_handler)
        logging.getLogger().setLevel(logging.DEBUG)

        # --- iOS Style Theme ---
        self.setStyleSheet("""
            QMainWindow { background-color: #f0f2f5; }
            QLabel { font-family: 'Helvetica Neue', Arial; }
            #TitleLabel { font-size: 34px; font-weight: bold; color: #1c1c1e; padding: 20px 20px 5px 20px; }
            #StatusLabel { font-size: 16px; font-weight: 500; color: #3c3c43; padding: 0 20px 15px 20px; }
            #ScanButton { background-color: #007aff; color: white; font-size: 17px; font-weight: 600; border: none; border-radius: 12px; padding: 14px; }
            #ScanButton:disabled { background-color: #a0c7ff; }
            QScrollArea { border: none; }
            #Card { background-color: white; border-radius: 12px; margin: 8px 20px; }
            #CardSymbol { font-size: 18px; font-weight: 600; color: #1c1c1e; }
            #CardProfit { font-size: 18px; font-weight: 600; color: #34c759; }
            #LogBox { font-family: 'Menlo', 'Courier New'; font-size: 11px; background-color: #f9f9f9; border: 1px solid #e0e0e0; border-radius: 8px; color: #555; }
            QDoubleSpinBox { border: 1px solid #ccc; border-radius: 8px; padding: 8px; background-color: white; }
            QFormLayout QLabel { font-size: 14px; font-weight: 500; color: #3c3c43; }
        """)

        # --- Main Layout ---
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        self.main_layout = QVBoxLayout(self.central_widget)
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.main_layout.setSpacing(0)

        # --- Header ---
        self.title_label = QLabel("Arbitrase Spot")
        self.title_label.setObjectName("TitleLabel")
        self.status_label = QLabel("Siap untuk memulai pemindaian.")
        self.status_label.setObjectName("StatusLabel")
        self.main_layout.addWidget(self.title_label)
        self.main_layout.addWidget(self.status_label)

        # --- Controls Area (Threshold & Button) ---
        controls_frame = QFrame()
        controls_layout = QHBoxLayout(controls_frame)
        controls_layout.setContentsMargins(20, 0, 20, 15)
        form_layout = QFormLayout()
        self.profit_spinbox = QDoubleSpinBox()
        self.profit_spinbox.setSuffix(" %")
        self.profit_spinbox.setSingleStep(0.1)
        self.profit_spinbox.setValue(0.5)
        self.profit_spinbox.setDecimals(2)
        form_layout.addRow("Profit Min:", self.profit_spinbox)
        controls_layout.addLayout(form_layout)
        controls_layout.addStretch()
        self.scan_button = QPushButton("Pindai Peluang")
        self.scan_button.setObjectName("ScanButton")
        self.scan_button.clicked.connect(self.start_scan)
        controls_layout.addWidget(self.scan_button)
        self.main_layout.addWidget(controls_frame)

        # --- Scroll Area for Results ---
        self.scroll_area = QScrollArea()
        self.scroll_widget = QWidget()
        self.results_layout = QVBoxLayout(self.scroll_widget)
        self.results_layout.setAlignment(Qt.AlignmentFlag.AlignTop)
        self.scroll_area.setWidget(self.scroll_widget)
        self.main_layout.addWidget(self.scroll_area, 1) # Give it stretch factor

        # --- Log Box ---
        log_label = QLabel("Log Aktivitas:")
        log_label.setStyleSheet("font-size: 16px; font-weight: 600; color: #1c1c1e; padding: 15px 20px 5px 20px;")
        self.log_box = QTextEdit()
        self.log_box.setObjectName("LogBox")
        self.log_box.setReadOnly(True)
        self.log_box.setLineWrapMode(QTextEdit.LineWrapMode.NoWrap)
        log_box_frame = QFrame()
        log_box_layout = QVBoxLayout(log_box_frame)
        log_box_layout.setContentsMargins(20,0,20,10)
        log_box_layout.addWidget(self.log_box)
        self.main_layout.addWidget(log_label)
        self.main_layout.addWidget(log_box_frame, 1) # Give it stretch factor

        # --- Connect Signals ---
        self.log_signal.connect(self.append_log)

    def append_log(self, message):
        self.log_box.append(message)
        self.log_box.moveCursor(QTextCursor.MoveOperation.End)

    def start_scan(self):
        while self.results_layout.count():
            child = self.results_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()
        
        self.log_box.clear()
        self.scan_button.setDisabled(True)
        self.scan_button.setText("Memindai...")
        self.status_label.setText("Memulai proses...")
        
        profit_threshold = self.profit_spinbox.value()
        self.worker = ArbitrageWorker(profit_threshold=profit_threshold)
        self.worker.opportunity.connect(self.add_opportunity_card)
        self.worker.finished.connect(self.scan_finished)
        self.worker.error.connect(self.scan_error)
        self.worker.start()

    def scan_finished(self, final_message):
        self.scan_button.setDisabled(False)
        self.scan_button.setText("Pindai Lagi")
        self.status_label.setText(final_message)

    def scan_error(self, message):
        logging.error(f"FATAL: {message}")
        self.scan_button.setDisabled(False)
        self.scan_button.setText("Coba Lagi")
        self.status_label.setText("Terjadi kesalahan fatal. Cek log.")

    def add_opportunity_card(self, data):
        card = QFrame()
        card.setObjectName("Card")
        card_layout = QVBoxLayout(card)
        shadow = QGraphicsDropShadowEffect(card); shadow.setBlurRadius(25); shadow.setColor(QColor(0, 0, 0, 40)); shadow.setOffset(0, 5); card.setGraphicsEffect(shadow)
        top_layout = QHBoxLayout()
        top_layout.addWidget(QLabel(data['symbol']),_ = setattr(top_layout.itemAt(0).widget(), "objectName", "CardSymbol"))
        top_layout.addStretch()
        top_layout.addWidget(QLabel(f"+{data['profit']:.2f}%"),_ = setattr(top_layout.itemAt(2).widget(), "objectName", "CardProfit"))
        card_layout.addLayout(top_layout)
        card_layout.addSpacing(10)
        def create_info_row(label_text, value_text):
            row = QHBoxLayout(); row.addWidget(QLabel(label_text)); row.itemAt(0).widget().setStyleSheet("color: #8a8a8e;"); value = QLabel(str(value_text)); value.setStyleSheet("font-weight: 500; color: #1c1c1e;"); value.setAlignment(Qt.AlignmentFlag.AlignRight); row.addWidget(value); return row
        card_layout.addLayout(create_info_row("Beli di:", data['buy_exchange']))
        card_layout.addLayout(create_info_row("Harga Beli:", f"${data['buy_price']:.6f}"))
        card_layout.addLayout(create_info_row("Jual di:", data['sell_exchange']))
        card_layout.addLayout(create_info_row("Harga Jual:", f"${data['sell_price']:.6f}"))
        card_layout.addSpacing(10)
        net_layout = QHBoxLayout(); net_layout.addWidget(QLabel("✅")); net_layout.itemAt(0).widget().setStyleSheet("font-size: 16px;"); net_layout.addWidget(QLabel("Jaringan Sama:")); net_layout.itemAt(1).widget().setStyleSheet("color: #8a8a8e;"); net_val = QLabel(', '.join(data['common_networks'])); net_val.setWordWrap(True); net_val.setAlignment(Qt.AlignmentFlag.AlignRight); net_val.setStyleSheet("font-weight: 500; color: #34c759;"); net_layout.addStretch(); net_layout.addWidget(net_val); card_layout.addLayout(net_layout)
        self.results_layout.addWidget(card)
        self.status_label.setText(f"Ditemukan {self.results_layout.count()} peluang valid!")

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec())
