"""
Utility functions for Jupiter DEX Arbitrage Analyzer
"""

import logging
import time
from datetime import datetime
from rich.console import Console
from rich.logging import <PERSON>Handler
from rich.panel import Panel
from rich.table import Table
from rich.text import Text

# Setup logging
def setup_logging(log_file="jupiter_arbitrage.log"):
    """Setup logging configuration with Rich formatting"""
    logging.basicConfig(
        level=logging.INFO,
        format="%(message)s",
        datefmt="[%X]",
        handlers=[
            RichHandler(rich_tracebacks=True, markup=True),
            logging.FileHandler(log_file, encoding="utf-8")
        ]
    )
    return logging.getLogger("jupiter_arbitrage")

# Console for rich output
console = Console()

def format_price(price, decimals=6):
    """Format price with appropriate decimals"""
    if price is None:
        return "N/A"
    return f"{float(price):.{decimals}f}"

def format_percentage(percentage, decimals=2):
    """Format percentage with appropriate decimals"""
    if percentage is None:
        return "N/A"
    return f"{float(percentage):.{decimals}f}%"

def format_usd_value(value, decimals=2):
    """Format USD value with appropriate decimals"""
    if value is None:
        return "N/A"
    return f"${float(value):.{decimals}f}"

def calculate_profit_percentage(buy_price, sell_price, slippage_percentage=5.0):
    """Calculate profit percentage including slippage costs"""
    if buy_price is None or sell_price is None or float(buy_price) == 0:
        return None
    
    # Calculate raw profit percentage
    raw_profit_percentage = (float(sell_price) / float(buy_price) - 1) * 100
    
    # Subtract slippage cost
    net_profit_percentage = raw_profit_percentage - slippage_percentage
    
    return net_profit_percentage

def calculate_profit_usd(capital_usd, profit_percentage):
    """Calculate profit in USD based on capital and profit percentage"""
    if capital_usd is None or profit_percentage is None:
        return None
    
    return capital_usd * profit_percentage / 100

def display_header():
    """Display header for the application"""
    title = Text("JUPITER DEX ARBITRAGE ANALYZER", style="bold cyan")
    subtitle = Text("Mencari peluang arbitrase di Solana menggunakan Jupiter DEX Aggregator", style="italic")
    
    header_panel = Panel(
        f"{title}\n{subtitle}",
        border_style="cyan",
        expand=False
    )
    
    console.print(header_panel)
    console.print(f"[bold green]Waktu Mulai:[/] {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    console.print("")

def display_arbitrage_opportunities(opportunities, sol_price_usd):
    """Display arbitrage opportunities in a rich table"""
    if not opportunities:
        console.print("[yellow]Tidak ditemukan peluang arbitrase yang memenuhi kriteria.[/]")
        return
    
    table = Table(title="Peluang Arbitrage Terdeteksi", show_header=True, header_style="bold magenta")
    
    # Add columns
    table.add_column("No", style="dim", width=4)
    table.add_column("Token", style="cyan")
    table.add_column("Symbol", style="green")
    table.add_column("Route", style="yellow")
    table.add_column("Harga Beli (USD)", justify="right")
    table.add_column("Harga Jual (USD)", justify="right")
    table.add_column("Profit (%)", justify="right")
    table.add_column("Profit (USD)", justify="right")
    table.add_column("Volume 24h", justify="right", style="dim")
    
    # Add rows
    for i, opp in enumerate(opportunities, 1):
        capital_usd = sol_price_usd * opp.get("capital_amount_sol", 1.0)
        profit_usd = calculate_profit_usd(capital_usd, opp.get("profit_percentage"))
        
        table.add_row(
            str(i),
            opp.get("token_name", "Unknown"),
            opp.get("token_symbol", "???"),
            f"SOL → {opp.get('token_symbol', '???')} → SOL",
            format_usd_value(opp.get("buy_price_usd")),
            format_usd_value(opp.get("sell_price_usd")),
            format_percentage(opp.get("profit_percentage")),
            format_usd_value(profit_usd),
            format_usd_value(opp.get("volume_24h"))
        )
    
    console.print(table)
    
    # Display summary
    console.print(f"[bold green]Total Peluang:[/] {len(opportunities)}")
    console.print(f"[bold blue]Harga SOL:[/] {format_usd_value(sol_price_usd)}")
    console.print(f"[bold yellow]Modal:[/] {format_usd_value(sol_price_usd)} (1 SOL)")
    console.print("")

def display_token_info(token_info):
    """Display detailed token information"""
    if not token_info:
        console.print("[yellow]Informasi token tidak tersedia.[/]")
        return
    
    panel = Panel(
        f"[bold cyan]Token:[/] {token_info.get('name', 'Unknown')}\n"
        f"[bold green]Symbol:[/] {token_info.get('symbol', '???')}\n"
        f"[bold yellow]Address:[/] {token_info.get('address', 'Unknown')}\n"
        f"[bold magenta]Decimals:[/] {token_info.get('decimals', 'Unknown')}\n"
        f"[bold blue]Tags:[/] {', '.join(token_info.get('tags', []))}\n"
        f"[bold]Volume 24h:[/] {format_usd_value(token_info.get('daily_volume'))}\n",
        title="Token Information",
        border_style="green"
    )
    
    console.print(panel)

def display_progress(current, total, message="Memproses token"):
    """Display progress bar"""
    console.print(f"[bold blue]{message}:[/] {current}/{total} ({(current/total*100):.1f}%)")

def measure_execution_time(func):
    """Decorator to measure execution time of a function"""
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        execution_time = end_time - start_time
        console.print(f"[dim]Execution time for {func.__name__}: {execution_time:.2f} seconds[/]")
        return result
    return wrapper
