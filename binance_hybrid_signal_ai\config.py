"""
Binance Hybrid Signal AI - Configuration
========================================

Konfigurasi untuk mengatasi masalah geo-blocking dan exchange connectivity
"""

# --- Exchange Configuration ---
EXCHANGE_CONFIG = {
    'primary': {
        'id': 'binanceusdm',
        'name': 'Binance USDM Futures',
        'config': {
            'enableRateLimit': True,
            'options': {'defaultType': 'future'},
            'timeout': 30000,
            'rateLimit': 1200,
            'headers': {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
        }
    },
    'alternatives': [
        {
            'id': 'bybit',
            'name': 'Bybit Linear Futures',
            'config': {
                'enableRateLimit': True,
                'options': {'defaultType': 'linear'},
                'timeout': 30000,
                'rateLimit': 1000,
            }
        },
        {
            'id': 'okx',
            'name': 'OKX Futures',
            'config': {
                'enableRateLimit': True,
                'options': {'defaultType': 'swap'},
                'timeout': 30000,
                'rateLimit': 1000,
            }
        },
        {
            'id': 'binance',
            'name': 'Binance Spot (Fallback)',
            'config': {
                'enableRateLimit': True,
                'options': {'defaultType': 'spot'},
                'timeout': 30000,
                'rateLimit': 1200,
            }
        }
    ]
}

# --- Geo-blocking Solutions ---
GEO_BLOCKING_SOLUTIONS = """
🚫 GEO-BLOCKING DETECTED!
📍 Lokasi Anda dibatasi untuk mengakses Binance API.

💡 SOLUSI YANG DIREKOMENDASIKAN:

1. 🌐 GUNAKAN VPN:
   • ExpressVPN (Rekomendasi #1)
   • NordVPN 
   • Surfshark
   • ProtonVPN
   
   Pilih server di negara yang tidak dibatasi:
   • Singapura, Hong Kong, Jepang
   • Eropa (Jerman, Belanda, Swiss)
   • Amerika (US, Kanada)

2. 🔄 EXCHANGE ALTERNATIF:
   • Bybit (Tersedia di Indonesia)
   • OKX (Global access)
   • Gate.io
   • KuCoin
   
3. 📧 HUBUNGI CUSTOMER SERVICE:
   • Binance Support: <EMAIL>
   • Jelaskan bahwa Anda menggunakan untuk analisis

4. 🏠 LOKASI ALTERNATIF:
   • Gunakan dari lokasi yang tidak dibatasi
   • Cafe/coworking space dengan VPN

⚠️ Program akan otomatis mencoba exchange alternatif...
"""

# --- Demo Mode Configuration ---
DEMO_MODE_CONFIG = {
    'enabled': False,  # Set True untuk testing tanpa koneksi
    'pairs': ['BTCUSDT', 'ETHUSDT', 'ADAUSDT', 'BNBUSDT', 'SOLUSDT', 'DOGEUSDT', 'XRPUSDT'],
    'mock_data': {
        'price_range': (20000, 70000),  # Range harga untuk mock data
        'volume_range': (1000000, 10000000),
        'volatility': 0.05  # 5% volatility
    }
}

# --- Network Configuration ---
NETWORK_CONFIG = {
    'timeout': 30,  # seconds
    'max_retries': 3,
    'retry_delay': 2,  # seconds
    'rate_limit_buffer': 1.2,  # 20% buffer untuk rate limiting
}

# --- Proxy Configuration (Optional) ---
PROXY_CONFIG = {
    'enabled': False,  # Set True jika menggunakan proxy
    'http': None,      # 'http://proxy:port'
    'https': None,     # 'https://proxy:port'
    'auth': None,      # ('username', 'password') jika perlu auth
}

# --- Error Messages ---
ERROR_MESSAGES = {
    'geo_blocking': "Geo-blocking detected. Gunakan VPN atau exchange alternatif.",
    'network_timeout': "Network timeout. Periksa koneksi internet Anda.",
    'rate_limit': "Rate limit exceeded. Menunggu sebelum retry...",
    'exchange_error': "Exchange error. Coba lagi atau gunakan exchange alternatif.",
    'no_data': "Tidak ada data tersedia untuk pair ini.",
    'insufficient_data': "Data tidak cukup untuk analisis teknikal."
}

# --- Success Messages ---
SUCCESS_MESSAGES = {
    'connection_success': "✅ Berhasil terhubung ke exchange!",
    'data_loaded': "✅ Data market berhasil dimuat!",
    'analysis_complete': "✅ Analisis selesai!",
    'alternative_exchange': "✅ Berhasil menggunakan exchange alternatif!"
}

# --- Logging Configuration ---
LOGGING_CONFIG = {
    'level': 'INFO',
    'format': '%(asctime)s - %(levelname)s - %(threadName)s - %(message)s',
    'console_output': True,
    'file_output': True,
    'file_name': 'binance_signals.log',
    'max_file_size': 10 * 1024 * 1024,  # 10MB
    'backup_count': 5
}

def get_exchange_config(exchange_id):
    """Get configuration for specific exchange"""
    if exchange_id == EXCHANGE_CONFIG['primary']['id']:
        return EXCHANGE_CONFIG['primary']['config']
    
    for alt in EXCHANGE_CONFIG['alternatives']:
        if alt['id'] == exchange_id:
            return alt['config']
    
    return None

def get_alternative_exchanges():
    """Get list of alternative exchanges"""
    return [(alt['id'], alt['name']) for alt in EXCHANGE_CONFIG['alternatives']]

def is_geo_blocking_error(error_message):
    """Check if error is related to geo-blocking"""
    geo_keywords = [
        '451', 'restricted location', 'service unavailable',
        'not available in your region', 'geo-blocked',
        'access denied', 'forbidden', 'location restricted'
    ]
    
    error_lower = str(error_message).lower()
    return any(keyword in error_lower for keyword in geo_keywords)

def get_user_agent():
    """Get random user agent for requests"""
    user_agents = [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    ]
    
    import random
    return random.choice(user_agents)
