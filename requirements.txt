# IntelliTrader X - Advanced AI-Powered Crypto Trading Signal Engine
# Requirements File - Install with: pip install -r requirements.txt

# Core GUI Framework
PySide6>=6.5.0

# Data Processing & Analysis
numpy>=1.24.0
pandas>=2.0.0
scipy>=1.10.0

# HTTP & API Clients
aiohttp>=3.8.0
requests>=2.31.0
websockets>=15.0

# Technical Analysis
ta>=0.10.0
TA-Lib>=0.4.25

# Trading & Exchange APIs
ccxt>=3.0.0

# Utilities
rich>=13.7.0
pyperclip>=1.8.0
colorama>=0.4.6
python-dateutil>=2.8.2

# Async Programming
asyncio-throttle>=1.0.2

# JSON & Configuration
jsonschema>=4.17.0

# Optional: For enhanced performance
numba>=0.57.0
