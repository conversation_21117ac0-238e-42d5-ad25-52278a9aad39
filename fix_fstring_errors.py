#!/usr/bin/env python3
"""
Script to fix all f-string errors by replacing them with safer alternatives
"""

import re
import os

def fix_fstring_errors(file_path):
    """Replace problematic f-strings with safer alternatives"""
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Backup original
    backup_path = file_path + '.backup'
    with open(backup_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    # Replace f-strings that contain dict access, method calls, or complex expressions
    replacements = [
        # f"text {dict['key']}" -> "text " + str(dict['key'])
        (r'f"([^"]*)\{([^}]*\[[^}]*\][^}]*)\}([^"]*)"', r'"\1" + str(\2) + "\3"'),
        
        # f"text {obj.get('key')}" -> "text " + str(obj.get('key'))
        (r'f"([^"]*)\{([^}]*\.get\([^}]*\)[^}]*)\}([^"]*)"', r'"\1" + str(\2) + "\3"'),
        
        # f"text {var:.2f}" -> "text " + "{:.2f}".format(var)
        (r'f"([^"]*)\{([^}]*):([^}]*)\}([^"]*)"', r'"\1" + "{\3}".format(\2) + "\4"'),
        
        # f"text {var}" -> "text " + str(var)
        (r'f"([^"]*)\{([^}]*)\}([^"]*)"', r'"\1" + str(\2) + "\3"'),
    ]
    
    original_content = content
    
    for pattern, replacement in replacements:
        content = re.sub(pattern, replacement, content)
    
    # Write fixed content
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    changes_made = content != original_content
    return changes_made

if __name__ == "__main__":
    file_path = r"binance_hybrid_signal_ai\binance3timeframe (1).py"
    
    if os.path.exists(file_path):
        print(f"Fixing f-string errors in {file_path}...")
        changes_made = fix_fstring_errors(file_path)
        
        if changes_made:
            print("✅ F-string errors fixed!")
            print("📁 Backup created: " + file_path + ".backup")
        else:
            print("ℹ️ No changes needed")
    else:
        print(f"❌ File not found: {file_path}")
