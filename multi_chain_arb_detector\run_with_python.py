"""
File untuk menjalankan program utama dengan Python secara langsung.
"""
import os
import sys
import subprocess

def main():
    """
    Fungsi utama untuk menjalankan program.
    """
    print("Menjalankan program utama dengan Python secara langsung...")
    
    # Dapatkan direktori script
    script_dir = os.path.dirname(os.path.abspath(__file__))
    print(f"Direktori script: {script_dir}")
    
    # Ubah direktori kerja ke direktori script
    os.chdir(script_dir)
    print(f"Direktori kerja diubah ke: {os.getcwd()}")
    
    # Cek apakah file main.py ada
    main_path = os.path.join(script_dir, "main.py")
    print(f"Path main.py: {main_path}")
    
    if os.path.exists(main_path):
        print("File main.py ditemukan!")
        
        # Jalankan program dengan subprocess
        try:
            print("\nMemulai program utama...")
            print("=" * 50)
            
            # Jalankan program dengan subprocess
            process = subprocess.Popen(
                [sys.executable, main_path],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # Tampilkan output secara real-time
            while True:
                output = process.stdout.readline()
                if output == '' and process.poll() is not None:
                    break
                if output:
                    print(output.strip())
            
            # Tampilkan error jika ada
            stderr = process.stderr.read()
            if stderr:
                print("Error:")
                print(stderr)
            
            # Dapatkan kode keluar
            return_code = process.poll()
            print(f"Program selesai dengan kode keluar: {return_code}")
            
            print("=" * 50)
            print("Program selesai.")
        except Exception as e:
            print(f"Error saat menjalankan program: {e}")
            import traceback
            traceback.print_exc()
    else:
        print("File main.py tidak ditemukan!")

if __name__ == "__main__":
    main()
