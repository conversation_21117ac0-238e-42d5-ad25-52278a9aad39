# Solana RPC endpoint (gunakan endpoint yang cepat dan andal)
RPC_URL=https://api.mainnet-beta.solana.com

# Parameter arbitrase
MIN_PROFIT_THRESHOLD=0.5  # Minimum profit percentage
SLIPPAGE_BPS=50  # 0.5% slippage
MAX_CONCURRENT_REQUESTS=10
REQUEST_TIMEOUT=10  # Timeout in seconds
REFRESH_INTERVAL=5  # Refresh interval in seconds

# Token amount for quote (in lamports/smallest unit)
QUOTE_AMOUNT=10000000  # 0.01 SOL in lamports

# UI Configuration
UI_REFRESH_RATE=1  # UI refresh rate in seconds
MAX_OPPORTUNITIES_DISPLAYED=10

# Maximum number of tokens to check for arbitrage
MAX_TOKENS_TO_CHECK=100

# Throttling parameters
RATE_LIMIT_PER_SECOND=5
