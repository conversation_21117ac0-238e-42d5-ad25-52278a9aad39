# Crypto Arbitrage Scanner - Environment Variables
# Copy this file to .env and fill in your API credentials

# Binance API (Optional - for enhanced features)
BINANCE_API_KEY=your_binance_api_key_here
BINANCE_SECRET=your_binance_secret_here

# Bybit API (Optional - for enhanced features)
BYBIT_API_KEY=your_bybit_api_key_here
BYBIT_SECRET=your_bybit_secret_here

# KuCoin API (Optional - for enhanced features)
KUCOIN_API_KEY=your_kucoin_api_key_here
KUCOIN_SECRET=your_kucoin_secret_here
KUCOIN_PASSPHRASE=your_kucoin_passphrase_here

# OKX API (Optional - for enhanced features)
OKX_API_KEY=your_okx_api_key_here
OKX_SECRET=your_okx_secret_here
OKX_PASSPHRASE=your_okx_passphrase_here

# Note: API keys are optional. The scanner works with public data,
# but API keys provide access to more detailed information and higher rate limits.
