"""
File untuk menjalankan program dengan token dan DEX yang paling populer.
"""
import os
import sys
import time
import asyncio
import traceback

async def main():
    """
    Fungsi utama untuk menjalankan program.
    """
    print("Menjalankan program dengan token dan DEX yang paling populer...")

    # Dapatkan direktori script
    script_dir = os.path.dirname(os.path.abspath(__file__))

    # Tambahkan direktori saat ini ke sys.path
    sys.path.append(script_dir)

    # Impor modul-modul
    from src.utils import load_config, setup_logging
    from src.network import Network
    from src.arbitrage import ArbitrageDetector

    # Muat konfigurasi
    config_path = os.path.join(script_dir, "config.yaml")
    config = load_config(config_path)

    # Dapatkan pengaturan
    settings = config.get('settings', {})

    # Setup logging
    logs_dir = os.path.join(script_dir, "logs")
    os.makedirs(logs_dir, exist_ok=True)
    logger = setup_logging(settings.get('log_level', 'INFO'), logs_dir)

    # Daftar token yang paling populer
    popular_tokens = {
        "ethereum": {
            "WETH": "******************************************",
            "USDC": "******************************************",
            "USDT": "******************************************",
            "DAI": "******************************************",
            "WBTC": "******************************************"
        }
    }

    # Perbarui konfigurasi dengan token yang populer
    for network_name, tokens in popular_tokens.items():
        if network_name in config['networks']:
            config['networks'][network_name]['tokens'] = tokens

    # Pastikan konfigurasi DEX tetap dalam format yang benar
    # Kita tetap menggunakan konfigurasi DEX yang ada di config.yaml

    # Modifikasi pengaturan
    settings['max_tokens_per_network'] = 5  # Batasi jumlah token
    settings['max_token_pairs'] = 20  # Batasi jumlah pasangan token
    settings['max_token_triplets'] = 10  # Batasi jumlah triplet token
    settings['token_pair_batch_size'] = 5  # Ukuran batch lebih kecil
    settings['token_triplet_batch_size'] = 2  # Ukuran batch lebih kecil
    settings['filter_stablecoins'] = False  # Jangan filter stablecoin
    settings['max_price_difference_percentage'] = 100.0  # Perbedaan harga maksimum lebih besar
    settings['min_profit_usd'] = 0.01  # Profit minimum lebih kecil

    # Inisialisasi Network
    network = Network('ethereum', config['networks']['ethereum'], settings)

    # Cek koneksi
    if network.w3.is_connected():
        print("Berhasil terhubung ke jaringan Ethereum!")

        # Inisialisasi detektor arbitrase
        detector = ArbitrageDetector(network, config['networks']['ethereum'], settings)

        # Jalankan deteksi arbitrase
        print("\nMenjalankan deteksi arbitrase...")
        print("Ini akan memakan waktu beberapa saat...")

        # Jalankan dengan timeout
        try:
            await asyncio.wait_for(detector.detect_arbitrage_opportunities(), timeout=120)
            print("Deteksi arbitrase selesai!")

            # Cek apakah ada peluang arbitrase
            if detector.arbitrage_opportunities:
                print(f"\nDitemukan {len(detector.arbitrage_opportunities)} peluang arbitrase!")

                # Tampilkan peluang arbitrase
                for i, opportunity in enumerate(detector.arbitrage_opportunities):
                    print(f"\n{i+1}. Jalur: {' -> '.join(opportunity['token_path'])}")
                    print(f"   DEX: {' -> '.join(opportunity['dex_path'])}")
                    print(f"   Profit: ${opportunity['net_profit_usd']:.2f} ({opportunity['profit_percentage']:.2f}%)")
            else:
                print("\nTidak ditemukan peluang arbitrase.")
        except asyncio.TimeoutError:
            print("Timeout! Deteksi arbitrase berjalan terlalu lama.")
    else:
        print("Gagal terhubung ke jaringan Ethereum.")

if __name__ == "__main__":
    # Jalankan dengan asyncio
    asyncio.run(main())
