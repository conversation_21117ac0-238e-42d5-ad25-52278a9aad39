"""
Konfigurasi untuk pengujian fee real-time.
"""

# Token untuk pengujian
TEST_TOKENS = [
    "BTC",
    "ETH",
    "BNB",
    "MATIC",
    # "SOL" dihapus sesuai permintaan
    "AVAX",
    "FTM",
    "LINK",
    "UNI",
    "AAVE",
    "CAKE",
    "SUSHI",
    "CRV",
    "BAL",
    "PEPE",
    "DOGE",
    "SHIB",
    "FLOKI",
    # "WIF" dihapus sesuai permintaan (token Solana)
    # "BONK" dihapus sesuai permintaan (token Solana)
]

# Chain untuk pengujian
TEST_CHAINS = [
    "polygon",
    "bsc",
    "fantom",
    # "solana" dihapus sesuai permintaan
    "arbitrum",
    "optimism"
]
