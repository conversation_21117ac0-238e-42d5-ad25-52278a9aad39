#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Configuration loader for the Cryptocurrency Arbitrage Finder.
"""

import json
import logging
import os

class ConfigLoader:
    """Configuration loader for the application."""

    def __init__(self, logger=None):
        """
        Initialize the configuration loader.

        Args:
            logger (logging.Logger, optional): Logger instance
        """
        self.logger = logger or logging.getLogger('crypto_arbitrage.config_loader')
        # Use absolute path for config directory
        import os
        self.config_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 'config')
        self.endpoints = None
        self.fees_networks = None

    def load_endpoints(self):
        """
        Load WebSocket endpoints from configuration file.

        Returns:
            dict: Dictionary of exchange endpoints
        """
        if self.endpoints is not None:
            return self.endpoints

        try:
            config_path = os.path.join(self.config_dir, 'endpoints.json')
            self.logger.debug(f"Loading endpoints from {config_path}")
            with open(config_path, 'r') as f:
                self.endpoints = json.load(f)

            self.logger.info(f"Loaded endpoints for {len(self.endpoints)} exchanges")
            return self.endpoints
        except Exception as e:
            self.logger.error(f"Failed to load endpoints: {e}")
            return {}

    def load_fees_networks(self):
        """
        Load fees and networks information from configuration file.

        Returns:
            dict: Dictionary of exchange fees and networks
        """
        if self.fees_networks is not None:
            return self.fees_networks

        try:
            config_path = os.path.join(self.config_dir, 'fees_networks.json')
            self.logger.debug(f"Loading fees and networks from {config_path}")
            with open(config_path, 'r') as f:
                self.fees_networks = json.load(f)

            self.logger.info(f"Loaded fees and networks information for {len(self.fees_networks.get('exchanges', {}))} exchanges")
            return self.fees_networks
        except Exception as e:
            self.logger.error(f"Failed to load fees and networks information: {e}")
            return {'exchanges': {}}

    def get_exchange_taker_fee(self, exchange_id):
        """
        Get the taker fee for an exchange.

        Args:
            exchange_id (str): The ID of the exchange

        Returns:
            float: The taker fee as a decimal (e.g., 0.001 for 0.1%)
        """
        # Define fees for all exchanges (simulated values)
        exchange_fees = {
            'binance': 0.001,    # 0.10%
            'coinbase': 0.0015,  # 0.15%
            'kraken': 0.0016,    # 0.16%
            'bybit': 0.0006,     # 0.06%
            'okx': 0.0008,       # 0.08%
            'kucoin': 0.001,     # 0.10%
            'huobi': 0.002,      # 0.20%
            'gate': 0.002,       # 0.20%
            'bitget': 0.0006,    # 0.06%
            'mexc': 0.002,       # 0.20%
            'bitstamp': 0.0025,  # 0.25%
            'bitfinex': 0.002,   # 0.20%
            'crypto_com': 0.0004, # 0.04%
            'phemex': 0.001,     # 0.10%
            'ascendex': 0.001,   # 0.10%
            'lbank': 0.001,      # 0.10%
            'bingx': 0.001,      # 0.10%
            'whitebit': 0.001,   # 0.10%
            'coinex': 0.002,     # 0.20%
            'deepcoin': 0.0004   # 0.04%
        }

        # Return the fee for the specified exchange, or default to 0.1%
        return exchange_fees.get(exchange_id, 0.001)

    def get_withdrawal_networks(self, exchange_id, asset):
        """
        Get the supported withdrawal networks for an asset on an exchange.

        Args:
            exchange_id (str): The ID of the exchange
            asset (str): The asset symbol (e.g., 'BTC')

        Returns:
            list: List of supported networks
        """
        if self.fees_networks is None:
            self.load_fees_networks()

        try:
            return self.fees_networks.get('exchanges', {}).get(exchange_id, {}).get('withdrawal_networks', {}).get(asset, [])
        except Exception as e:
            self.logger.error(f"Failed to get withdrawal networks for {asset} on {exchange_id}: {e}")
            return []

    def get_withdrawal_fee(self, exchange_id, asset, network):
        """
        Get the withdrawal fee for an asset on a specific network.

        Args:
            exchange_id (str): The ID of the exchange
            asset (str): The asset symbol (e.g., 'BTC')
            network (str): The network (e.g., 'BTC', 'ETH', 'TRX')

        Returns:
            float: The withdrawal fee
        """
        # Define standard withdrawal fees for common assets
        standard_fees = {
            'BTC': 0.0001,
            'ETH': 0.005,
            'BNB': 0.01,
            'SOL': 0.01,
            'XRP': 0.25,
            'ADA': 1.0,
            'DOGE': 5.0,
            'AVAX': 0.01,
            'DOT': 0.1,
            'MATIC': 0.1
        }

        # Define exchange-specific fee multipliers (some exchanges have higher/lower fees)
        exchange_multipliers = {
            'binance': 1.0,
            'coinbase': 1.2,
            'kraken': 0.9,
            'bybit': 1.1,
            'okx': 0.8,
            'kucoin': 1.0,
            'huobi': 1.1,
            'gate': 0.9,
            'bitget': 1.2,
            'mexc': 1.0,
            'bitstamp': 1.3,
            'bitfinex': 0.8,
            'crypto_com': 1.1,
            'phemex': 0.9,
            'ascendex': 1.0,
            'lbank': 1.2,
            'bingx': 1.0,
            'whitebit': 0.9,
            'coinex': 1.1,
            'deepcoin': 1.0
        }

        # Get the standard fee for the asset (or a small default fee)
        standard_fee = standard_fees.get(asset, 0.00001)

        # Apply the exchange-specific multiplier
        multiplier = exchange_multipliers.get(exchange_id, 1.0)

        # Return the calculated fee
        return standard_fee * multiplier

    def find_common_networks(self, exchange_a, exchange_b, asset):
        """
        Find common networks for an asset between two exchanges.

        Args:
            exchange_a (str): The ID of the first exchange
            exchange_b (str): The ID of the second exchange
            asset (str): The asset symbol (e.g., 'BTC')

        Returns:
            list: List of common networks
        """
        # Define standard networks for common assets
        asset_networks = {
            'BTC': ['BTC', 'Lightning'],
            'ETH': ['ETH', 'Arbitrum', 'Optimism'],
            'BNB': ['BEP2', 'BEP20', 'BSC'],
            'SOL': ['SOL'],
            'XRP': ['XRP'],
            'ADA': ['ADA'],
            'DOGE': ['DOGE'],
            'AVAX': ['AVAX', 'C-Chain'],
            'DOT': ['DOT'],
            'MATIC': ['MATIC', 'Polygon']
        }

        # Define exchange support for networks
        # 1 means fully supported, 0 means not supported
        exchange_support = {
            'binance': {
                'BTC': 1, 'Lightning': 1, 'ETH': 1, 'Arbitrum': 1, 'Optimism': 1,
                'BEP2': 1, 'BEP20': 1, 'BSC': 1, 'SOL': 1, 'XRP': 1, 'ADA': 1,
                'DOGE': 1, 'AVAX': 1, 'C-Chain': 1, 'DOT': 1, 'MATIC': 1, 'Polygon': 1
            },
            'coinbase': {
                'BTC': 1, 'Lightning': 0, 'ETH': 1, 'Arbitrum': 1, 'Optimism': 1,
                'BEP2': 0, 'BEP20': 0, 'BSC': 0, 'SOL': 1, 'XRP': 0, 'ADA': 1,
                'DOGE': 1, 'AVAX': 1, 'C-Chain': 1, 'DOT': 1, 'MATIC': 1, 'Polygon': 1
            },
            'kraken': {
                'BTC': 1, 'Lightning': 1, 'ETH': 1, 'Arbitrum': 0, 'Optimism': 0,
                'BEP2': 0, 'BEP20': 0, 'BSC': 0, 'SOL': 1, 'XRP': 1, 'ADA': 1,
                'DOGE': 1, 'AVAX': 1, 'C-Chain': 0, 'DOT': 1, 'MATIC': 1, 'Polygon': 0
            }
        }

        # Default support for other exchanges (assume they support main networks)
        default_support = {
            'BTC': 1, 'Lightning': 0, 'ETH': 1, 'Arbitrum': 0, 'Optimism': 0,
            'BEP2': 0, 'BEP20': 1, 'BSC': 1, 'SOL': 1, 'XRP': 1, 'ADA': 1,
            'DOGE': 1, 'AVAX': 1, 'C-Chain': 0, 'DOT': 1, 'MATIC': 1, 'Polygon': 0
        }

        # Get networks for the asset
        networks = asset_networks.get(asset, ['Default'])

        # Get support for exchange A
        support_a = exchange_support.get(exchange_a, default_support)

        # Get support for exchange B
        support_b = exchange_support.get(exchange_b, default_support)

        # Find common supported networks
        common_networks = []
        for network in networks:
            if support_a.get(network, 0) == 1 and support_b.get(network, 0) == 1:
                common_networks.append(network)

        # If no common networks found, return a default network
        if not common_networks:
            return ['Default']

        return common_networks
