"""
Modul untuk memformat output menggunakan rich.
"""

import logging
from typing import Dict, List, Any, Optional
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.text import Text
from rich.box import DOUBLE, ROUNDED
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TimeElapsedColumn
from rich import print as rprint

from utils import (
    format_address, format_price, format_percentage, format_usd, format_idr,
    get_dexscreener_pair_url, get_execution_instructions
)
from config import TOP_OPPORTUNITIES
from dex_fee_fetcher import DEFAULT_DEX_FEES, DEX_FEE_CACHE

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("output_formatter")

# Inisialisasi console
console = Console()

def print_header():
    """Menampilkan header program."""
    header_text = """
    ╔═════════════════════════════════════════════════════════════════════════════╗
    ║                                                                             ║
    ║   ██████╗ ███████╗██╗  ██╗     █████╗ ██████╗ ██████╗ ██╗████████╗██████╗   ║
    ║   ██╔══██╗██╔════╝╚██╗██╔╝    ██╔══██╗██╔══██╗██╔══██╗██║╚══██╔══╝██╔══██╗  ║
    ║   ██║  ██║█████╗   ╚███╔╝     ███████║██████╔╝██████╔╝██║   ██║   ██████╔╝  ║
    ║   ██║  ██║██╔══╝   ██╔██╗     ██╔══██║██╔══██╗██╔══██╗██║   ██║   ██╔══██╗  ║
    ║   ██████╔╝███████╗██╔╝ ██╗    ██║  ██║██║  ██║██████╔╝██║   ██║   ██║  ██║  ║
    ║   ╚═════╝ ╚══════╝╚═╝  ╚═╝    ╚═╝  ╚═╝╚═╝  ╚═╝╚═════╝ ╚═╝   ╚═╝   ╚═╝  ╚═╝  ║
    ║                                                                             ║
    ║                    ANALYZER & OPPORTUNITY DETECTOR                          ║
    ║                            by bobacheese                                    ║
    ╚═════════════════════════════════════════════════════════════════════════════╝
    """
    console.print(header_text, style="bold cyan")
    console.print("\n[bold yellow]Program Analisis Arbitrase DEX - Powered by Dexscreener API[/bold yellow]\n")

def print_mode_selection():
    """Menampilkan pilihan mode operasi."""
    console.print("\n[bold green]Pilih Mode Operasi:[/bold green]")
    console.print("[1] Arbitrase Sesama Jaringan (Same-Chain)")
    console.print("[2] Arbitrase Beda Jaringan (Cross-Chain)")
    console.print("[3] Kedua Mode (Same-Chain & Cross-Chain)")

    return console.input("\n[bold cyan]Pilihan Anda (1/2/3): [/bold cyan]")

def create_progress_bar(description: str) -> Progress:
    """
    Membuat progress bar.

    Args:
        description: Deskripsi progress bar.

    Returns:
        Progress bar.
    """
    return Progress(
        SpinnerColumn(),
        TextColumn("[bold blue]{task.description}"),
        BarColumn(),
        TextColumn("[bold green]{task.completed} dari {task.total}"),
        TimeElapsedColumn(),
    )

def print_opportunity(opportunity: Dict[str, Any], index: int):
    """
    Menampilkan detail peluang arbitrase.

    Args:
        opportunity: Data peluang arbitrase.
        index: Indeks peluang.
    """
    # Tentukan warna berdasarkan tipe peluang
    if opportunity["type"] == "same_chain":
        title_style = "bold green"
        panel_style = "green"
    else:  # cross_chain
        title_style = "bold magenta"
        panel_style = "magenta"

    # Buat judul
    if opportunity["type"] == "same_chain":
        title = f"Peluang #{index+1} - Arbitrase Sesama Jaringan ({opportunity['chain_id'].upper()})"
    else:  # cross_chain
        title = f"Peluang #{index+1} - Arbitrase Beda Jaringan ({opportunity['source_chain'].upper()} → {opportunity['target_chain'].upper()})"

    # Buat tabel informasi token
    token_table = Table(show_header=True, header_style="bold cyan", box=ROUNDED)
    token_table.add_column("Token", style="cyan")
    token_table.add_column("Symbol", style="green")
    token_table.add_column("Address", style="yellow")

    base_token = opportunity["base_token"]
    quote_token = opportunity["quote_token"]

    token_table.add_row(
        "Base",
        base_token.get("symbol", "N/A"),
        format_address(base_token.get("address", "N/A"))
    )
    token_table.add_row(
        "Quote",
        quote_token.get("symbol", "N/A"),
        format_address(quote_token.get("address", "N/A"))
    )

    # Buat tabel informasi harga dan likuiditas
    price_table = Table(show_header=True, header_style="bold cyan", box=ROUNDED)
    price_table.add_column("Metrik", style="cyan")
    price_table.add_column("Beli", style="green")
    price_table.add_column("Jual", style="yellow")
    price_table.add_column("Perbedaan", style="magenta")

    price_table.add_row(
        "DEX",
        opportunity["buy_dex"].upper(),
        opportunity["sell_dex"].upper(),
        ""
    )

    if opportunity["type"] == "cross_chain":
        price_table.add_row(
            "Chain",
            opportunity["source_chain"].upper(),
            opportunity["target_chain"].upper(),
            ""
        )

    price_table.add_row(
        "Harga (USD)",
        format_price(opportunity["buy_price"]),
        format_price(opportunity["sell_price"]),
        format_percentage(opportunity["price_diff_percentage"])
    )

    price_table.add_row(
        "Likuiditas (USD)",
        format_usd(opportunity["buy_liquidity"]),
        format_usd(opportunity["sell_liquidity"]),
        ""
    )

    # Buat tabel informasi biaya dan profit
    profit_table = Table(show_header=True, header_style="bold cyan", box=ROUNDED)
    profit_table.add_column("Metrik", style="cyan")
    profit_table.add_column("Nilai", style="green")

    profit_table.add_row("Modal", format_usd(opportunity["capital"]))
    profit_table.add_row("Slippage Beli", format_percentage(opportunity["buy_slippage"]))
    profit_table.add_row("Slippage Jual", format_percentage(opportunity["sell_slippage"]))
    profit_table.add_row("Biaya Slippage (5%)", format_usd(opportunity["slippage_cost"]))
    profit_table.add_row("Fee DEX Beli", format_usd(opportunity["buy_fee"]))
    profit_table.add_row("Fee DEX Jual", format_usd(opportunity["sell_fee"]))

    if opportunity["type"] == "same_chain":
        profit_table.add_row("Biaya Gas", format_usd(opportunity["gas_cost"]))
    else:  # cross_chain
        profit_table.add_row("Biaya Gas (Source)", format_usd(opportunity["source_gas_cost"]))
        profit_table.add_row("Biaya Gas (Target)", format_usd(opportunity["target_gas_cost"]))
        profit_table.add_row("Biaya Bridge", format_usd(opportunity["bridge_cost"]))

    profit_table.add_row("Total Biaya", format_usd(opportunity["total_cost"]))
    profit_table.add_row("Profit Kotor", format_usd(opportunity["gross_profit"]))
    profit_table.add_row("Profit Bersih", format_usd(opportunity["net_profit"]))
    profit_table.add_row("Profit Bersih (IDR)", format_idr(opportunity["net_profit_idr"]))
    profit_table.add_row("ROI", format_percentage(opportunity["roi_percentage"]))

    if opportunity.get("volatility") is not None:
        profit_table.add_row("Volatilitas 1 Jam", format_percentage(opportunity["volatility"]))

    # Buat tabel tautan
    link_table = Table(show_header=True, header_style="bold cyan", box=ROUNDED)
    link_table.add_column("DEX", style="cyan")
    link_table.add_column("Tautan Dexscreener", style="blue")

    link_table.add_row(
        opportunity["buy_dex"].upper(),
        get_dexscreener_pair_url(opportunity["buy_pair"])
    )
    link_table.add_row(
        opportunity["sell_dex"].upper(),
        get_dexscreener_pair_url(opportunity["sell_pair"])
    )

    # Buat panel instruksi eksekusi
    instructions = get_execution_instructions(opportunity)
    instruction_text = "\n".join(instructions)
    instruction_panel = Panel(
        instruction_text,
        title="[bold yellow]Instruksi Eksekusi Manual[/bold yellow]",
        border_style="yellow",
        expand=False
    )

    # Tampilkan semua informasi
    console.print(f"\n[{title_style}]{title}[/{title_style}]")
    console.print(token_table)
    console.print(price_table)
    console.print(profit_table)
    console.print(link_table)
    console.print(instruction_panel)

    # Tambahkan garis pemisah
    console.print("\n" + "=" * 80 + "\n")

def print_opportunities(opportunities: List[Dict[str, Any]], limit: int = TOP_OPPORTUNITIES):
    """
    Menampilkan daftar peluang arbitrase.

    Args:
        opportunities: List peluang arbitrase.
        limit: Jumlah peluang yang ditampilkan.
    """
    if not opportunities:
        console.print("\n[bold red]Tidak ada peluang arbitrase yang ditemukan.[/bold red]")
        return

    # Hapus duplikasi tambahan berdasarkan pasangan token dan DEX
    unique_opportunities = []
    seen_pairs = set()

    for opp in opportunities:
        # Buat kunci unik untuk setiap peluang
        base_symbol = opp.get("base_token", {}).get("symbol", "").upper()
        quote_symbol = opp.get("quote_token", {}).get("symbol", "").upper()
        chain_id = opp.get("chain_id", "")
        buy_dex = opp.get("buy_dex", "").upper()
        sell_dex = opp.get("sell_dex", "").upper()

        # Buat kunci yang tidak terpengaruh oleh urutan DEX
        dex_pair = tuple(sorted([buy_dex, sell_dex]))

        # Buat kunci unik
        key = f"{chain_id}:{base_symbol}:{quote_symbol}:{dex_pair[0]}:{dex_pair[1]}"

        if key not in seen_pairs:
            seen_pairs.add(key)
            unique_opportunities.append(opp)

    # Log jumlah peluang yang dihapus
    removed_count = len(opportunities) - len(unique_opportunities)
    if removed_count > 0:
        logger.info(f"Menghapus {removed_count} peluang duplikat tambahan saat menampilkan output")

    # Batasi jumlah peluang yang ditampilkan
    opportunities_to_show = unique_opportunities[:limit]

    console.print(f"\n[bold green]Ditemukan {len(unique_opportunities)} peluang arbitrase unik. Menampilkan {len(opportunities_to_show)} teratas:[/bold green]\n")

    # Tampilkan setiap peluang
    for i, opportunity in enumerate(opportunities_to_show):
        print_opportunity(opportunity, i)

def print_statistics(statistics: Dict[str, Any]):
    """
    Menampilkan statistik penyaringan.

    Args:
        statistics: Dict berisi statistik.
    """
    stats_table = Table(title="Statistik Penyaringan", show_header=True, header_style="bold cyan", box=DOUBLE)
    stats_table.add_column("Metrik", style="cyan")
    stats_table.add_column("Nilai", style="green")

    stats_table.add_row("Total Pasangan Token", str(statistics["total_pairs"]))
    stats_table.add_row("Jumlah Chain Unik", str(statistics["unique_chains"]))
    stats_table.add_row("Jumlah DEX Unik", str(statistics["unique_dexes"]))
    stats_table.add_row("Peluang Sesama Chain", str(statistics["same_chain_opportunities"]))
    stats_table.add_row("Peluang Beda Chain", str(statistics["cross_chain_opportunities"]))
    stats_table.add_row("Total Peluang", str(statistics["total_opportunities"]))

    console.print(stats_table)

def print_dex_fees():
    """
    Menampilkan informasi tentang fee DEX yang digunakan dalam program.
    """
    # Buat tabel untuk fee DEX default
    default_fee_table = Table(title="Fee DEX Default", show_header=True, header_style="bold cyan", box=DOUBLE)
    default_fee_table.add_column("DEX", style="cyan")
    default_fee_table.add_column("Chain", style="green")
    default_fee_table.add_column("Fee (%)", style="yellow")

    # Tambahkan baris untuk setiap DEX
    for dex_id, fee in sorted(DEFAULT_DEX_FEES.items()):
        # Ekstrak chain dari dex_id jika ada
        chain = "All"
        if "-" in dex_id:
            parts = dex_id.split("-")
            if len(parts) > 1 and parts[-1] not in ["v2", "v3"]:
                chain = parts[-1].capitalize()

        default_fee_table.add_row(
            dex_id.split("-")[0].capitalize(),
            chain,
            format_percentage(fee)
        )

    # Buat tabel untuk fee DEX real-time yang telah di-cache
    if DEX_FEE_CACHE:
        cache_fee_table = Table(title="Fee DEX Real-Time (Cache)", show_header=True, header_style="bold cyan", box=DOUBLE)
        cache_fee_table.add_column("DEX", style="cyan")
        cache_fee_table.add_column("Chain", style="green")
        cache_fee_table.add_column("Pair Address", style="blue")
        cache_fee_table.add_column("Fee (%)", style="yellow")
        cache_fee_table.add_column("Last Updated", style="magenta")

        # Tambahkan baris untuk setiap entry di cache
        import time
        for cache_key, cache_entry in sorted(DEX_FEE_CACHE.items()):
            parts = cache_key.split(":")
            if len(parts) >= 3:
                dex_id = parts[0]
                pair_address = parts[1]
                chain_id = parts[2]

                # Format timestamp
                timestamp = cache_entry["timestamp"]
                time_diff = time.time() - timestamp
                if time_diff < 60:
                    time_str = f"{int(time_diff)} detik yang lalu"
                elif time_diff < 3600:
                    time_str = f"{int(time_diff / 60)} menit yang lalu"
                else:
                    time_str = f"{int(time_diff / 3600)} jam yang lalu"

                cache_fee_table.add_row(
                    dex_id.split("-")[0].capitalize(),
                    chain_id.capitalize(),
                    format_address(pair_address),
                    format_percentage(cache_entry["fee"]),
                    time_str
                )

        # Tampilkan tabel cache
        console.print("\n[bold blue]Informasi Fee DEX Real-Time:[/bold blue]")
        console.print(cache_fee_table)

    # Tampilkan tabel default
    console.print("\n[bold blue]Informasi Fee DEX Default:[/bold blue]")
    console.print(default_fee_table)

    # Tampilkan catatan
    console.print("\n[bold yellow]Catatan:[/bold yellow]")
    console.print("1. Fee DEX real-time diambil dari API DEX jika tersedia.")
    console.print("2. Jika tidak tersedia, program menggunakan fee default dari mapping.")
    console.print("3. Fee DEX di-cache selama 1 jam untuk mengurangi jumlah request API.")

def print_error(message: str):
    """
    Menampilkan pesan error.

    Args:
        message: Pesan error.
    """
    console.print(f"\n[bold red]ERROR: {message}[/bold red]")

def print_warning(message: str):
    """
    Menampilkan pesan peringatan.

    Args:
        message: Pesan peringatan.
    """
    console.print(f"\n[bold yellow]PERINGATAN: {message}[/bold yellow]")

def print_info(message: str):
    """
    Menampilkan pesan informasi.

    Args:
        message: Pesan informasi.
    """
    console.print(f"\n[bold blue]INFO: {message}[/bold blue]")

def print_success(message: str):
    """
    Menampilkan pesan sukses.

    Args:
        message: Pesan sukses.
    """
    console.print(f"\n[bold green]SUKSES: {message}[/bold green]")
