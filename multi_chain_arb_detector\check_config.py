"""
File untuk memeriksa apakah file konfigurasi dapat ditemukan.
"""
import os
import sys

def main():
    """
    Fungsi utama untuk memeriksa file konfigurasi.
    """
    print("Memeriksa file konfigurasi...")
    
    # Dapatkan direktori script
    script_dir = os.path.dirname(os.path.abspath(__file__))
    print(f"Direktori script: {script_dir}")
    
    # Cek apakah file config.yaml ada
    config_path = os.path.join(script_dir, "config.yaml")
    print(f"Path konfigurasi: {config_path}")
    
    if os.path.exists(config_path):
        print("File config.yaml ditemukan!")
        
        # Cek ukuran file
        file_size = os.path.getsize(config_path)
        print(f"Ukuran file: {file_size} bytes")
        
        # Coba baca beberapa baris pertama
        try:
            with open(config_path, 'r') as file:
                first_lines = [next(file) for _ in range(5)]
            print("5 baris pertama dari file:")
            for i, line in enumerate(first_lines):
                print(f"{i+1}: {line.strip()}")
        except Exception as e:
            print(f"Error saat membaca file: {e}")
    else:
        print("File config.yaml tidak ditemukan!")
        
        # Tampilkan daftar file di direktori
        print("Daftar file di direktori:")
        for file in os.listdir(script_dir):
            print(f"  - {file}")
    
    # Cek direktori abis
    abis_dir = os.path.join(script_dir, "abis")
    print(f"\nPath direktori abis: {abis_dir}")
    
    if os.path.exists(abis_dir) and os.path.isdir(abis_dir):
        print("Direktori abis ditemukan!")
        
        # Tampilkan daftar file di direktori abis
        print("Daftar file di direktori abis:")
        for file in os.listdir(abis_dir):
            print(f"  - {file}")
    else:
        print("Direktori abis tidak ditemukan!")
    
    # Cek direktori logs
    logs_dir = os.path.join(script_dir, "logs")
    print(f"\nPath direktori logs: {logs_dir}")
    
    if os.path.exists(logs_dir) and os.path.isdir(logs_dir):
        print("Direktori logs ditemukan!")
    else:
        print("Direktori logs tidak ditemukan!")
        
        # Coba buat direktori logs
        try:
            os.makedirs(logs_dir, exist_ok=True)
            print("Direktori logs berhasil dibuat!")
        except Exception as e:
            print(f"Error saat membuat direktori logs: {e}")

if __name__ == "__main__":
    main()
