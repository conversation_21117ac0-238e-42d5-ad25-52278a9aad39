"""
Jupiter Opportunity Hunter - Program untuk mendeteksi peluang arbitrase di Jupiter Aggregator.
"""
import asyncio
import os
import time
import logging
from decimal import Decimal
from typing import Dict, List, Optional
import aiohttp
from rich.console import Console
from rich.layout import Layout
from rich.panel import Panel
from rich.table import Table
from rich.text import Text
from rich.live import Live
from rich.logging import <PERSON>Handler
from rich import box
from solana.rpc.async_api import AsyncClient

import config

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    datefmt="[%X]",
    handlers=[
        RichHandler(rich_tracebacks=True),
        logging.FileHandler("jupiter_hunter.log", mode="w")
    ]
)
log = logging.getLogger("jupiter_hunter")

# Create console
console = Console()

# Token cache
token_cache: Dict[str, Dict] = {}
price_cache: Dict[str, Decimal] = {}

class JupiterClient:
    """Client for interacting with Jupiter API."""

    def __init__(self):
        """Initialize the Jupiter client."""
        self.session = None
        self.solana_client = AsyncClient(config.RPC_URL)
        self.rate_limiter = asyncio.Semaphore(config.RATE_LIMIT_PER_SECOND)

    async def initialize(self):
        """Initialize the client session."""
        if self.session is None:
            self.session = aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=config.REQUEST_TIMEOUT))

    async def close(self):
        """Close the client session."""
        if self.session:
            await self.session.close()
            self.session = None
        await self.solana_client.close()

    async def _rate_limited_request(self, method, url, **kwargs):
        """Make a rate-limited request to the API."""
        async with self.rate_limiter:
            retry_count = 0
            max_retries = 3

            while retry_count < max_retries:
                try:
                    log.info(f"Making request to {url}")
                    async with getattr(self.session, method)(url, **kwargs) as response:
                        if response.status == 200:
                            result = await response.json()
                            log.info(f"Request to {url} successful")
                            return result
                        elif response.status == 429:  # Rate limit
                            retry_count += 1
                            wait_time = 2 ** retry_count  # Exponential backoff
                            log.warning(f"Rate limit hit, retrying in {wait_time}s: {url}")
                            await asyncio.sleep(wait_time)
                        else:
                            log.error(f"API request failed: {response.status} - {await response.text()}")
                            return None
                except asyncio.TimeoutError:
                    retry_count += 1
                    wait_time = 2 ** retry_count
                    log.error(f"Request timeout: {url}, retrying in {wait_time}s ({retry_count}/{max_retries})")
                    await asyncio.sleep(wait_time)
                except Exception as e:
                    log.error(f"Request error: {url} - {str(e)}")
                    return None

            log.error(f"Max retries reached for {url}")
            return None

    async def get_quote(self, input_mint: str, output_mint: str, amount: int) -> Optional[Dict]:
        """Get a quote for swapping from input_mint to output_mint."""
        try:
            url = f"{config.JUPITER_QUOTE_ENDPOINT}?inputMint={input_mint}&outputMint={output_mint}&amount={amount}&slippageBps={config.SLIPPAGE_BPS}"
            result = await self._rate_limited_request("get", url)
            return result
        except Exception as e:
            log.error(f"Error saat mendapatkan quote untuk {input_mint} -> {output_mint}: {str(e)}")
            return None

    async def get_tradable_tokens(self) -> List[str]:
        """Get a list of tradable tokens on Jupiter."""
        try:
            log.info("Mengambil daftar token yang dapat diperdagangkan dari Jupiter API...")
            result = await self._rate_limited_request("get", config.JUPITER_TOKENS_ENDPOINT)
            if result:
                log.info(f"Berhasil mendapatkan {len(result)} token dari Jupiter API")
                return result
            log.warning("Tidak dapat mengambil daftar token, menggunakan daftar fallback")
        except Exception as e:
            log.error(f"Error saat mengambil daftar token: {str(e)}")
            log.warning("Menggunakan daftar fallback token")

        # Fallback to a list of popular tokens if API fails
        fallback_tokens = [
            "So11111111111111111111111111111111111111112",  # SOL
            "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",  # USDC
            "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB",  # USDT
            "7dHbWXmci3dT8UFYWYZweBLXgycu7Y3iL6trKn1Y7ARj",  # stSOL
            "mSoLzYCxHdYgdzU16g5QSh3i5K3z3KZK7ytfqcJm7So",  # mSOL
            "7vfCXTUXx5WJV5JADk17DUJ4ksgau7utNKj4b963voxs",  # ETH (Wormhole)
            "DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263",  # BONK
            "JUPyiwrYJFskUPiHa7hkeR8VUtAeFoSYbKedZNsDvCN",  # JUP
            "7i5KKsX2weiTkry7jA4ZwSuXGhs5eJBEjY8vVxR4pfRx",  # GMT
            "7xKXtg2CW87d97TXJSDpbD5jBkheTqA83TZRuJosgAsU",  # SAMO
            "9n4nbM75f5Ui33ZbPYXn59EwSgE8CGsHtAeTH5YFeJ9E",  # BTC (Wrapped)
            "2FPyTwcZLUg1MDrwsyoP4D6s1tM7hAkHYRjkNb5w6Pxk",  # ETH (Wrapped)
            "orcaEKTdK7LKz57vaAYr9QeNsVEPfiu6QeMU1kektZE",  # ORCA
            "4k3Dyjzvzp8eMZWUXbBCjEvwSkkk59S5iCNLY3QrkX6R",  # RAY
            "SRMuApVNdxXokk5GT7XD5cUUgXMBCoAz2LHeuAoKWRt",  # SRM
            "MangoCzJ36AjZyKwVj3VnYU4GTonjfVEnJmvvWaxLac",  # MNGO
            "5oVNBeEEQvYi1cX3ir8Dx5n1P7pdxydbGF2X4TxVusJm",  # SCNSOL
            "Saber2gLauYim4Mvftnrasomsv6NvAuncvMEZwcLpD1",  # SBR
            "4dmKkXNHdgYsXqBHCuMikNQWwVomZURhYvkkX5c4pQ7y",  # SNY
            "StepAscQoEioFxxWGnh2sLBDFp9d8rvKz2Yp39iDpyT",  # STEP
        ]
        log.info(f"Menggunakan {len(fallback_tokens)} token fallback")
        return fallback_tokens

    async def get_token_info(self, token_mint: str) -> Optional[Dict]:
        """Get information about a token."""
        if token_mint in token_cache:
            return token_cache[token_mint]

        result = await self._rate_limited_request("get", f"{config.JUPITER_TOKEN_INFO_ENDPOINT}/{token_mint}")
        if result:
            token_cache[token_mint] = result
            return result
        return None

    async def get_token_price(self, token_mint: str) -> Optional[Decimal]:
        """Get the price of a token in USDC."""
        try:
            if token_mint in price_cache:
                return price_cache[token_mint]

            result = await self._rate_limited_request("get", f"{config.JUPITER_PRICE_ENDPOINT}?ids={token_mint}")
            if result and "data" in result and token_mint in result["data"]:
                price_str = result["data"][token_mint]["price"]
                price = Decimal(str(price_str))
                price_cache[token_mint] = price
                return price

            # Use default prices for common tokens if API fails
            if token_mint == "So11111111111111111111111111111111111111112":  # SOL
                return Decimal("150.0")  # Approximate SOL price in USD
            elif token_mint == "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v":  # USDC
                return Decimal("1.0")
            elif token_mint == "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB":  # USDT
                return Decimal("1.0")

            return Decimal("1.0")  # Default fallback
        except Exception as e:
            log.error(f"Error getting token price for {token_mint}: {str(e)}")

            # Use default prices for common tokens if API fails
            if token_mint == "So11111111111111111111111111111111111111112":  # SOL
                return Decimal("150.0")  # Approximate SOL price in USD
            elif token_mint == "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v":  # USDC
                return Decimal("1.0")
            elif token_mint == "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB":  # USDT
                return Decimal("1.0")

            return Decimal("1.0")  # Default fallback

    async def estimate_transaction_fee(self) -> Decimal:
        """Estimate the transaction fee for a swap."""
        # Simplified estimation - in a real implementation, you would
        # use get_fee_for_message with the actual transaction message
        # Typical Solana transaction fee is around 5000 lamports
        return Decimal("0.000005")  # 5000 lamports in SOL

class ArbitrageDetector:
    """Detector for arbitrage opportunities on Jupiter."""

    def __init__(self, jupiter_client: JupiterClient):
        """Initialize the arbitrage detector."""
        self.jupiter_client = jupiter_client
        self.opportunities = []
        self.checking_tokens = set()
        self.base_tokens = config.BASE_TOKENS

    async def get_token_symbol(self, token_mint: str) -> str:
        """Get the symbol for a token."""
        token_info = await self.jupiter_client.get_token_info(token_mint)
        if token_info and "symbol" in token_info:
            return token_info["symbol"]
        return token_mint[:8] + "..."

    async def detect_direct_arbitrage(self, token_a: str, token_b: str) -> Optional[Dict]:
        """
        Detect direct arbitrage opportunity (A -> B -> A).

        Args:
            token_a: The first token mint address
            token_b: The second token mint address

        Returns:
            Opportunity details if profitable, None otherwise
        """
        # Skip if tokens are the same
        if token_a == token_b:
            return None

        # Skip if we're already checking this pair
        pair_key = f"{token_a}_{token_b}"
        if pair_key in self.checking_tokens:
            return None

        self.checking_tokens.add(pair_key)

        try:
            # Get amount in lamports/smallest unit
            amount = config.QUOTE_AMOUNT

            # Get token symbols for logging
            token_a_symbol = await self.get_token_symbol(token_a)
            token_b_symbol = await self.get_token_symbol(token_b)

            log.info(f"Checking direct arbitrage: {token_a_symbol} -> {token_b_symbol} -> {token_a_symbol}")

            # Step 1: A -> B
            quote_a_to_b = await self.jupiter_client.get_quote(token_a, token_b, amount)
            if not quote_a_to_b or "outAmount" not in quote_a_to_b:
                log.info(f"No route found for {token_a_symbol} -> {token_b_symbol}")
                return None

            out_amount_a_to_b = int(quote_a_to_b["outAmount"])
            log.info(f"Step 1: {token_a_symbol} -> {token_b_symbol}, In: {amount}, Out: {out_amount_a_to_b}")

            # Step 2: B -> A
            quote_b_to_a = await self.jupiter_client.get_quote(token_b, token_a, out_amount_a_to_b)
            if not quote_b_to_a or "outAmount" not in quote_b_to_a:
                log.info(f"No route found for {token_b_symbol} -> {token_a_symbol}")
                return None

            out_amount_b_to_a = int(quote_b_to_a["outAmount"])
            log.info(f"Step 2: {token_b_symbol} -> {token_a_symbol}, In: {out_amount_a_to_b}, Out: {out_amount_b_to_a}")

            # Calculate profit
            profit_amount = out_amount_b_to_a - amount
            profit_percentage = (Decimal(profit_amount) / Decimal(amount)) * Decimal(100)
            log.info(f"Raw profit: {profit_amount} ({profit_percentage:.2f}%)")

            # Estimate transaction fees (2 swaps)
            fee_estimate = Decimal("0.00001")  # Estimate 0.00001 SOL per transaction (10,000 lamports)
            fee_estimate = fee_estimate * Decimal(2)  # Two transactions

            # Apply slippage cost (5%)
            slippage_cost = Decimal(amount) * Decimal("0.05")  # 5% of initial amount

            # Subtract fees and slippage from profit
            profit_amount -= int(fee_estimate * Decimal(10 ** 9))  # Convert SOL to lamports
            profit_amount -= int(slippage_cost)  # Apply slippage cost

            # Recalculate profit percentage
            profit_percentage = (Decimal(profit_amount) / Decimal(amount)) * Decimal(100)
            log.info(f"After fees and slippage: {profit_amount} ({profit_percentage:.2f}%)")

            # Check if profitable
            if profit_percentage > config.MIN_PROFIT_THRESHOLD:
                # Calculate USD values
                token_a_price_usd = await self.jupiter_client.get_token_price(token_a)
                initial_amount_usd = Decimal(amount) * token_a_price_usd / Decimal(10 ** 9) if token_a_price_usd else Decimal(0)
                final_amount_usd = Decimal(out_amount_b_to_a) * token_a_price_usd / Decimal(10 ** 9) if token_a_price_usd else Decimal(0)
                profit_usd = final_amount_usd - initial_amount_usd

                log.info(f"PROFITABLE OPPORTUNITY FOUND: {token_a_symbol} -> {token_b_symbol} -> {token_a_symbol}, Profit: {profit_percentage:.2f}%, ${profit_usd:.4f}")

                return {
                    "type": "direct",
                    "path": f"{token_a_symbol} -> {token_b_symbol} -> {token_a_symbol}",
                    "tokens": [token_a, token_b, token_a],
                    "token_symbols": [token_a_symbol, token_b_symbol, token_a_symbol],
                    "initial_amount": amount,
                    "final_amount": out_amount_b_to_a,
                    "profit_amount": profit_amount,
                    "profit_percentage": profit_percentage,
                    "initial_amount_usd": initial_amount_usd,
                    "final_amount_usd": final_amount_usd,
                    "profit_usd": profit_usd,
                    "timestamp": time.time()
                }
            else:
                log.info(f"Not profitable: {profit_percentage:.2f}% < {config.MIN_PROFIT_THRESHOLD}%")

            return None
        except Exception as e:
            log.error(f"Error in detect_direct_arbitrage: {str(e)}")
            return None
        finally:
            self.checking_tokens.remove(pair_key)

    async def detect_triangular_arbitrage(self, token_a: str, token_b: str, token_c: str) -> Optional[Dict]:
        """
        Detect triangular arbitrage opportunity (A -> B -> C -> A).

        Args:
            token_a: The first token mint address
            token_b: The second token mint address
            token_c: The third token mint address

        Returns:
            Opportunity details if profitable, None otherwise
        """
        # Skip if tokens are the same
        if token_a == token_b or token_b == token_c or token_a == token_c:
            return None

        # Skip if we're already checking this triplet
        triplet_key = f"{token_a}_{token_b}_{token_c}"
        if triplet_key in self.checking_tokens:
            return None

        # Get token symbols for logging
        token_a_symbol = await self.get_token_symbol(token_a)
        token_b_symbol = await self.get_token_symbol(token_b)
        token_c_symbol = await self.get_token_symbol(token_c)

        # Skip stablecoin triangular arbitrage as they're usually not profitable
        stablecoins = ['usdc', 'usdt', 'dai', 'busd', 'tusd', 'usdh']
        if (token_a_symbol.lower() in stablecoins and
            token_b_symbol.lower() in stablecoins and
            token_c_symbol.lower() in stablecoins):
            log.info(f"Skipping stablecoin triangle: {token_a_symbol}-{token_b_symbol}-{token_c_symbol}")
            return None

        self.checking_tokens.add(triplet_key)

        try:
            # Get amount in lamports/smallest unit
            amount = config.QUOTE_AMOUNT

            log.info(f"Checking triangular arbitrage: {token_a_symbol} -> {token_b_symbol} -> {token_c_symbol} -> {token_a_symbol}")

            # Step 1: A -> B
            quote_a_to_b = await self.jupiter_client.get_quote(token_a, token_b, amount)
            if not quote_a_to_b or "outAmount" not in quote_a_to_b:
                log.info(f"No route found for {token_a_symbol} -> {token_b_symbol}")
                return None

            out_amount_a_to_b = int(quote_a_to_b["outAmount"])
            log.info(f"Step 1: {token_a_symbol} -> {token_b_symbol}, In: {amount}, Out: {out_amount_a_to_b}")

            # Step 2: B -> C
            quote_b_to_c = await self.jupiter_client.get_quote(token_b, token_c, out_amount_a_to_b)
            if not quote_b_to_c or "outAmount" not in quote_b_to_c:
                log.info(f"No route found for {token_b_symbol} -> {token_c_symbol}")
                return None

            out_amount_b_to_c = int(quote_b_to_c["outAmount"])
            log.info(f"Step 2: {token_b_symbol} -> {token_c_symbol}, In: {out_amount_a_to_b}, Out: {out_amount_b_to_c}")

            # Step 3: C -> A
            quote_c_to_a = await self.jupiter_client.get_quote(token_c, token_a, out_amount_b_to_c)
            if not quote_c_to_a or "outAmount" not in quote_c_to_a:
                log.info(f"No route found for {token_c_symbol} -> {token_a_symbol}")
                return None

            out_amount_c_to_a = int(quote_c_to_a["outAmount"])
            log.info(f"Step 3: {token_c_symbol} -> {token_a_symbol}, In: {out_amount_b_to_c}, Out: {out_amount_c_to_a}")

            # Calculate profit
            profit_amount = out_amount_c_to_a - amount
            profit_percentage = (Decimal(profit_amount) / Decimal(amount)) * Decimal(100)
            log.info(f"Raw profit: {profit_amount} ({profit_percentage:.2f}%)")

            # Estimate transaction fees (3 swaps)
            fee_estimate = Decimal("0.00001")  # Estimate 0.00001 SOL per transaction (10,000 lamports)
            fee_estimate = fee_estimate * Decimal(3)  # Three transactions

            # Apply slippage cost (5%)
            slippage_cost = Decimal(amount) * Decimal("0.05")  # 5% of initial amount

            # Subtract fees and slippage from profit
            profit_amount -= int(fee_estimate * Decimal(10 ** 9))  # Convert SOL to lamports
            profit_amount -= int(slippage_cost)  # Apply slippage cost

            # Recalculate profit percentage
            profit_percentage = (Decimal(profit_amount) / Decimal(amount)) * Decimal(100)
            log.info(f"After fees and slippage: {profit_amount} ({profit_percentage:.2f}%)")

            # Check if profitable
            if profit_percentage > config.MIN_PROFIT_THRESHOLD:
                # Calculate USD values
                token_a_price_usd = await self.jupiter_client.get_token_price(token_a)
                initial_amount_usd = Decimal(amount) * token_a_price_usd / Decimal(10 ** 9) if token_a_price_usd else Decimal(0)
                final_amount_usd = Decimal(out_amount_c_to_a) * token_a_price_usd / Decimal(10 ** 9) if token_a_price_usd else Decimal(0)
                profit_usd = final_amount_usd - initial_amount_usd

                log.info(f"PROFITABLE OPPORTUNITY FOUND: {token_a_symbol} -> {token_b_symbol} -> {token_c_symbol} -> {token_a_symbol}, Profit: {profit_percentage:.2f}%, ${profit_usd:.4f}")

                return {
                    "type": "triangular",
                    "path": f"{token_a_symbol} -> {token_b_symbol} -> {token_c_symbol} -> {token_a_symbol}",
                    "tokens": [token_a, token_b, token_c, token_a],
                    "token_symbols": [token_a_symbol, token_b_symbol, token_c_symbol, token_a_symbol],
                    "initial_amount": amount,
                    "final_amount": out_amount_c_to_a,
                    "profit_amount": profit_amount,
                    "profit_percentage": profit_percentage,
                    "initial_amount_usd": initial_amount_usd,
                    "final_amount_usd": final_amount_usd,
                    "profit_usd": profit_usd,
                    "timestamp": time.time()
                }
            else:
                log.info(f"Not profitable: {profit_percentage:.2f}% < {config.MIN_PROFIT_THRESHOLD}%")

            return None
        except Exception as e:
            log.error(f"Error in detect_triangular_arbitrage: {str(e)}")
            return None
        finally:
            self.checking_tokens.remove(triplet_key)

    async def find_opportunities(self):
        """Find arbitrage opportunities."""
        # Get tradable tokens
        tradable_tokens = await self.jupiter_client.get_tradable_tokens()

        # Prioritize tokens with high liquidity
        high_liquidity_tokens = [
            "So11111111111111111111111111111111111111112",  # SOL
            "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",  # USDC
            "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB",  # USDT
            "7dHbWXmci3dT8UFYWYZweBLXgycu7Y3iL6trKn1Y7ARj",  # stSOL
            "mSoLzYCxHdYgdzU16g5QSh3i5K3z3KZK7ytfqcJm7So",  # mSOL
            "7vfCXTUXx5WJV5JADk17DUJ4ksgau7utNKj4b963voxs",  # ETH (Wormhole)
            "DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263",  # BONK
            "JUPyiwrYJFskUPiHa7hkeR8VUtAeFoSYbKedZNsDvCN",  # JUP
        ]

        # Limit the number of tokens to check
        tokens_to_check = []

        # First add high liquidity tokens
        for token in high_liquidity_tokens:
            if token in tradable_tokens:
                tokens_to_check.append(token)

        # Then add other tokens up to the limit
        for token in tradable_tokens:
            if token not in tokens_to_check and len(tokens_to_check) < config.MAX_TOKENS_TO_CHECK:
                tokens_to_check.append(token)

        log.info(f"Checking {len(tokens_to_check)} tokens for arbitrage opportunities")

        # Focus on direct arbitrage first as it's more likely to find valid opportunities
        opportunities = []

        # Create tasks for direct arbitrage with SOL and USDC as base tokens
        # These are the most liquid tokens and most likely to have arbitrage opportunities
        direct_tasks = []
        priority_base_tokens = [
            "So11111111111111111111111111111111111111112",  # SOL
            "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",  # USDC
            "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB",  # USDT
        ]

        # First check direct arbitrage with priority base tokens
        for base_token in priority_base_tokens:
            if base_token in tokens_to_check:
                for token in tokens_to_check[:20]:  # Check top 20 tokens
                    if token != base_token:
                        direct_tasks.append(self.detect_direct_arbitrage(base_token, token))

        # Process priority direct arbitrage tasks
        if direct_tasks:
            log.info(f"Checking {len(direct_tasks)} priority direct arbitrage opportunities")
            batch_size = min(config.MAX_CONCURRENT_REQUESTS, 3)  # Limit concurrent requests to avoid rate limiting

            for i in range(0, len(direct_tasks), batch_size):
                batch = direct_tasks[i:i+batch_size]
                batch_results = await asyncio.gather(*batch, return_exceptions=True)

                for result in batch_results:
                    if isinstance(result, Exception):
                        log.error(f"Error detecting opportunity: {str(result)}")
                    elif result:
                        opportunities.append(result)
                        log.info(f"Found profitable opportunity: {result['path']} with {result['profit_percentage']:.4f}% profit")

                # If we found opportunities, no need to check more
                if opportunities:
                    break

                # Add a longer delay to avoid rate limiting
                await asyncio.sleep(2.0)

        # If no opportunities found with priority tokens, try other base tokens
        if not opportunities:
            direct_tasks = []
            for base_token in self.base_tokens:
                if base_token not in priority_base_tokens and base_token in tokens_to_check:
                    for token in tokens_to_check[:10]:  # Check top 10 tokens
                        if token != base_token:
                            direct_tasks.append(self.detect_direct_arbitrage(base_token, token))

            # Process other direct arbitrage tasks
            if direct_tasks:
                log.info(f"Checking {len(direct_tasks)} additional direct arbitrage opportunities")
                batch_size = min(config.MAX_CONCURRENT_REQUESTS, 3)  # Limit concurrent requests

                for i in range(0, len(direct_tasks), batch_size):
                    batch = direct_tasks[i:i+batch_size]
                    batch_results = await asyncio.gather(*batch, return_exceptions=True)

                    for result in batch_results:
                        if isinstance(result, Exception):
                            log.error(f"Error detecting opportunity: {str(result)}")
                        elif result:
                            opportunities.append(result)
                            log.info(f"Found profitable opportunity: {result['path']} with {result['profit_percentage']:.4f}% profit")

                    # If we found opportunities, no need to check more
                    if opportunities:
                        break

                    # Add a longer delay to avoid rate limiting
                    await asyncio.sleep(2.0)

        # If still no opportunities, try triangular arbitrage with SOL as base token
        if not opportunities:
            triangular_tasks = []
            sol_token = "So11111111111111111111111111111111111111112"  # SOL

            if sol_token in tokens_to_check:
                # Use fewer tokens for triangular arbitrage to avoid too many API requests
                for token_b in tokens_to_check[:5]:  # Only check first 5 tokens
                    if token_b == sol_token:
                        continue
                    for token_c in tokens_to_check[:5]:  # Only check first 5 tokens
                        if token_c == sol_token or token_c == token_b:
                            continue
                        triangular_tasks.append(self.detect_triangular_arbitrage(sol_token, token_b, token_c))

            # Process triangular arbitrage tasks
            if triangular_tasks:
                log.info(f"Checking {len(triangular_tasks)} triangular arbitrage opportunities")
                batch_size = min(config.MAX_CONCURRENT_REQUESTS, 2)  # Limit concurrent requests even more for triangular

                for i in range(0, len(triangular_tasks), batch_size):
                    batch = triangular_tasks[i:i+batch_size]
                    batch_results = await asyncio.gather(*batch, return_exceptions=True)

                    for result in batch_results:
                        if isinstance(result, Exception):
                            log.error(f"Error detecting opportunity: {str(result)}")
                        elif result:
                            opportunities.append(result)
                            log.info(f"Found profitable opportunity: {result['path']} with {result['profit_percentage']:.4f}% profit")

                    # If we found opportunities, no need to check more
                    if opportunities:
                        break

                    # Add a longer delay to avoid rate limiting
                    await asyncio.sleep(3.0)

        # Sort opportunities by profit percentage (descending)
        opportunities.sort(key=lambda x: x["profit_percentage"], reverse=True)

        # Update opportunities
        self.opportunities = opportunities[:config.MAX_OPPORTUNITIES_DISPLAYED]

class UI:
    """UI for displaying arbitrage opportunities."""

    def __init__(self, detector: ArbitrageDetector):
        """Initialize the UI."""
        self.detector = detector
        self.layout = self._create_layout()
        self.start_time = time.time()
        self.last_update_time = time.time()
        self.scan_count = 0

    def _create_layout(self) -> Layout:
        """Create the layout for the UI."""
        layout = Layout(name="root")

        # Split the screen into header, body, and footer
        layout.split(
            Layout(name="header", size=3),
            Layout(name="body", ratio=8),
            Layout(name="footer", size=3)
        )

        # Split the body into main and info
        layout["body"].split_row(
            Layout(name="main", ratio=3),
            Layout(name="info", ratio=1)
        )

        return layout

    def _generate_header(self) -> Panel:
        """Generate the header panel."""
        title = Text("✨ JUPITER OPPORTUNITY HUNTER ✨", style="bold bright_cyan", justify="center")
        subtitle = Text("Real-time Arbitrage Detection for Solana", style="italic bright_magenta", justify="center")
        version = Text("v1.1.0", style="dim", justify="center")

        header_text = Text()
        header_text.append(title)
        header_text.append("\n")
        header_text.append(subtitle)
        header_text.append("\n")
        header_text.append(version)

        # Add a gradient border
        return Panel(
            header_text,
            style="on black",
            border_style="bright_blue",
            box=box.DOUBLE,
            title="[bold bright_yellow]SOLANA ARBITRAGE[/bold bright_yellow]",
            title_align="center"
        )

    def _generate_opportunities_table(self) -> Table:
        """Generate the table of arbitrage opportunities."""
        table = Table(
            title="🔍 [bold bright_yellow]PELUANG ARBITRASE TERDETEKSI[/bold bright_yellow]",
            box=box.DOUBLE,
            caption="[dim italic]Peluang arbitrase diperbarui secara real-time[/dim italic]",
            caption_justify="center",
            caption_style="bright_blue",
            border_style="bright_blue",
            header_style="bold bright_magenta",
            expand=True,
            highlight=True,
            show_lines=True
        )

        # Add columns
        table.add_column("No", justify="right", style="cyan", no_wrap=True, width=4)
        table.add_column("Jalur Arbitrase", style="green", width=30)
        table.add_column("Tipe", justify="center", style="bright_cyan", width=10)
        table.add_column("Profit %", justify="right", style="bold magenta", width=10)
        table.add_column("Profit USD", justify="right", style="bold green", width=12)
        table.add_column("Modal", justify="right", style="bright_blue", width=10)
        table.add_column("Waktu", justify="right", style="dim", width=10)

        # Add rows
        for i, opportunity in enumerate(self.detector.opportunities, 1):
            # Format profit percentage
            profit_percentage = f"{opportunity['profit_percentage']:.4f}%"  # Show more decimal places

            # Format profit USD
            profit_usd = f"${opportunity['profit_usd']:.6f}"  # Show more decimal places

            # Format time
            elapsed = time.time() - opportunity["timestamp"]
            time_str = f"{elapsed:.1f}s lalu"

            # Format capital
            capital = f"{opportunity['initial_amount'] / 10**9:.4f} SOL"
            if "initial_amount_usd" in opportunity:
                capital += f"\n(${opportunity['initial_amount_usd']:.2f})"

            # Choose style based on profit percentage
            profit_style = "bold magenta"
            if opportunity['profit_percentage'] < Decimal("0.1"):
                profit_style = "dim magenta"
            elif opportunity['profit_percentage'] > Decimal("1.0"):
                profit_style = "bold bright_magenta"

            # Get opportunity type
            opp_type = opportunity.get("type", "direct")
            type_style = "bright_cyan"
            if opp_type == "triangular":
                type_style = "bright_yellow"

            table.add_row(
                str(i),
                opportunity["path"],
                Text(opp_type.upper(), style=type_style),
                Text(profit_percentage, style=profit_style),
                profit_usd,
                capital,
                time_str
            )

        if not self.detector.opportunities:
            table.add_row(
                "-",
                "[italic]Mencari peluang arbitrase... Mohon tunggu...[/italic]",
                "-",
                "-",
                "-",
                "-",
                "-"
            )

        return table

    def _generate_info_panel(self) -> Panel:
        """Generate the info panel with status and statistics."""
        info_text = Text()

        # Calculate runtime
        runtime = time.time() - self.start_time
        hours, remainder = divmod(runtime, 3600)
        minutes, seconds = divmod(remainder, 60)
        runtime_str = f"{int(hours):02}:{int(minutes):02}:{int(seconds):02}"

        # Add status section
        info_text.append("📡 [bold bright_cyan]STATUS[/bold bright_cyan]\n", style="bold")
        info_text.append("• Status: ", style="bright_white")
        info_text.append("✅ AKTIF", style="bold bright_green")
        info_text.append("\n• Waktu Berjalan: ", style="bright_white")
        info_text.append(f"{runtime_str}", style="bold bright_yellow")
        info_text.append("\n• Jumlah Scan: ", style="bright_white")
        info_text.append(f"{self.scan_count}", style="bold bright_yellow")
        info_text.append("\n• Peluang Ditemukan: ", style="bright_white")
        info_text.append(f"{len(self.detector.opportunities)}", style="bold bright_green")

        # Add last update time
        last_update = time.time() - self.last_update_time
        info_text.append("\n• Update Terakhir: ", style="bright_white")
        info_text.append(f"{last_update:.1f}s lalu", style="bold bright_yellow")

        info_text.append("\n\n")  # Add blank lines for separation

        # Add configuration section
        info_text.append("⚙️ [bold bright_cyan]KONFIGURASI[/bold bright_cyan]\n", style="bold")

        # Calculate USD value of capital
        sol_price = Decimal("150.0")  # Default SOL price
        try:
            sol_mint = "So11111111111111111111111111111111111111112"
            if sol_mint in price_cache:
                sol_price = price_cache[sol_mint]
        except:
            pass

        capital_sol = Decimal(config.QUOTE_AMOUNT) / Decimal(10**9)
        capital_usd = capital_sol * sol_price

        # Add min profit threshold
        info_text.append("• Min Profit: ", style="bright_white")
        info_text.append(f"{config.MIN_PROFIT_THRESHOLD}%", style="bold bright_green")

        # Add capital amount
        info_text.append("\n• Modal: ", style="bright_white")
        info_text.append(f"{capital_sol:.4f} SOL (${capital_usd:.2f})", style="bold bright_green")

        # Add slippage
        info_text.append("\n• Slippage: ", style="bright_white")
        info_text.append(f"{config.SLIPPAGE_BPS / 100}%", style="bold bright_green")

        # Add token count
        info_text.append("\n• Token Dipantau: ", style="bright_white")
        info_text.append(f"{config.MAX_TOKENS_TO_CHECK}", style="bold bright_green")

        info_text.append("\n\n")  # Add blank lines for separation

        # Add network section
        info_text.append("🌐 [bold bright_cyan]JARINGAN[/bold bright_cyan]\n", style="bold")

        # Add blockchain
        info_text.append("• Blockchain: ", style="bright_white")
        info_text.append("Solana", style="bold bright_magenta")

        # Add DEX
        info_text.append("\n• DEX Aggregator: ", style="bright_white")
        info_text.append("Jupiter", style="bold bright_magenta")

        # Add RPC status
        info_text.append("\n• RPC: ", style="bright_white")
        try:
            rpc_display = config.RPC_URL.split('//')[1][:20]
        except:
            rpc_display = config.RPC_URL[:20]
        info_text.append(f"{rpc_display}...", style="dim")

        # Add API status
        info_text.append("\n• Jupiter API: ", style="bright_white")
        info_text.append("Terhubung", style="bold bright_green")

        # Add refresh interval
        info_text.append("\n• Interval Refresh: ", style="bright_white")
        info_text.append(f"{config.REFRESH_INTERVAL}s", style="bold bright_yellow")

        return Panel(
            info_text,
            title="[bold bright_yellow]ℹ️ INFORMASI & STATISTIK[/bold bright_yellow]",
            title_align="center",
            border_style="bright_blue",
            box=box.DOUBLE,
            style="on black"
        )

    def _generate_footer(self) -> Panel:
        """Generate the footer panel."""
        footer_text = Text()
        footer_text.append("💡 ", style="bright_yellow")
        footer_text.append("Tekan ", style="bright_white")
        footer_text.append("Ctrl+C", style="bold bright_cyan")
        footer_text.append(" untuk keluar", style="bright_white")
        footer_text.append(" | ", style="dim")
        footer_text.append("Mencari peluang arbitrase dengan profit > ", style="bright_white")
        footer_text.append(f"{config.MIN_PROFIT_THRESHOLD}%", style="bold bright_magenta")
        footer_text.append(" | ", style="dim")
        footer_text.append("Dibuat dengan ", style="bright_white")
        footer_text.append("❤️", style="bright_red")
        footer_text.append(" oleh ", style="bright_white")
        footer_text.append("Jupiter Hunter Team", style="bold bright_green")

        return Panel(
            footer_text,
            style="on black",
            border_style="bright_blue",
            box=box.DOUBLE
        )

    def render(self) -> Layout:
        """Render the UI."""
        # Update the layout with the latest data
        self.layout["header"].update(self._generate_header())
        self.layout["main"].update(self._generate_opportunities_table())
        self.layout["info"].update(self._generate_info_panel())
        self.layout["footer"].update(self._generate_footer())

        return self.layout

    # Metode update_stats dihapus karena tidak lagi digunakan

async def main():
    """Main function."""
    # Create Jupiter client
    console.print("[bold cyan]Initializing Jupiter client...[/bold cyan]")
    jupiter_client = JupiterClient()
    await jupiter_client.initialize()
    console.print("[bold green]Jupiter client initialized successfully![/bold green]")

    try:
        # Create arbitrage detector
        console.print("[bold cyan]Creating arbitrage detector...[/bold cyan]")
        detector = ArbitrageDetector(jupiter_client)
        console.print("[bold green]Arbitrage detector created successfully![/bold green]")

        # Create UI
        console.print("[bold cyan]Creating UI...[/bold cyan]")
        ui = UI(detector)
        console.print("[bold green]UI created successfully![/bold green]")

        # Create live display
        console.print("[bold magenta]Starting live display...[/bold magenta]")
        console.print("[bold yellow]Mencari peluang arbitrase dengan profit > " +
                     f"{float(config.MIN_PROFIT_THRESHOLD) * 100:.4f}%[/bold yellow]")
        console.print("[bold yellow]Tekan Ctrl+C untuk keluar dari program[/bold yellow]")

        with Live(ui.render(), refresh_per_second=4, screen=False) as live:
            while True:
                # Increment scan count and update UI
                ui.scan_count += 1
                console.print(f"[dim]Scan #{ui.scan_count} dimulai...[/dim]", end="\r")

                # Find opportunities
                await detector.find_opportunities()

                # Update last update time
                ui.last_update_time = time.time()

                # Update live display
                live.update(ui.render())

                # Log if opportunities found
                if detector.opportunities:
                    console.print(f"[bold green]Ditemukan {len(detector.opportunities)} peluang arbitrase![/bold green]")
                    for i, opp in enumerate(detector.opportunities, 1):
                        console.print(f"[bold cyan]Peluang #{i}:[/bold cyan] {opp['path']} - " +
                                     f"Profit: [bold magenta]{opp['profit_percentage']:.4f}%[/bold magenta] " +
                                     f"(${opp['profit_usd']:.6f})")

                # Wait for refresh interval
                await asyncio.sleep(config.REFRESH_INTERVAL)
    except Exception as e:
        console.print(f"[bold red]Error dalam program: {str(e)}[/bold red]")
        logging.exception("Unhandled exception")
    finally:
        # Close client
        console.print("[bold cyan]Menutup Jupiter client...[/bold cyan]")
        await jupiter_client.close()
        console.print("[bold green]Jupiter client ditutup![/bold green]")

if __name__ == "__main__":
    try:
        # Run main function
        asyncio.run(main())
    except KeyboardInterrupt:
        console.print("\n[bold cyan]Program dihentikan oleh pengguna (Ctrl+C).[/bold cyan]")
        console.print("[bold green]Terima kasih telah menggunakan Jupiter Opportunity Hunter![/bold green]")
    except Exception as e:
        console.print(f"\n[bold red]Error: {str(e)}[/bold red]")
        logging.exception("Unhandled exception")
    finally:
        # Ensure clean exit
        console.print("[cyan]Keluar dari program...[/cyan]")
        # Force exit to terminate any hanging tasks
        os._exit(0)
