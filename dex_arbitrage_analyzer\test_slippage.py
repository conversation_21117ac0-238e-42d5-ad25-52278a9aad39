"""
Script pengujian untuk implementasi biaya slippage dalam USD.
"""

import sys
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.text import Text

from config import DEFAULT_SLIPPAGE_PERCENTAGE, IDR_TO_USD_RATE
from utils import format_usd, format_percentage, format_idr

# Setup console
console = Console()

def test_slippage_cost():
    """
    Menguji implementasi biaya slippage dalam USD.
    """
    console.print(Panel.fit(
        Text("Pengujian Biaya Slippage dalam USD", style="bold cyan"),
        border_style="cyan"
    ))
    
    # Tabel untuk hasil
    results_table = Table(title="Hasil Pengujian Biaya Slippage", show_header=True, header_style="bold cyan")
    results_table.add_column("Modal (USD)", style="cyan")
    results_table.add_column("Slippage (%)", style="green")
    results_table.add_column("Biaya Slippage (USD)", style="yellow")
    results_table.add_column("Biaya Slippage (IDR)", style="magenta")
    
    # Daftar modal untuk pengujian
    capitals = [10, 50, 90, 100, 500, 1000, 5000, 10000]
    
    # Uji biaya slippage untuk setiap modal
    for capital in capitals:
        # Hitung biaya slippage
        slippage_cost = capital * (DEFAULT_SLIPPAGE_PERCENTAGE / 100)
        slippage_cost_idr = slippage_cost * IDR_TO_USD_RATE
        
        # Tambahkan ke tabel
        results_table.add_row(
            format_usd(capital),
            format_percentage(DEFAULT_SLIPPAGE_PERCENTAGE),
            format_usd(slippage_cost),
            format_idr(slippage_cost_idr)
        )
    
    # Tampilkan hasil
    console.print(results_table)
    
    # Tampilkan kesimpulan
    console.print("\n[bold]Kesimpulan:[/bold]")
    console.print(f"1. Dengan slippage tetap {DEFAULT_SLIPPAGE_PERCENTAGE}%, biaya slippage adalah {DEFAULT_SLIPPAGE_PERCENTAGE}% dari modal.")
    console.print("2. Semakin besar modal, semakin besar biaya slippage dalam USD.")
    console.print("3. Biaya slippage harus diperhitungkan dalam analisis profitabilitas arbitrase.")
    console.print("4. Untuk modal $90 USD, biaya slippage adalah $4.5 USD atau sekitar Rp 76,050.")

if __name__ == "__main__":
    test_slippage_cost()
