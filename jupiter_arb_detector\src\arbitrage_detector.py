"""
Modul untuk mendeteksi peluang arbitrase di Jupiter.
"""
import asyncio
import logging
import time
from typing import Dict, Any, List, Optional, Tuple

from .jupiter_api import JupiterAPI
from .utils import display_opportunity

logger = logging.getLogger("jupiter_arb_detector")

class ArbitrageDetector:
    """
    Kelas untuk mendeteksi peluang arbitrase di Jupiter.
    """

    def __init__(self, jupiter_api: JupiterAPI, settings: Dict[str, Any]):
        """
        Inisialisasi objek ArbitrageDetector.

        Args:
            jupiter_api: Objek JupiterAPI
            settings: Pengaturan dari konfigurasi
        """
        self.jupiter_api = jupiter_api
        self.settings = settings

        self.base_token = settings.get('base_token', 'So11111111111111111111111111111111111111112')  # SOL
        self.usdc_token = settings.get('usdc_token', 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v')  # USDC

        self.base_token_amount = settings.get('base_token_amount', 1.0)  # Jumlah SOL untuk simulasi swap
        self.slippage_percentage = settings.get('slippage_percentage', 1.0)  # Persentase slippage
        self.min_profit_percentage = settings.get('min_profit_percentage', 1.0)  # Persentase profit minimum

        # Konversi slippage dari persentase ke basis poin
        self.slippage_bps = int(self.slippage_percentage * 100)  # 1% = 100 bps

        # Filter untuk token
        self.exclude_tags = settings.get('exclude_tags', ["meme", "scam"])
        self.min_daily_volume = settings.get('min_daily_volume', 1000)  # Volume harian minimum dalam USD

        logger.info(f"ArbitrageDetector diinisialisasi dengan base token: {self.base_token}")

    async def detect_opportunities(self) -> List[Dict[str, Any]]:
        """
        Mendeteksi peluang arbitrase di Jupiter.

        Returns:
            Daftar peluang arbitrase
        """
        logger.info("Memulai deteksi peluang arbitrase")

        try:
            # Dapatkan daftar token yang dapat diperdagangkan
            tokens = await self.jupiter_api.get_tradable_tokens()

            if not tokens:
                logger.warning("Tidak ada token yang dapat diperdagangkan")
                return []

            # Filter token berdasarkan tag dan volume
            filtered_tokens = []
            for token in tokens:
                try:
                    # Lewati token yang memiliki tag yang diexclude
                    if "tags" in token and any(tag in token["tags"] for tag in self.exclude_tags):
                        continue

                    # Lewati token dengan volume rendah
                    if "daily_volume" in token and token["daily_volume"] < self.min_daily_volume:
                        continue

                    # Lewati token yang tidak memiliki simbol atau desimal
                    if "symbol" not in token or "decimals" not in token:
                        continue

                    # Lewati token yang sama dengan base token
                    if token["address"] == self.base_token:
                        continue

                    filtered_tokens.append(token)
                except Exception as e:
                    logger.warning(f"Error saat memfilter token: {e}")

            logger.info(f"Menganalisis {len(filtered_tokens)} token setelah filter")

            if not filtered_tokens:
                logger.warning("Tidak ada token yang tersisa setelah filter")
                return []

            try:
                # Dapatkan harga base token dalam USD
                base_token_price_usd = await self.jupiter_api.get_token_price(self.base_token)
                logger.info(f"Harga SOL: ${base_token_price_usd}")

                # Hitung nilai base token dalam USD
                base_token_value_usd = self.base_token_amount * base_token_price_usd
                logger.info(f"Nilai {self.base_token_amount} SOL: ${base_token_value_usd:.2f}")

                # Dapatkan informasi base token
                base_token_info = await self.jupiter_api.get_token_info(self.base_token)
                base_token_decimals = base_token_info["decimals"]

                # Konversi jumlah base token ke satuan terkecil (lamports)
                base_token_amount_lamports = int(self.base_token_amount * (10 ** base_token_decimals))
            except Exception as e:
                logger.error(f"Error saat mendapatkan informasi base token: {e}")
                return []

            # Daftar peluang arbitrase
            opportunities = []

            # Batasi jumlah token yang dianalisis secara bersamaan
            batch_size = 5  # Kurangi ukuran batch untuk menghindari rate limiting
            for i in range(0, len(filtered_tokens), batch_size):
                batch = filtered_tokens[i:i+batch_size]

                # Buat tugas untuk setiap token
                tasks = []
                for token in batch:
                    task = self._check_token_opportunity(
                        token,
                        base_token_amount_lamports,
                        base_token_value_usd
                    )
                    tasks.append(task)

                # Jalankan tugas secara bersamaan
                results = await asyncio.gather(*tasks, return_exceptions=True)

                # Proses hasil
                for result in results:
                    if isinstance(result, Exception):
                        logger.warning(f"Error saat memeriksa peluang: {result}")
                    elif result:
                        opportunities.append(result)

                # Jeda sebentar untuk menghindari rate limiting
                await asyncio.sleep(2)  # Tambah jeda untuk menghindari rate limiting

            # Urutkan peluang berdasarkan profit persentase
            opportunities.sort(key=lambda x: x["profit_percentage"], reverse=True)

            logger.info(f"Ditemukan {len(opportunities)} peluang arbitrase")
            return opportunities
        except Exception as e:
            logger.error(f"Error saat mendeteksi peluang arbitrase: {e}")
            return []

    async def _check_token_opportunity(
        self,
        token: Dict[str, Any],
        base_token_amount_lamports: int,
        base_token_value_usd: float
    ) -> Optional[Dict[str, Any]]:
        """
        Memeriksa peluang arbitrase untuk token tertentu.

        Args:
            token: Informasi token
            base_token_amount_lamports: Jumlah base token dalam satuan terkecil
            base_token_value_usd: Nilai base token dalam USD

        Returns:
            Peluang arbitrase jika ditemukan, None jika tidak
        """
        token_address = token["address"]
        token_symbol = token["symbol"]

        try:
            # Dapatkan harga token dalam USD
            token_price_usd = await self.jupiter_api.get_token_price(token_address)

            # Validasi peluang swap
            validation_result = await self.jupiter_api.validate_swap_opportunity(
                self.base_token,
                token_address,
                base_token_amount_lamports,
                await self.jupiter_api.get_token_price(self.base_token),
                token_price_usd
            )

            # Jika peluang valid dan profit di atas minimum
            if validation_result["valid"] and validation_result["net_profit_percentage"] > self.min_profit_percentage:
                logger.info(
                    f"Peluang swap ditemukan: SOL -> {token_symbol} "
                    f"dengan profit bersih {validation_result['net_profit_percentage']:.2f}% (${validation_result['net_profit_usd']:.2f})"
                )

                # Tampilkan peluang dengan informasi lengkap
                display_opportunity(
                    token_symbol,
                    token_address,
                    self.base_token_amount,
                    validation_result["input_value_usd"],
                    validation_result["output_value_usd"],
                    validation_result["net_profit_usd"],
                    validation_result["net_profit_percentage"],
                    validation_result["fee_usd"],
                    validation_result["gas_usd"],
                    validation_result["total_cost_usd"]
                )

                # Tambahkan informasi tambahan
                opportunity = {
                    "token_symbol": token_symbol,
                    "token_address": token_address,
                    "input_amount": self.base_token_amount,
                    "input_value_usd": validation_result["input_value_usd"],
                    "output_value_usd": validation_result["output_value_usd"],
                    "profit_usd": validation_result["profit_usd"],
                    "profit_percentage": validation_result["profit_percentage"],
                    "fee_usd": validation_result["fee_usd"],
                    "gas_usd": validation_result["gas_usd"],
                    "total_cost_usd": validation_result["total_cost_usd"],
                    "net_profit_usd": validation_result["net_profit_usd"],
                    "net_profit_percentage": validation_result["net_profit_percentage"],
                    "quote": validation_result["quote"]
                }

                return opportunity

            return None
        except Exception as e:
            logger.debug(f"Error saat memeriksa peluang untuk {token_symbol} ({token_address}): {e}")
            return None
