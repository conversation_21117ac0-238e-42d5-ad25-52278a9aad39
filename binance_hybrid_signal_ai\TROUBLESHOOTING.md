# 🔧 Troubleshooting Guide - Binance Hybrid Signal AI

## 🚫 Masalah Geo-blocking (Error 451)

### **Gejala:**
```
NetworkError: ExchangeNotAvailable - binanceusdm GET https://fapi.binance.com/fapi/v1/exchangeInfo?reload=true 451
"Service unavailable from a restricted location according to 'b. Eligibility'"
```

### **Penyebab:**
- Lokasi geografis Anda dibatasi oleh Binance
- IP address terdeteksi dari negara yang restricted
- ISP blocking akses ke Binance

### **Solusi:**

#### **1. 🌐 Gunakan VPN (Rekomendasi Utama)**
```bash
# VPN Terbaik untuk Trading:
• ExpressVPN (Fastest, most reliable)
• NordVPN (Good for Indonesia)
• Surfshark (Budget-friendly)
• ProtonVPN (Privacy-focused)

# Server yang Direkomendasikan:
• Singapura (Latency rendah)
• Hong Kong (Stable connection)
• Jepang (Fast speed)
• Jerman/Belanda (EU servers)
```

#### **2. 🔄 Exchange Alternatif (Otomatis)**
Program akan otomatis mencoba:
- **Bybit** (Tersedia di Indonesia)
- **OKX** (Global access)
- **Binance Spot** (Fallback)

#### **3. 📧 Hubungi Customer Service**
```
Email: <EMAIL>
Subject: API Access Issue - Error 451

Template:
"Hi Binance Support,
I'm getting error 451 when accessing your API for trading analysis.
My location: [Your Country]
Error: Service unavailable from restricted location
Please help resolve this issue.
Thanks!"
```

#### **4. 🛠️ Technical Workarounds**
```python
# Edit config.py untuk menggunakan proxy:
PROXY_CONFIG = {
    'enabled': True,
    'http': 'http://your-proxy:port',
    'https': 'https://your-proxy:port'
}
```

---

## 🌐 Masalah Koneksi Network

### **Gejala:**
- Timeout errors
- Connection refused
- DNS resolution failed

### **Solusi:**

#### **1. Cek Koneksi Internet**
```bash
# Test koneksi:
ping google.com
ping binance.com

# Test DNS:
nslookup binance.com
```

#### **2. Firewall/Antivirus**
- Disable firewall sementara
- Whitelist Python.exe
- Disable antivirus real-time protection

#### **3. Proxy/Corporate Network**
```python
# Jika di belakang corporate proxy:
PROXY_CONFIG = {
    'enabled': True,
    'http': 'http://proxy.company.com:8080',
    'https': 'https://proxy.company.com:8080',
    'auth': ('username', 'password')
}
```

---

## 📦 Masalah Dependencies

### **Gejala:**
```
ImportError: No module named 'ccxt'
ImportError: No module named 'PySide6'
```

### **Solusi:**

#### **1. Install Dependencies**
```bash
# Method 1: Automatic
python run.py --install

# Method 2: Manual
pip install -r requirements.txt

# Method 3: Individual
pip install ccxt pandas numpy ta pandas-ta PySide6
```

#### **2. Virtual Environment (Recommended)**
```bash
# Create virtual environment
python -m venv venv

# Activate (Windows)
venv\Scripts\activate

# Activate (Linux/Mac)
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt
```

#### **3. Python Version Issues**
```bash
# Check Python version
python --version

# Required: Python 3.8+
# If older, download from: https://www.python.org/downloads/
```

---

## 🖥️ Masalah GUI

### **Gejala:**
- GUI tidak muncul
- Blank window
- Crash saat startup

### **Solusi:**

#### **1. PySide6 Issues**
```bash
# Reinstall PySide6
pip uninstall PySide6
pip install PySide6

# Alternative: PyQt5
pip install PyQt5
```

#### **2. Display Issues**
```bash
# Windows: Run as Administrator
# Linux: Install X11 dependencies
sudo apt-get install python3-tk

# Mac: Install XQuartz
brew install --cask xquartz
```

#### **3. Console Mode**
```python
# Edit binance3timeframe (1).py
# Comment out GUI parts, run console only
```

---

## 📊 Masalah Data/Analysis

### **Gejala:**
- No signals found
- Empty data
- Analysis errors

### **Solusi:**

#### **1. Market Hours**
```
Binance Futures: 24/7
Bybit: 24/7
OKX: 24/7

# Check if markets are open
# Weekend might have lower volume
```

#### **2. Pair Availability**
```python
# Check if pairs exist on exchange
# Some pairs might be delisted
# Use popular pairs: BTCUSDT, ETHUSDT, etc.
```

#### **3. Data Quality**
```python
# Increase candle limit
CANDLE_LIMIT_PER_TIMEFRAME = 500

# Use longer timeframes
TIMEFRAMES_TO_ANALYZE = ['4h', '1h', '30m']
```

---

## 🔍 Debug Mode

### **Enable Detailed Logging:**
```python
# Edit config.py
LOGGING_CONFIG = {
    'level': 'DEBUG',  # Change from INFO to DEBUG
    'console_output': True,
    'file_output': True
}
```

### **Demo Mode untuk Testing:**
```python
# Edit binance3timeframe (1).py
DEMO_MODE = True  # Change to True

# Test tanpa koneksi exchange
python test_logging.py
```

---

## 📞 Bantuan Lebih Lanjut

### **1. Check Log Files**
```
binance_signals.log - Main log
test_binance_signals.log - Test log
```

### **2. System Information**
```bash
# Collect system info:
python --version
pip list
ipconfig /all  # Windows
ifconfig       # Linux/Mac
```

### **3. Error Reporting**
Jika masih bermasalah, kirim informasi berikut:
- Error message lengkap
- Log file
- System information
- Steps to reproduce

### **4. Community Support**
- GitHub Issues
- Trading communities
- Python forums

---

## ✅ Quick Fixes Checklist

- [ ] ✅ Python 3.8+ installed
- [ ] ✅ Dependencies installed (`pip install -r requirements.txt`)
- [ ] ✅ Internet connection working
- [ ] ✅ VPN enabled (if geo-blocked)
- [ ] ✅ Firewall/antivirus disabled
- [ ] ✅ Run as Administrator (Windows)
- [ ] ✅ Check log files for errors
- [ ] ✅ Try demo mode first
- [ ] ✅ Use alternative exchange
- [ ] ✅ Update to latest version

---

**💡 Tip: Kebanyakan masalah bisa diselesaikan dengan VPN + restart program!**
