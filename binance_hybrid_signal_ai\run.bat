@echo off
REM Binance Hybrid Signal AI - Windows Launcher
REM ============================================

title Binance Hybrid Signal AI

echo.
echo ================================================================================
echo                    BINANCE HYBRID SIGNAL AI (ASYNC)
echo ================================================================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python tidak ditemukan! Pastikan Python 3.8+ terinstall.
    echo 💡 Download Python dari: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python ditemukan!

REM Check if we're in the right directory
if not exist "binance3timeframe (1).py" (
    echo ❌ File program utama tidak ditemukan!
    echo 💡 Pastikan Anda berada di folder binance_hybrid_signal_ai
    pause
    exit /b 1
)

echo ✅ File program ditemukan!

REM Check if requirements.txt exists
if not exist "requirements.txt" (
    echo ⚠️ File requirements.txt tidak ditemukan!
    echo 💡 Melanjutkan tanpa cek dependencies...
) else (
    echo ✅ File requirements.txt ditemukan!
)

echo.
echo 🚀 Memulai Binance Hybrid Signal AI...
echo ================================================================================
echo.

REM Run the main program
python "binance3timeframe (1).py"

REM Check exit code
if errorlevel 1 (
    echo.
    echo ❌ Program berakhir dengan error!
    echo 💡 Coba install dependencies dengan: pip install -r requirements.txt
) else (
    echo.
    echo ✅ Program selesai dengan sukses!
)

echo.
echo ================================================================================
echo                              Program Selesai
echo ================================================================================
echo.
pause
