"""
File untuk menjalankan program dengan token dan DEX yang paling populer.
"""
import os
import sys
import time
import asyncio
import traceback
import signal

# Variabel global untuk menangani sinyal interupsi
should_exit = False

def signal_handler(sig, frame):
    """
    Menangani sinyal interupsi (Ctrl+C).
    """
    global should_exit
    print("\nMenerima sinyal interupsi. Menghentikan program dengan aman...")
    should_exit = True

async def main():
    """
    Fungsi utama untuk menjalankan program.
    """
    # Tangani sinyal interupsi
    signal.signal(signal.SIGINT, signal_handler)
    
    print("Menjalankan program dengan token dan DEX yang paling populer...")
    
    # Dapatkan direktori script
    script_dir = os.path.dirname(os.path.abspath(__file__))
    
    # Tambahkan direktori saat ini ke sys.path
    sys.path.append(script_dir)
    
    # Impor modul-modul
    from src.utils import load_config, setup_logging
    from src.network import Network
    from src.arbitrage import ArbitrageDetector
    
    # Muat konfigurasi
    config_path = os.path.join(script_dir, "config.yaml")
    config = load_config(config_path)
    
    # Dapatkan pengaturan
    settings = config.get('settings', {})
    
    # Setup logging
    logs_dir = os.path.join(script_dir, "logs")
    os.makedirs(logs_dir, exist_ok=True)
    logger = setup_logging(settings.get('log_level', 'INFO'), logs_dir)
    
    # Daftar token yang paling populer
    popular_tokens = {
        "ethereum": {
            "WETH": "******************************************",
            "USDC": "******************************************",
            "USDT": "******************************************",
            "DAI": "******************************************",
            "WBTC": "******************************************"
        },
        "polygon": {
            "WMATIC": "******************************************",
            "USDC": "******************************************",
            "USDT": "******************************************",
            "DAI": "******************************************",
            "WETH": "******************************************"
        }
    }
    
    # Perbarui konfigurasi dengan token yang populer
    for network_name, tokens in popular_tokens.items():
        if network_name in config['networks']:
            config['networks'][network_name]['tokens'] = tokens
    
    # Modifikasi pengaturan
    settings['max_tokens_per_network'] = 5  # Batasi jumlah token
    settings['max_token_pairs'] = 20  # Batasi jumlah pasangan token
    settings['max_token_triplets'] = 10  # Batasi jumlah triplet token
    settings['token_pair_batch_size'] = 5  # Ukuran batch lebih kecil
    settings['token_triplet_batch_size'] = 2  # Ukuran batch lebih kecil
    settings['filter_stablecoins'] = False  # Jangan filter stablecoin
    settings['max_price_difference_percentage'] = 100.0  # Perbedaan harga maksimum lebih besar
    settings['min_profit_usd'] = 0.01  # Profit minimum lebih kecil
    
    # Simpan semua peluang arbitrase yang ditemukan
    all_opportunities = []
    
    # Waktu mulai
    start_time = time.time()
    
    # Loop utama
    iteration = 0
    try:
        while not should_exit and iteration < 3:  # Batasi jumlah iterasi untuk pengujian
            iteration += 1
            print(f"Iterasi #{iteration}: Memulai pemeriksaan peluang arbitrase...")
            
            # Buat tugas untuk setiap jaringan
            tasks = []
            detectors = []
            
            # Daftar jaringan yang akan dianalisis
            networks_to_analyze = ['polygon', 'ethereum']  # Fokus pada 2 jaringan saja
            
            for network_name in networks_to_analyze:
                if network_name in config['networks']:
                    try:
                        # Inisialisasi objek Network dengan pengaturan
                        network = Network(network_name, config['networks'][network_name], settings)
                        
                        # Inisialisasi detektor arbitrase
                        detector = ArbitrageDetector(network, config['networks'][network_name], settings)
                        detectors.append(detector)
                        
                        # Buat tugas untuk deteksi arbitrase
                        task = asyncio.create_task(detector.detect_arbitrage_opportunities())
                        tasks.append(task)
                    except Exception as e:
                        print(f"Error saat menginisialisasi jaringan {network_name}: {e}")
                        traceback.print_exc()
            
            # Jalankan semua tugas secara bersamaan dengan penanganan error
            try:
                # Gunakan timeout untuk menghindari program hang
                timeout = settings.get('network_timeout_seconds', 180)
                results = await asyncio.wait_for(
                    asyncio.gather(*tasks, return_exceptions=True),
                    timeout=timeout
                )
                
                # Periksa hasil untuk error
                for i, result in enumerate(results):
                    if isinstance(result, Exception):
                        network_name = networks_to_analyze[i]
                        print(f"Error saat mendeteksi arbitrase di jaringan {network_name}: {result}")
                        traceback.print_exc()
            except asyncio.TimeoutError:
                print(f"Timeout saat mendeteksi arbitrase. Melanjutkan ke iterasi berikutnya.")
            except Exception as e:
                print(f"Error tidak terduga saat mendeteksi arbitrase: {e}")
                traceback.print_exc()
            
            # Kumpulkan peluang arbitrase dari semua detektor
            for detector in detectors:
                try:
                    if hasattr(detector, 'arbitrage_opportunities') and detector.arbitrage_opportunities:
                        all_opportunities.extend(detector.arbitrage_opportunities)
                        
                        # Tampilkan peluang arbitrase yang baru ditemukan
                        for opportunity in detector.arbitrage_opportunities:
                            print(f"\n[!] Peluang arbitrase ditemukan di {opportunity['network']}!")
                            print(f"    Jalur: {' -> '.join(opportunity['token_path'])}")
                            print(f"    DEX: {' -> '.join(opportunity['dex_path'])}")
                            print(f"    Profit: ${opportunity['net_profit_usd']:.2f} ({opportunity['profit_percentage']:.2f}%)")
                        
                        # Reset daftar peluang arbitrase di detektor
                        detector.arbitrage_opportunities = []
                except Exception as e:
                    print(f"Error saat mengumpulkan peluang arbitrase: {e}")
                    traceback.print_exc()
            
            # Tampilkan ringkasan
            elapsed_time = time.time() - start_time
            hours, remainder = divmod(elapsed_time, 3600)
            minutes, seconds = divmod(remainder, 60)
            
            print(f"\nRingkasan setelah iterasi #{iteration}:")
            print(f"Waktu berjalan: {int(hours):02}:{int(minutes):02}:{int(seconds):02}")
            print(f"Total peluang arbitrase ditemukan: {len(all_opportunities)}")
            
            # Jika perlu keluar, keluar dari loop
            if should_exit:
                break
            
            # Tunggu interval yang dikonfigurasi
            interval = 5  # Gunakan interval lebih pendek untuk pengujian
            print(f"Menunggu {interval} detik sebelum pemeriksaan berikutnya...")
            
            # Gunakan asyncio.sleep dengan pengecekan should_exit
            for i in range(interval):
                if should_exit:
                    break
                await asyncio.sleep(1)
    
    finally:
        # Tampilkan ringkasan akhir
        print("\n" + "=" * 70)
        print("                   RINGKASAN AKHIR")
        print("=" * 70)
        
        elapsed_time = time.time() - start_time
        hours, remainder = divmod(elapsed_time, 3600)
        minutes, seconds = divmod(remainder, 60)
        
        print(f"Total waktu berjalan: {int(hours):02}:{int(minutes):02}:{int(seconds):02}")
        print(f"Total iterasi: {iteration}")
        print(f"Total peluang arbitrase ditemukan: {len(all_opportunities)}")
        
        # Tampilkan peluang arbitrase terbaik
        if all_opportunities:
            # Urutkan berdasarkan profit bersih
            sorted_opportunities = sorted(all_opportunities, key=lambda x: x['net_profit_usd'], reverse=True)
            
            print("\nPeluang Arbitrase Terbaik:")
            for i, opportunity in enumerate(sorted_opportunities[:5]):  # Tampilkan 5 peluang terbaik
                print(f"\n{i+1}. Jaringan: {opportunity['network']}")
                print(f"   Jalur: {' -> '.join(opportunity['token_path'])}")
                print(f"   DEX: {' -> '.join(opportunity['dex_path'])}")
                print(f"   Profit: ${opportunity['net_profit_usd']:.2f} ({opportunity['profit_percentage']:.2f}%)")
        
        print("\nProgram berhenti dengan aman.")

if __name__ == "__main__":
    # Jalankan dengan asyncio
    asyncio.run(main())
