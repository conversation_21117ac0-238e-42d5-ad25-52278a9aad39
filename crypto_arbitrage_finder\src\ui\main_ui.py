#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Main UI for the Cryptocurrency Arbitrage Finder.
"""

import asyncio
import signal
import platform
from datetime import datetime
from rich.console import Console
from rich.live import Live
from rich.layout import Layout
from rich.panel import Panel
from rich.text import Text

from ..core.arbitrage_finder import ArbitrageFinder
from ..utils.display_utils import (
    clear_screen, create_layout, create_header, create_market_table,
    create_opportunities_table, create_error_table, create_footer
)

class MainUI:
    """Main UI for the Cryptocurrency Arbitrage Finder."""

    def __init__(self, arbitrage_finder):
        """
        Initialize the UI.

        Args:
            arbitrage_finder (ArbitrageFinder): The arbitrage finder instance
        """
        self.arbitrage_finder = arbitrage_finder
        self.console = Console()
        self.layout = create_layout()
        self.running = False
        self.update_interval = 1.0  # Update interval in seconds
        self.update_task = None
        self.market_data = []
        self.last_update = None

    async def start(self):
        """Start the UI."""
        if self.running:
            return

        self.running = True

        # Clear the screen
        clear_screen()

        # Start the arbitrage finder (in background)
        asyncio.create_task(self.arbitrage_finder.start())

        # Wait a bit to let the UI initialize
        await asyncio.sleep(0.5)

        # Start the UI update loop
        self.update_task = asyncio.create_task(self._update_loop())

        # Set up signal handlers for graceful shutdown
        if platform.system() != "Windows":
            for sig in (signal.SIGINT, signal.SIGTERM):
                signal.signal(sig, self._signal_handler)
        else:
            print("Running on Windows - signal handlers not supported")

        # Start the live display
        with Live(self.layout, refresh_per_second=4, screen=True):
            try:
                # Keep the main task running
                while self.running:
                    await asyncio.sleep(0.1)
            except asyncio.CancelledError:
                pass
            finally:
                await self.stop()

    async def stop(self):
        """Stop the UI."""
        if not self.running:
            return

        self.running = False

        # Cancel the update task
        if self.update_task:
            self.update_task.cancel()
            try:
                await self.update_task
            except asyncio.CancelledError:
                pass

        # Stop the arbitrage finder
        await self.arbitrage_finder.stop()

    def _signal_handler(self, sig, frame):
        """Handle signals for graceful shutdown."""
        if self.running:
            self.running = False
            asyncio.create_task(self.stop())

    async def _update_loop(self):
        """Update the UI periodically."""
        while self.running:
            try:
                # Update the UI
                self._update_ui()

                # Wait for the next update
                await asyncio.sleep(self.update_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                print(f"Error in UI update loop: {e}")
                await asyncio.sleep(self.update_interval)

    def _update_ui(self):
        """Update the UI components."""
        try:
            # Update the header
            header = create_header()
            self.layout["header"].update(header)

            # Update the market data
            self._update_market_data()
            market_table = create_market_table(self.market_data)
            self.layout["market_data"].update(market_table)

            # Get real arbitrage opportunities
            opportunities = self.arbitrage_finder.find_arbitrage_opportunities()

            # If no opportunities, create dummy ones for UI testing
            if not opportunities:
                opportunities = self._generate_dummy_opportunities()

            # Create top opportunities table (top 10)
            top_opportunities = create_opportunities_table(
                opportunities,
                title="Top 10 Peluang Arbitrase",
                max_rows=10
            )
            self.layout["top_opportunities"].update(top_opportunities)

            # Get real errors
            errors = self.arbitrage_finder.get_errors()
            error_table = create_error_table(errors)
            self.layout["errors"].update(error_table)

            # Update the footer
            self.last_update = datetime.now().strftime("%H:%M:%S")
            footer = create_footer("Running", self.last_update)
            self.layout["footer"].update(footer)
        except Exception as e:
            error_msg = f"Error updating UI: {e}"
            print(error_msg)
            self.arbitrage_finder.add_error("system", error_msg)

    def _generate_dummy_opportunities(self):
        """Generate dummy arbitrage opportunities for testing."""
        import random

        # Define default symbols for testing
        symbols = ['BTC/USDT', 'ETH/USDT', 'BNB/USDT', 'SOL/USDT', 'XRP/USDT']

        # Define exchanges for testing
        exchanges = [
            'binance', 'kucoin'
        ]

        # Base prices for common symbols
        base_prices = {
            'BTC/USDT': 65000.0,
            'ETH/USDT': 3500.0,
            'BNB/USDT': 600.0,
            'SOL/USDT': 150.0,
            'XRP/USDT': 0.55,
        }

        # Generate opportunities
        opportunities = []

        # Create 15 random opportunities (so we can show top 10)
        for _ in range(15):
            # Pick random symbol
            symbol = random.choice(symbols)

            # Pick two different exchanges
            buy_exchange, sell_exchange = random.sample(exchanges, 2)

            # Get base price
            base_price = base_prices.get(symbol, 1000.0)

            # Create price difference (0.1% to 3%)
            price_diff = random.uniform(0.001, 0.03)

            # Calculate buy and sell prices
            buy_price = base_price * (1.0 - price_diff/2)
            sell_price = base_price * (1.0 + price_diff/2)

            # Calculate profit percentage
            profit_percent = (sell_price - buy_price) / buy_price * 100

            # Create opportunity
            opportunity = {
                'symbol': symbol,
                'buy_exchange': buy_exchange.upper(),
                'buy_price': buy_price,
                'sell_exchange': sell_exchange.upper(),
                'sell_price': sell_price,
                'profit_percent': profit_percent,
                'timestamp': datetime.now().isoformat()
            }

            opportunities.append(opportunity)

        # Sort by profit percentage (descending)
        opportunities.sort(key=lambda x: x['profit_percent'], reverse=True)

        return opportunities



    def _update_market_data(self):
        """Update the market data."""
        try:
            # Clear the market data
            self.market_data = []

            # Get all exchange clients
            clients = self.arbitrage_finder.client_factory.get_all_clients()

            # Get common symbols
            common_symbols = self.arbitrage_finder.common_symbols

            # Limit to top 5 symbols for display
            top_symbols = list(common_symbols)[:5] if common_symbols else []

            # If no symbols are available, use some default symbols
            if not top_symbols:
                top_symbols = ['BTC/USDT', 'ETH/USDT', 'BNB/USDT', 'SOL/USDT', 'XRP/USDT']

            # Get market data for each exchange and symbol
            for exchange_id, client in clients.items():
                for symbol in top_symbols:
                    try:
                        # Get orderbook data
                        orderbook = client.get_orderbook_data(symbol)

                        if orderbook and 'bid' in orderbook and 'ask' in orderbook:
                            # Calculate spread percentage
                            bid = orderbook['bid']
                            ask = orderbook['ask']
                            spread_pct = (ask - bid) / bid * 100

                            # Add to market data
                            self.market_data.append({
                                'exchange': exchange_id.upper(),
                                'symbol': symbol,
                                'bid': bid,
                                'ask': ask,
                                'spread': f"{spread_pct:.4f}",
                                'updated': datetime.now().strftime("%H:%M:%S")
                            })
                    except Exception as e:
                        error_msg = f"Error getting market data for {symbol} on {exchange_id}: {e}"
                        print(error_msg)
                        self.arbitrage_finder.add_error(exchange_id, error_msg)

            # If no market data was collected, generate dummy data
            if not self.market_data:
                self._generate_dummy_market_data(top_symbols)

        except Exception as e:
            error_msg = f"Error updating market data: {e}"
            print(error_msg)
            self.arbitrage_finder.add_error("system", error_msg)

    def _generate_dummy_market_data(self, symbols):
        """Generate dummy market data for testing."""
        import random

        # Define exchanges for testing
        exchanges = [
            'binance', 'kucoin'
        ]

        # Base prices for common symbols
        base_prices = {
            'BTC/USDT': 65000.0,
            'ETH/USDT': 3500.0,
            'BNB/USDT': 600.0,
            'SOL/USDT': 150.0,
            'XRP/USDT': 0.55,
        }

        # Exchange price factors (to create price differences)
        exchange_factors = {
            'binance': 1.0,
            'kucoin': 0.99
        }

        # Generate market data for each exchange and symbol
        for exchange_id in exchanges:
            for symbol in symbols:
                # Get base price
                base_price = base_prices.get(symbol, 1000.0)

                # Apply exchange factor
                factor = exchange_factors.get(exchange_id, 1.0)
                mid_price = base_price * factor

                # Create a small spread
                spread = mid_price * 0.001  # 0.1% spread
                bid = mid_price - spread/2
                ask = mid_price + spread/2

                # Calculate spread percentage
                spread_pct = (ask - bid) / bid * 100

                # Add to market data
                self.market_data.append({
                    'exchange': exchange_id.upper(),
                    'symbol': symbol,
                    'bid': bid,
                    'ask': ask,
                    'spread': f"{spread_pct:.4f}",
                    'updated': datetime.now().strftime("%H:%M:%S")
                })
