# BAGIAN PALING ATAS: WORKAROUND UNTUK ImportError numpy.NaN di pandas_ta
# =====================================================================
import numpy
import sys
try:
    if not hasattr(numpy, 'NaN') and hasattr(numpy, 'nan'):
        numpy.NaN = numpy.nan # type: ignore
    elif not hasattr(numpy, 'nan') and hasattr(numpy, 'NaN'):
        numpy.nan = numpy.NaN # type: ignore
except Exception as e:
    print(f"WORKAROUND: Failed to apply numpy.NaN/nan monkey patch: {e}", file=sys.stderr)
# =====================================================================

import pandas as pd
import ta
from ta import add_all_ta_features
from ta.utils import dropna
import pandas_ta as pta
import threading
import time
from concurrent.futures import ThreadPoolExecutor, as_completed, ProcessPoolExecutor
import os
import traceback
import logging
import requests # Untuk panggilan HTTP REST API
import json   # Untuk parsing JSON
import random
import hashlib  # For caching
import pickle   # For data serialization
from functools import lru_cache  # For function caching
import asyncio
import aiohttp
import multiprocessing
from typing import Dict, List, Tuple, Optional

from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QLabel,
    QPushButton, QScrollArea, QFrame, QTextEdit, QSizePolicy, QSpacerItem,
    QProgressBar, QStackedWidget, QGraphicsView, QGraphicsScene, QGraphicsEllipseItem,
    QGraphicsTextItem, QGraphicsRectItem, QTabWidget, QGroupBox, QTableWidget,
    QTableWidgetItem, QHeaderView, QSplitter, QSpinBox, QDoubleSpinBox, QCheckBox,
    QComboBox, QSlider, QFormLayout, QGridLayout, QLineEdit, QMessageBox, QFileDialog, QToolTip
)
from PySide6.QtCore import Qt, QThread, Signal, QSize, QTimer, QRectF, QPointF, Property
from PySide6.QtGui import QColor, QFont, QIcon, QPixmap, QPainter, QBrush, QPen, QPainterPath, QLinearGradient, QPalette

# --- Konfigurasi Logging ---
log_file_handler = logging.FileHandler('intellitrader_x_v3_manual_rest_fixed2.log', encoding='utf-8', mode='w') # Nama file log diubah
log_file_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(threadName)s - %(message)s'))
logging.basicConfig(
    level=logging.INFO, 
    format='%(asctime)s - %(levelname)s - %(threadName)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        log_file_handler
    ]
)

# --- Helper Function untuk Dual Logging (Console + GUI) ---
def log_to_console_and_gui(message, log_signal=None, level="INFO"):
    formatted_message = f"[{level.upper()}] {time.strftime('%H:%M:%S')} - {message}"
    if level.upper() == "INFO": logging.info(message)
    elif level.upper() == "WARNING": logging.warning(message)
    elif level.upper() == "ERROR": logging.error(message)
    elif level.upper() == "DEBUG": logging.debug(message)
    else: logging.info(message)
    if log_signal: log_signal.emit(formatted_message)


# --- Configuration Class for GUI Settings ---
class TradingConfig:
    """Centralized configuration management for trading parameters"""

    def __init__(self):
        # Default values - can be modified via GUI
        self.reset_to_defaults()

    def reset_to_defaults(self):
        """Reset all settings to default values"""
        # Display & Performance
        self.MAX_PAIRS_DISPLAY = 50
        self.CANDLE_LIMIT_PER_TIMEFRAME = 500
        self.TIMEFRAMES_TO_ANALYZE = ['4h', '1h', '30m', '15m', '5m']
        self.REFERENCE_TIMEFRAME_FOR_PRICE = '1h'

        # Performance Optimization - BALANCED FOR RELIABILITY
        self.MAX_WORKERS_ANALYSIS = min(16, max(4, multiprocessing.cpu_count() * 2))  # Balanced parallelism
        self.MAX_CONCURRENT_DOWNLOADS = 50  # Balanced for stability
        self.DOWNLOAD_BATCH_SIZE = 100  # Optimal batch size to prevent timeouts
        self.ANALYSIS_BATCH_SIZE = 25  # Balanced batch size
        self.ASYNC_TIMEOUT_SECONDS = 45  # Increased for reliable downloads
        self.CONNECTION_POOL_SIZE = 150  # Balanced connection pool

        # Confidence & Thresholds - LOWERED FOR MORE OPPORTUNITIES
        self.MIN_CONFIDENCE_THRESHOLD = 0.15  # Lowered from 0.30 to 0.15 for more signals
        self.ADVANCED_SIGNAL_THRESHOLD = 0.50  # Lowered from 0.75 to 0.50 for more opportunities
        self.CONFLUENCE_MULTIPLIER = 1.3  # Slightly lowered from 1.5 to 1.3
        self.VOLUME_SPIKE_THRESHOLD = 1.5  # Lowered from 2.0 to 1.5 for more volume signals
        self.MOMENTUM_LOOKBACK = 15  # Reduced from 20 to 15 for faster detection
        self.TREND_STRENGTH_PERIODS = 40  # Reduced from 50 to 40

        # Filtering Settings - LOWERED FOR MORE OPPORTUNITIES
        self.ENABLE_STABLECOIN_FILTERING = False  # Disabled to include more pairs
        self.MIN_VOLUME_24H_USD = 50_000  # Lowered from 100K to 50K for more pairs
        self.MIN_ATR_PERCENTAGE = 0.005  # Lowered from 0.01 to 0.005 for less volatile pairs
        self.MAX_ATR_PERCENTAGE = 75.0  # Increased from 50.0 to 75.0 for more volatile pairs
        self.MIN_RRR_RATIO = 0.3  # Lowered from 0.5 to 0.3 for more opportunities
        self.VOLUME_CONFIRMATION_MULTIPLIER = 1.005  # Lowered from 1.01 to 1.005

        # Mode Settings
        self.DISCOVERY_MODE = True
        self.EMERGENCY_MODE = True
        self.DEBUG_FILTERING = True
        self.DEMO_MODE = False

        # API Settings - RELIABILITY OPTIMIZED
        self.API_REQUEST_DELAY_MS = 25  # Balanced delay to prevent rate limiting
        self.API_RETRY_DELAY_S = 2  # Reasonable retry delay
        self.MAX_API_RETRIES = 4  # More retries for reliability
        self.REQUEST_TIMEOUT_S = 10  # Longer timeout for stable connections

        # Indicator Weights
        self.INDICATOR_WEIGHTS = {
            'market_structure_trend': 4.5,
            'smc_zone_reaction': 4.0,
            'liquidity_event': 3.0,
            'volume_confirmation': 3.5,
            'multi_tf_alignment': 5.0,
            'momentum_divergence_strong': 3.5,
            'htf_trend_clarity': 2.5,
            'general_ta_score': 1.5,
            'price_action_patterns': 3.0,
            'volatility_analysis': 2.0,
            'fibonacci_levels': 2.5,
            'support_resistance': 3.0,
            'trend_strength': 2.8,
            'momentum_oscillators': 2.2,
            'volume_profile': 2.8,
            'market_sentiment': 2.0,
        }

        # Technical Indicator Settings per Timeframe
        self.INDICATOR_SETTINGS = {
            '4h': {'rsi_period': 14, 'rsi_oversold': 25, 'rsi_overbought': 75, 'macd_fast': 12, 'macd_slow': 26, 'macd_signal': 9, 'bb_period': 20, 'bb_std_dev': 2.0, 'ema_short': 34, 'ema_medium': 89, 'ema_long': 200, 'atr_period': 14, 'adx_period': 14},
            '1h': {'rsi_period': 14, 'rsi_oversold': 28, 'rsi_overbought': 72, 'macd_fast': 12, 'macd_slow': 26, 'macd_signal': 9, 'bb_period': 20, 'bb_std_dev': 2.0, 'ema_short': 21, 'ema_medium': 55, 'ema_long': 100, 'atr_period': 14, 'adx_period': 14},
            '30m': {'rsi_period': 12, 'rsi_oversold': 30, 'rsi_overbought': 70, 'macd_fast': 10, 'macd_slow': 21, 'macd_signal': 8, 'bb_period': 20, 'bb_std_dev': 2.1, 'ema_short': 13, 'ema_medium': 34, 'ema_long': 55, 'atr_period': 12, 'adx_period': 12},
            '15m': {'rsi_period': 10, 'rsi_oversold': 20, 'rsi_overbought': 80, 'macd_fast': 9, 'macd_slow': 18, 'macd_signal': 6, 'bb_period': 15, 'bb_std_dev': 2.0, 'ema_short': 9, 'ema_medium': 21, 'ema_long': 34, 'atr_period': 10, 'adx_period': 10},
            '5m': {'rsi_period': 8, 'rsi_oversold': 15, 'rsi_overbought': 85, 'macd_fast': 8, 'macd_slow': 15, 'macd_signal': 5, 'bb_period': 12, 'bb_std_dev': 2.0, 'ema_short': 5, 'ema_medium': 13, 'ema_long': 21, 'atr_period': 8, 'adx_period': 8}
        }

    def save_to_file(self, filename="trading_config.json"):
        """Save current configuration to JSON file"""
        import json
        config_data = {
            'display_performance': {
                'MAX_PAIRS_DISPLAY': self.MAX_PAIRS_DISPLAY,
                'CANDLE_LIMIT_PER_TIMEFRAME': self.CANDLE_LIMIT_PER_TIMEFRAME,
                'TIMEFRAMES_TO_ANALYZE': self.TIMEFRAMES_TO_ANALYZE,
                'REFERENCE_TIMEFRAME_FOR_PRICE': self.REFERENCE_TIMEFRAME_FOR_PRICE,
            },
            'performance': {
                'MAX_WORKERS_ANALYSIS': self.MAX_WORKERS_ANALYSIS,
                'MAX_CONCURRENT_DOWNLOADS': self.MAX_CONCURRENT_DOWNLOADS,
                'DOWNLOAD_BATCH_SIZE': self.DOWNLOAD_BATCH_SIZE,
                'ANALYSIS_BATCH_SIZE': self.ANALYSIS_BATCH_SIZE,
                'ASYNC_TIMEOUT_SECONDS': self.ASYNC_TIMEOUT_SECONDS,
                'CONNECTION_POOL_SIZE': self.CONNECTION_POOL_SIZE,
            },
            'thresholds': {
                'MIN_CONFIDENCE_THRESHOLD': self.MIN_CONFIDENCE_THRESHOLD,
                'ADVANCED_SIGNAL_THRESHOLD': self.ADVANCED_SIGNAL_THRESHOLD,
                'CONFLUENCE_MULTIPLIER': self.CONFLUENCE_MULTIPLIER,
                'VOLUME_SPIKE_THRESHOLD': self.VOLUME_SPIKE_THRESHOLD,
                'MOMENTUM_LOOKBACK': self.MOMENTUM_LOOKBACK,
                'TREND_STRENGTH_PERIODS': self.TREND_STRENGTH_PERIODS,
            },
            'filtering': {
                'ENABLE_STABLECOIN_FILTERING': self.ENABLE_STABLECOIN_FILTERING,
                'MIN_VOLUME_24H_USD': self.MIN_VOLUME_24H_USD,
                'MIN_ATR_PERCENTAGE': self.MIN_ATR_PERCENTAGE,
                'MAX_ATR_PERCENTAGE': self.MAX_ATR_PERCENTAGE,
                'MIN_RRR_RATIO': self.MIN_RRR_RATIO,
                'VOLUME_CONFIRMATION_MULTIPLIER': self.VOLUME_CONFIRMATION_MULTIPLIER,
            },
            'modes': {
                'DISCOVERY_MODE': self.DISCOVERY_MODE,
                'EMERGENCY_MODE': self.EMERGENCY_MODE,
                'DEBUG_FILTERING': self.DEBUG_FILTERING,
                'DEMO_MODE': self.DEMO_MODE,
            },
            'api': {
                'API_REQUEST_DELAY_MS': self.API_REQUEST_DELAY_MS,
                'API_RETRY_DELAY_S': self.API_RETRY_DELAY_S,
                'MAX_API_RETRIES': self.MAX_API_RETRIES,
                'REQUEST_TIMEOUT_S': self.REQUEST_TIMEOUT_S,
            },
            'indicator_weights': self.INDICATOR_WEIGHTS,
            'indicator_settings': self.INDICATOR_SETTINGS
        }

        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
            return True, f"Konfigurasi berhasil disimpan ke {filename}"
        except Exception as e:
            return False, f"Error menyimpan konfigurasi: {e}"

    def load_from_file(self, filename="trading_config.json"):
        """Load configuration from JSON file"""
        import json
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                config_data = json.load(f)

            # Load display & performance settings
            if 'display_performance' in config_data:
                dp = config_data['display_performance']
                self.MAX_PAIRS_DISPLAY = dp.get('MAX_PAIRS_DISPLAY', self.MAX_PAIRS_DISPLAY)
                self.CANDLE_LIMIT_PER_TIMEFRAME = dp.get('CANDLE_LIMIT_PER_TIMEFRAME', self.CANDLE_LIMIT_PER_TIMEFRAME)
                self.TIMEFRAMES_TO_ANALYZE = dp.get('TIMEFRAMES_TO_ANALYZE', self.TIMEFRAMES_TO_ANALYZE)
                self.REFERENCE_TIMEFRAME_FOR_PRICE = dp.get('REFERENCE_TIMEFRAME_FOR_PRICE', self.REFERENCE_TIMEFRAME_FOR_PRICE)

            # Load performance settings
            if 'performance' in config_data:
                perf = config_data['performance']
                self.MAX_WORKERS_ANALYSIS = perf.get('MAX_WORKERS_ANALYSIS', self.MAX_WORKERS_ANALYSIS)
                self.MAX_CONCURRENT_DOWNLOADS = perf.get('MAX_CONCURRENT_DOWNLOADS', self.MAX_CONCURRENT_DOWNLOADS)
                self.DOWNLOAD_BATCH_SIZE = perf.get('DOWNLOAD_BATCH_SIZE', self.DOWNLOAD_BATCH_SIZE)
                self.ANALYSIS_BATCH_SIZE = perf.get('ANALYSIS_BATCH_SIZE', self.ANALYSIS_BATCH_SIZE)
                self.ASYNC_TIMEOUT_SECONDS = perf.get('ASYNC_TIMEOUT_SECONDS', self.ASYNC_TIMEOUT_SECONDS)
                self.CONNECTION_POOL_SIZE = perf.get('CONNECTION_POOL_SIZE', self.CONNECTION_POOL_SIZE)

            # Load threshold settings
            if 'thresholds' in config_data:
                thresh = config_data['thresholds']
                self.MIN_CONFIDENCE_THRESHOLD = thresh.get('MIN_CONFIDENCE_THRESHOLD', self.MIN_CONFIDENCE_THRESHOLD)
                self.ADVANCED_SIGNAL_THRESHOLD = thresh.get('ADVANCED_SIGNAL_THRESHOLD', self.ADVANCED_SIGNAL_THRESHOLD)
                self.CONFLUENCE_MULTIPLIER = thresh.get('CONFLUENCE_MULTIPLIER', self.CONFLUENCE_MULTIPLIER)
                self.VOLUME_SPIKE_THRESHOLD = thresh.get('VOLUME_SPIKE_THRESHOLD', self.VOLUME_SPIKE_THRESHOLD)
                self.MOMENTUM_LOOKBACK = thresh.get('MOMENTUM_LOOKBACK', self.MOMENTUM_LOOKBACK)
                self.TREND_STRENGTH_PERIODS = thresh.get('TREND_STRENGTH_PERIODS', self.TREND_STRENGTH_PERIODS)

            # Load filtering settings
            if 'filtering' in config_data:
                filt = config_data['filtering']
                self.ENABLE_STABLECOIN_FILTERING = filt.get('ENABLE_STABLECOIN_FILTERING', self.ENABLE_STABLECOIN_FILTERING)
                self.MIN_VOLUME_24H_USD = filt.get('MIN_VOLUME_24H_USD', self.MIN_VOLUME_24H_USD)
                self.MIN_ATR_PERCENTAGE = filt.get('MIN_ATR_PERCENTAGE', self.MIN_ATR_PERCENTAGE)
                self.MAX_ATR_PERCENTAGE = filt.get('MAX_ATR_PERCENTAGE', self.MAX_ATR_PERCENTAGE)
                self.MIN_RRR_RATIO = filt.get('MIN_RRR_RATIO', self.MIN_RRR_RATIO)
                self.VOLUME_CONFIRMATION_MULTIPLIER = filt.get('VOLUME_CONFIRMATION_MULTIPLIER', self.VOLUME_CONFIRMATION_MULTIPLIER)

            # Load mode settings
            if 'modes' in config_data:
                modes = config_data['modes']
                self.DISCOVERY_MODE = modes.get('DISCOVERY_MODE', self.DISCOVERY_MODE)
                self.EMERGENCY_MODE = modes.get('EMERGENCY_MODE', self.EMERGENCY_MODE)
                self.DEBUG_FILTERING = modes.get('DEBUG_FILTERING', self.DEBUG_FILTERING)
                self.DEMO_MODE = modes.get('DEMO_MODE', self.DEMO_MODE)

            # Load API settings
            if 'api' in config_data:
                api = config_data['api']
                self.API_REQUEST_DELAY_MS = api.get('API_REQUEST_DELAY_MS', self.API_REQUEST_DELAY_MS)
                self.API_RETRY_DELAY_S = api.get('API_RETRY_DELAY_S', self.API_RETRY_DELAY_S)
                self.MAX_API_RETRIES = api.get('MAX_API_RETRIES', self.MAX_API_RETRIES)
                self.REQUEST_TIMEOUT_S = api.get('REQUEST_TIMEOUT_S', self.REQUEST_TIMEOUT_S)

            # Load indicator weights
            if 'indicator_weights' in config_data:
                self.INDICATOR_WEIGHTS.update(config_data['indicator_weights'])

            # Load indicator settings
            if 'indicator_settings' in config_data:
                self.INDICATOR_SETTINGS.update(config_data['indicator_settings'])

            return True, f"Konfigurasi berhasil dimuat dari {filename}"
        except FileNotFoundError:
            return False, f"File {filename} tidak ditemukan"
        except Exception as e:
            return False, f"Error memuat konfigurasi: {e}"

# Global configuration instance
trading_config = TradingConfig()

# --- Konstanta dan Konfigurasi ENHANCED (Updated to use config) ---
MAX_PAIRS_DISPLAY = trading_config.MAX_PAIRS_DISPLAY
CANDLE_LIMIT_PER_TIMEFRAME = trading_config.CANDLE_LIMIT_PER_TIMEFRAME
TIMEFRAMES_TO_ANALYZE = trading_config.TIMEFRAMES_TO_ANALYZE
REFERENCE_TIMEFRAME_FOR_PRICE = trading_config.REFERENCE_TIMEFRAME_FOR_PRICE

# --- Performance Optimization V5 (Updated to use config) ---
OPTIMAL_CPU_CORES = max(1, multiprocessing.cpu_count() - 1)  # Leave 1 core for system
MAX_WORKERS_ANALYSIS = trading_config.MAX_WORKERS_ANALYSIS
MAX_CONCURRENT_DOWNLOADS = trading_config.MAX_CONCURRENT_DOWNLOADS
DOWNLOAD_BATCH_SIZE = trading_config.DOWNLOAD_BATCH_SIZE
ANALYSIS_BATCH_SIZE = trading_config.ANALYSIS_BATCH_SIZE
ASYNC_TIMEOUT_SECONDS = trading_config.ASYNC_TIMEOUT_SECONDS
CONNECTION_POOL_SIZE = trading_config.CONNECTION_POOL_SIZE

# --- V5 Enhanced: Stablecoin Filtering Configuration ---
ENABLE_STABLECOIN_FILTERING = True  # Enable/disable stablecoin filtering
STABLECOINS_MAJOR = ['USDC', 'USDT', 'BUSD', 'DAI', 'TUSD', 'USDP', 'FRAX']
STABLECOINS_REGIONAL = ['FDUSD', 'USDD', 'GUSD', 'PYUSD']
STABLECOINS_ALGORITHMIC = ['UST', 'USTC', 'LUSD', 'SUSD']
ALL_STABLECOINS = STABLECOINS_MAJOR + STABLECOINS_REGIONAL + STABLECOINS_ALGORITHMIC

# --- V5 Enhanced: EMERGENCY MODE - Maximum Signal Detection (Updated to use config) ---
MIN_CONFIDENCE_THRESHOLD = trading_config.MIN_CONFIDENCE_THRESHOLD
MIN_VOLUME_24H_USD = trading_config.MIN_VOLUME_24H_USD
MIN_ATR_PERCENTAGE = trading_config.MIN_ATR_PERCENTAGE
MAX_ATR_PERCENTAGE = trading_config.MAX_ATR_PERCENTAGE
MIN_RRR_RATIO = trading_config.MIN_RRR_RATIO
VOLUME_CONFIRMATION_MULTIPLIER = trading_config.VOLUME_CONFIRMATION_MULTIPLIER

# Emergency Mode Flags (Updated to use config)
DISCOVERY_MODE = trading_config.DISCOVERY_MODE
EMERGENCY_MODE = trading_config.EMERGENCY_MODE
DEBUG_FILTERING = trading_config.DEBUG_FILTERING

# --- Advanced Algorithm Configuration (Updated to use config) ---
ADVANCED_SIGNAL_THRESHOLD = trading_config.ADVANCED_SIGNAL_THRESHOLD
CONFLUENCE_MULTIPLIER = trading_config.CONFLUENCE_MULTIPLIER
VOLUME_SPIKE_THRESHOLD = trading_config.VOLUME_SPIKE_THRESHOLD
MOMENTUM_LOOKBACK = trading_config.MOMENTUM_LOOKBACK
TREND_STRENGTH_PERIODS = trading_config.TREND_STRENGTH_PERIODS

# --- Konfigurasi Permintaan API Manual (Updated to use config) ---
BINANCE_FUTURES_API_BASE_URL = "https://fapi.binance.com"
API_REQUEST_DELAY_MS = trading_config.API_REQUEST_DELAY_MS
API_RETRY_DELAY_S = trading_config.API_RETRY_DELAY_S
MAX_API_RETRIES = trading_config.MAX_API_RETRIES
REQUEST_TIMEOUT_S = trading_config.REQUEST_TIMEOUT_S

# --- Mode Demo untuk Testing (Updated to use config) ---
DEMO_MODE = trading_config.DEMO_MODE
DEMO_PAIRS = ['BTCUSDT', 'ETHUSDT', 'SOLUSDT', 'BNBUSDT', 'XRPUSDT', 'ADAUSDT', 'DOGEUSDT']

# --- Performance Optimization V4 ---
ENABLE_CACHING = True
CACHE_EXPIRY_MINUTES = 5  # Cache indicators for 5 minutes
PARALLEL_INDICATOR_CALCULATION = True
BATCH_SIZE_OPTIMIZATION = 50  # Process pairs in batches

# --- Enhanced Bobot dan Skor Indikator/Fitur (ADVANCED V4) - Updated to use config ---
def get_indicator_weights():
    """Get current indicator weights from configuration"""
    global trading_config
    return trading_config.INDICATOR_WEIGHTS

# Legacy reference for backward compatibility
INDICATOR_WEIGHTS_V4 = trading_config.INDICATOR_WEIGHTS

SIGNAL_SCORES_V3 = {
    "Sangat Bullish Sistem": 2.0, "Bullish Sistem": 1.0, "Netral Sistem": 0.0, "Bearish Sistem": -1.0, "Sangat Bearish Sistem": -2.0,
    "Bullish Divergence Kuat (RSI/MACD)": 2.5, "Bullish Divergence Sedang (RSI/MACD)": 1.8,
    "Bearish Divergence Kuat (RSI/MACD)": -2.5, "Bearish Divergence Sedang (RSI/MACD)": -1.8,
    "RSI Oversold Ekstrem (<20)": 1.2, "RSI Overbought Ekstrem (>80)": -1.2,
    "RSI Oversold (<30)": 0.8, "RSI Overbought (>70)": -0.8,
    "MACD Bullish Cross Kuat": 1.5, "MACD Bearish Cross Kuat": -1.5,
    "Harga di Atas EMA Cloud Bullish": 1.2, "Harga di Bawah EMA Cloud Bearish": -1.2,
    "Harga di Atas VWAP (Bullish Bias)": 0.7, "Harga di Bawah VWAP (Bearish Bias)": -0.7,
    "ADX Tren Bullish Kuat (>25, DI+ > DI-)": 1.8, "ADX Tren Bearish Kuat (>25, DI- > DI+)": -1.8,
    "Bullish Order Block Valid (HTF/MTF)": 2.5, "Bearish Order Block Valid (HTF/MTF)": -2.5,
    "Bullish FVG Belum Termitigasi (Zona Demand)": 2.0, "Bearish FVG Belum Termitigasi (Zona Supply)": -2.0,
    "Reaksi Bullish di Zona Demand (OB/FVG)": 1.5, "Reaksi Bearish di Zona Supply (OB/FVG)": -1.5,
    "Sweep Likuiditas Bawah diikuti Reaksi Bullish": 2.2, "Sweep Likuiditas Atas diikuti Reaksi Bearish": -2.2,
    "Bullish CHoCH Dikonfirmasi (LTF/MTF)": 2.5, "Bearish CHoCH Dikonfirmasi (LTF/MTF)": -2.5,
    "Bullish BoS Dikonfirmasi (Searah Tren HTF)": 2.8, "Bearish BoS Dikonfirmasi (Searah Tren HTF)": -2.8,
    "Volume Spike Konfirmasi Breakout Bullish": 1.5, "Volume Spike Konfirmasi Breakout Bearish": -1.5,
    "Volume Tinggi pada Reaksi Zona Bullish": 1.0, "Volume Tinggi pada Reaksi Zona Bearish": -1.0,
    "Konfluensi Bullish HTF-MTF-LTF": 3.5, "Konfluensi Bearish HTF-MTF-LTF": -3.5,
    "HTF Bullish, MTF Konfirmasi Bullish": 2.0, "HTF Bearish, MTF Konfirmasi Bearish": -2.0,
}

# --- Skema Warna Modern & Elegan (Soft & Relaxed) V3 ---
COLOR_BACKGROUND_MAIN = QColor("#EAF0F6") 
COLOR_PRIMARY_ACCENT_APP = QColor("#7B66FF") 
COLOR_SECONDARY_ACCENT_APP = QColor("#48D1CC") 
COLOR_CARD_BG = QColor("#FFFFFF")
COLOR_TEXT_HEADLINE = QColor("#2C3A47") 
COLOR_TEXT_BODY = QColor("#5D6D7E")    
COLOR_TEXT_MUTED = QColor("#95A5A6")   
COLOR_BUTTON_PRIMARY_TEXT = QColor("#FFFFFF")
COLOR_SUCCESS_APP = QColor("#27AE60") 
COLOR_ERROR_APP = QColor("#C0392B")   
COLOR_WARNING_APP = QColor("#F39C12") 
COLOR_PROGRESS_CHUNK_START = COLOR_PRIMARY_ACCENT_APP.lighter(110)
COLOR_PROGRESS_CHUNK_END = COLOR_SECONDARY_ACCENT_APP.lighter(110)
COLOR_PROGRESS_BG_APP = QColor("#DDE2E8")
COLOR_SHADOW = QColor(150, 150, 180, 30)

# --- Custom UI Components ---
class AnimatedBackgroundWidget(QWidget):
    def __init__(self, color1, color2):
        super().__init__()
        self.color1 = color1
        self.color2 = color2
        self.setAutoFillBackground(True)
        # Use stylesheet instead of custom painting to avoid QPainter conflicts
        self.setStyleSheet(f"""
            QWidget {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {color1.name()}, stop:1 {color2.name()});
            }}
        """)

    def paintEvent(self, event):
        # Use stylesheet rendering instead of manual QPainter to avoid conflicts
        super().paintEvent(event)

class ModernCollapsibleCard(QFrame):
    def __init__(self, title):
        super().__init__()
        self.setFrameStyle(QFrame.Box)
        self.setStyleSheet(f"""
            QFrame {{
                background-color: {COLOR_CARD_BG.name()};
                border: 1px solid {COLOR_PROGRESS_BG_APP.name()};
                border-radius: 10px;
                margin: 5px;
            }}
        """)

        layout = QVBoxLayout(self)
        self.toggle_button = QPushButton(f"▼ {title}")
        self.toggle_button.setCheckable(True)
        self.toggle_button.setChecked(False)
        self.toggle_button.clicked.connect(self.toggle_content)
        self.toggle_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {COLOR_PRIMARY_ACCENT_APP.name()};
                color: white;
                border: none;
                padding: 10px;
                text-align: left;
                font-weight: bold;
                border-radius: 5px;
            }}
            QPushButton:hover {{
                background-color: {COLOR_PRIMARY_ACCENT_APP.darker(110).name()};
            }}
        """)
        layout.addWidget(self.toggle_button)

        self.content_widget = QWidget()
        self.content_widget.setVisible(False)
        layout.addWidget(self.content_widget)

    def setContentWidget(self, widget):
        if self.content_widget.layout():
            QWidget().setLayout(self.content_widget.layout())
        layout = QVBoxLayout(self.content_widget)
        layout.addWidget(widget)

    def toggle_content(self):
        is_visible = self.content_widget.isVisible()
        self.content_widget.setVisible(not is_visible)
        self.toggle_button.setText(self.toggle_button.text().replace("▼" if is_visible else "▲", "▲" if is_visible else "▼"))

class ConfigurationWidget(QWidget):
    """Advanced configuration widget for trading parameters"""

    config_changed = Signal()

    def __init__(self, config: TradingConfig):
        super().__init__()
        self.config = config
        self.init_ui()

    def init_ui(self):
        """Initialize the configuration UI"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)

        # Title
        title = QLabel("⚙️ Pengaturan Trading Advanced")
        title.setFont(QFont("Inter", 20, QFont.Bold))
        title.setStyleSheet(f"color: {COLOR_TEXT_HEADLINE.name()}; margin-bottom: 10px;")
        title.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title)

        # Create tab widget for organized settings
        self.tab_widget = QTabWidget()
        self.tab_widget.setStyleSheet(f"""
            QTabWidget::pane {{
                border: 1px solid {COLOR_PROGRESS_BG_APP.name()};
                border-radius: 8px;
                background-color: {COLOR_CARD_BG.name()};
            }}
            QTabBar::tab {{
                background-color: {COLOR_PROGRESS_BG_APP.name()};
                color: {COLOR_TEXT_BODY.name()};
                padding: 8px 16px;
                margin-right: 2px;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
            }}
            QTabBar::tab:selected {{
                background-color: {COLOR_PRIMARY_ACCENT_APP.name()};
                color: white;
            }}
            QTabBar::tab:hover {{
                background-color: {COLOR_PRIMARY_ACCENT_APP.lighter(150).name()};
            }}
        """)

        # Create tabs
        self.create_general_tab()
        self.create_performance_tab()
        self.create_thresholds_tab()
        self.create_indicators_tab()
        self.create_filtering_tab()
        self.create_advanced_tab()

        main_layout.addWidget(self.tab_widget)

        # Action buttons
        self.create_action_buttons(main_layout)

    def create_general_tab(self):
        """Create general settings tab"""
        tab = QWidget()
        layout = QFormLayout(tab)
        layout.setSpacing(15)

        # Timeframes section
        timeframes_group = QGroupBox("📊 Pengaturan Timeframe")
        timeframes_group.setStyleSheet(f"""
            QGroupBox {{
                font-weight: bold;
                border: 2px solid {COLOR_PROGRESS_BG_APP.name()};
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }}
        """)
        timeframes_layout = QFormLayout(timeframes_group)

        # Timeframes to analyze
        self.timeframes_widget = QWidget()
        timeframes_h_layout = QHBoxLayout(self.timeframes_widget)
        timeframes_h_layout.setContentsMargins(0, 0, 0, 0)

        self.timeframe_checkboxes = {}
        available_timeframes = ['1m', '5m', '15m', '30m', '1h', '4h', '1d']
        for tf in available_timeframes:
            cb = QCheckBox(tf)
            cb.setChecked(tf in self.config.TIMEFRAMES_TO_ANALYZE)
            cb.stateChanged.connect(self.on_timeframe_changed)
            self.timeframe_checkboxes[tf] = cb
            timeframes_h_layout.addWidget(cb)

        timeframes_layout.addRow("Timeframes untuk Analisis:", self.timeframes_widget)

        # Reference timeframe
        self.ref_timeframe_combo = QComboBox()
        self.ref_timeframe_combo.addItems(['1m', '5m', '15m', '30m', '1h', '4h', '1d'])
        self.ref_timeframe_combo.setCurrentText(self.config.REFERENCE_TIMEFRAME_FOR_PRICE)
        self.ref_timeframe_combo.currentTextChanged.connect(self.on_config_changed)
        timeframes_layout.addRow("Timeframe Referensi Harga:", self.ref_timeframe_combo)

        layout.addRow(timeframes_group)

        # Display settings
        display_group = QGroupBox("🖥️ Pengaturan Tampilan")
        display_group.setStyleSheet(timeframes_group.styleSheet())
        display_layout = QFormLayout(display_group)

        self.max_pairs_spin = QSpinBox()
        self.max_pairs_spin.setRange(10, 200)
        self.max_pairs_spin.setValue(self.config.MAX_PAIRS_DISPLAY)
        self.max_pairs_spin.valueChanged.connect(self.on_config_changed)
        self.max_pairs_spin.setToolTip("Jumlah maksimum pasangan yang ditampilkan dalam hasil")
        display_layout.addRow("Max Pairs Display:", self.max_pairs_spin)

        self.candle_limit_spin = QSpinBox()
        self.candle_limit_spin.setRange(100, 2000)
        self.candle_limit_spin.setValue(self.config.CANDLE_LIMIT_PER_TIMEFRAME)
        self.candle_limit_spin.valueChanged.connect(self.on_config_changed)
        self.candle_limit_spin.setToolTip("Jumlah candle yang diambil per timeframe untuk analisis")
        display_layout.addRow("Candle Limit per TF:", self.candle_limit_spin)

        layout.addRow(display_group)

        # Mode settings
        mode_group = QGroupBox("🎯 Mode Operasi")
        mode_group.setStyleSheet(timeframes_group.styleSheet())
        mode_layout = QFormLayout(mode_group)

        self.discovery_mode_cb = QCheckBox("Discovery Mode")
        self.discovery_mode_cb.setChecked(self.config.DISCOVERY_MODE)
        self.discovery_mode_cb.stateChanged.connect(self.on_config_changed)
        self.discovery_mode_cb.setToolTip("Mode pencarian sinyal dengan threshold yang lebih permisif")
        mode_layout.addRow(self.discovery_mode_cb)

        self.emergency_mode_cb = QCheckBox("Emergency Mode")
        self.emergency_mode_cb.setChecked(self.config.EMERGENCY_MODE)
        self.emergency_mode_cb.stateChanged.connect(self.on_config_changed)
        self.emergency_mode_cb.setToolTip("Mode darurat dengan deteksi sinyal maksimum")
        mode_layout.addRow(self.emergency_mode_cb)

        self.debug_filtering_cb = QCheckBox("Debug Filtering")
        self.debug_filtering_cb.setChecked(self.config.DEBUG_FILTERING)
        self.debug_filtering_cb.stateChanged.connect(self.on_config_changed)
        self.debug_filtering_cb.setToolTip("Aktifkan logging debug untuk proses filtering")
        mode_layout.addRow(self.debug_filtering_cb)

        self.demo_mode_cb = QCheckBox("Demo Mode")
        self.demo_mode_cb.setChecked(self.config.DEMO_MODE)
        self.demo_mode_cb.stateChanged.connect(self.on_config_changed)
        self.demo_mode_cb.setToolTip("Mode demo dengan data terbatas untuk testing")
        mode_layout.addRow(self.demo_mode_cb)

        layout.addRow(mode_group)

        self.tab_widget.addTab(tab, "🏠 Umum")

    def create_performance_tab(self):
        """Create performance settings tab"""
        tab = QWidget()
        layout = QFormLayout(tab)
        layout.setSpacing(15)

        # Threading settings
        threading_group = QGroupBox("🚀 Pengaturan Threading & Performance")
        threading_group.setStyleSheet(f"""
            QGroupBox {{
                font-weight: bold;
                border: 2px solid {COLOR_PROGRESS_BG_APP.name()};
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }}
        """)
        threading_layout = QFormLayout(threading_group)

        self.max_workers_spin = QSpinBox()
        self.max_workers_spin.setRange(1, 32)
        self.max_workers_spin.setValue(self.config.MAX_WORKERS_ANALYSIS)
        self.max_workers_spin.valueChanged.connect(self.on_config_changed)
        self.max_workers_spin.setToolTip("Jumlah maksimum worker threads untuk analisis paralel")
        threading_layout.addRow("Max Workers Analysis:", self.max_workers_spin)

        self.max_concurrent_spin = QSpinBox()
        self.max_concurrent_spin.setRange(10, 200)
        self.max_concurrent_spin.setValue(self.config.MAX_CONCURRENT_DOWNLOADS)
        self.max_concurrent_spin.valueChanged.connect(self.on_config_changed)
        self.max_concurrent_spin.setToolTip("Jumlah maksimum download concurrent")
        threading_layout.addRow("Max Concurrent Downloads:", self.max_concurrent_spin)

        self.download_batch_spin = QSpinBox()
        self.download_batch_spin.setRange(10, 500)
        self.download_batch_spin.setValue(self.config.DOWNLOAD_BATCH_SIZE)
        self.download_batch_spin.valueChanged.connect(self.on_config_changed)
        self.download_batch_spin.setToolTip("Ukuran batch untuk proses download")
        threading_layout.addRow("Download Batch Size:", self.download_batch_spin)

        self.analysis_batch_spin = QSpinBox()
        self.analysis_batch_spin.setRange(5, 100)
        self.analysis_batch_spin.setValue(self.config.ANALYSIS_BATCH_SIZE)
        self.analysis_batch_spin.valueChanged.connect(self.on_config_changed)
        self.analysis_batch_spin.setToolTip("Ukuran batch untuk proses analisis")
        threading_layout.addRow("Analysis Batch Size:", self.analysis_batch_spin)

        layout.addRow(threading_group)

        # API settings
        api_group = QGroupBox("🌐 Pengaturan API")
        api_group.setStyleSheet(threading_group.styleSheet())
        api_layout = QFormLayout(api_group)

        self.api_delay_spin = QSpinBox()
        self.api_delay_spin.setRange(0, 1000)
        self.api_delay_spin.setValue(self.config.API_REQUEST_DELAY_MS)
        self.api_delay_spin.valueChanged.connect(self.on_config_changed)
        self.api_delay_spin.setToolTip("Delay antar request API dalam milliseconds")
        api_layout.addRow("API Request Delay (ms):", self.api_delay_spin)

        self.api_retry_delay_spin = QSpinBox()
        self.api_retry_delay_spin.setRange(1, 30)
        self.api_retry_delay_spin.setValue(self.config.API_RETRY_DELAY_S)
        self.api_retry_delay_spin.valueChanged.connect(self.on_config_changed)
        self.api_retry_delay_spin.setToolTip("Delay untuk retry API dalam seconds")
        api_layout.addRow("API Retry Delay (s):", self.api_retry_delay_spin)

        self.max_retries_spin = QSpinBox()
        self.max_retries_spin.setRange(1, 10)
        self.max_retries_spin.setValue(self.config.MAX_API_RETRIES)
        self.max_retries_spin.valueChanged.connect(self.on_config_changed)
        self.max_retries_spin.setToolTip("Jumlah maksimum retry untuk API calls")
        api_layout.addRow("Max API Retries:", self.max_retries_spin)

        self.request_timeout_spin = QSpinBox()
        self.request_timeout_spin.setRange(5, 60)
        self.request_timeout_spin.setValue(self.config.REQUEST_TIMEOUT_S)
        self.request_timeout_spin.valueChanged.connect(self.on_config_changed)
        self.request_timeout_spin.setToolTip("Timeout untuk request API dalam seconds")
        api_layout.addRow("Request Timeout (s):", self.request_timeout_spin)

        self.async_timeout_spin = QSpinBox()
        self.async_timeout_spin.setRange(10, 120)
        self.async_timeout_spin.setValue(self.config.ASYNC_TIMEOUT_SECONDS)
        self.async_timeout_spin.valueChanged.connect(self.on_config_changed)
        self.async_timeout_spin.setToolTip("Timeout untuk operasi async dalam seconds")
        api_layout.addRow("Async Timeout (s):", self.async_timeout_spin)

        self.connection_pool_spin = QSpinBox()
        self.connection_pool_spin.setRange(50, 500)
        self.connection_pool_spin.setValue(self.config.CONNECTION_POOL_SIZE)
        self.connection_pool_spin.valueChanged.connect(self.on_config_changed)
        self.connection_pool_spin.setToolTip("Ukuran connection pool untuk aiohttp")
        api_layout.addRow("Connection Pool Size:", self.connection_pool_spin)

        layout.addRow(api_group)

        self.tab_widget.addTab(tab, "⚡ Performance")

    def create_thresholds_tab(self):
        """Create thresholds and confidence settings tab"""
        tab = QWidget()
        layout = QFormLayout(tab)
        layout.setSpacing(15)

        # Confidence settings
        confidence_group = QGroupBox("🎯 Pengaturan Confidence & Threshold")
        confidence_group.setStyleSheet(f"""
            QGroupBox {{
                font-weight: bold;
                border: 2px solid {COLOR_PROGRESS_BG_APP.name()};
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }}
        """)
        confidence_layout = QFormLayout(confidence_group)

        self.min_confidence_spin = QDoubleSpinBox()
        self.min_confidence_spin.setRange(0.1, 1.0)
        self.min_confidence_spin.setSingleStep(0.05)
        self.min_confidence_spin.setDecimals(2)
        self.min_confidence_spin.setValue(self.config.MIN_CONFIDENCE_THRESHOLD)
        self.min_confidence_spin.valueChanged.connect(self.on_config_changed)
        self.min_confidence_spin.setToolTip("Threshold minimum confidence untuk sinyal (0.1-1.0)")
        confidence_layout.addRow("Min Confidence Threshold:", self.min_confidence_spin)

        self.advanced_signal_spin = QDoubleSpinBox()
        self.advanced_signal_spin.setRange(0.5, 2.0)
        self.advanced_signal_spin.setSingleStep(0.05)
        self.advanced_signal_spin.setDecimals(2)
        self.advanced_signal_spin.setValue(self.config.ADVANCED_SIGNAL_THRESHOLD)
        self.advanced_signal_spin.valueChanged.connect(self.on_config_changed)
        self.advanced_signal_spin.setToolTip("Threshold untuk sinyal berkualitas tinggi")
        confidence_layout.addRow("Advanced Signal Threshold:", self.advanced_signal_spin)

        self.confluence_multiplier_spin = QDoubleSpinBox()
        self.confluence_multiplier_spin.setRange(1.0, 3.0)
        self.confluence_multiplier_spin.setSingleStep(0.1)
        self.confluence_multiplier_spin.setDecimals(1)
        self.confluence_multiplier_spin.setValue(self.config.CONFLUENCE_MULTIPLIER)
        self.confluence_multiplier_spin.valueChanged.connect(self.on_config_changed)
        self.confluence_multiplier_spin.setToolTip("Multiplier untuk sinyal yang confluent")
        confidence_layout.addRow("Confluence Multiplier:", self.confluence_multiplier_spin)

        self.volume_spike_spin = QDoubleSpinBox()
        self.volume_spike_spin.setRange(1.5, 5.0)
        self.volume_spike_spin.setSingleStep(0.1)
        self.volume_spike_spin.setDecimals(1)
        self.volume_spike_spin.setValue(self.config.VOLUME_SPIKE_THRESHOLD)
        self.volume_spike_spin.valueChanged.connect(self.on_config_changed)
        self.volume_spike_spin.setToolTip("Threshold untuk deteksi volume spike")
        confidence_layout.addRow("Volume Spike Threshold:", self.volume_spike_spin)

        layout.addRow(confidence_group)

        # Analysis parameters
        analysis_group = QGroupBox("📈 Parameter Analisis")
        analysis_group.setStyleSheet(confidence_group.styleSheet())
        analysis_layout = QFormLayout(analysis_group)

        self.momentum_lookback_spin = QSpinBox()
        self.momentum_lookback_spin.setRange(10, 50)
        self.momentum_lookback_spin.setValue(self.config.MOMENTUM_LOOKBACK)
        self.momentum_lookback_spin.valueChanged.connect(self.on_config_changed)
        self.momentum_lookback_spin.setToolTip("Periode lookback untuk analisis momentum")
        analysis_layout.addRow("Momentum Lookback:", self.momentum_lookback_spin)

        self.trend_strength_spin = QSpinBox()
        self.trend_strength_spin.setRange(20, 100)
        self.trend_strength_spin.setValue(self.config.TREND_STRENGTH_PERIODS)
        self.trend_strength_spin.valueChanged.connect(self.on_config_changed)
        self.trend_strength_spin.setToolTip("Periode untuk kalkulasi kekuatan trend")
        analysis_layout.addRow("Trend Strength Periods:", self.trend_strength_spin)

        layout.addRow(analysis_group)

        self.tab_widget.addTab(tab, "🎯 Thresholds")

    def create_indicators_tab(self):
        """Create indicator weights configuration tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setSpacing(15)

        # Title
        title = QLabel("⚖️ Pengaturan Bobot Indikator")
        title.setFont(QFont("Inter", 14, QFont.Bold))
        title.setStyleSheet(f"color: {COLOR_TEXT_HEADLINE.name()};")
        layout.addWidget(title)

        # Scroll area for indicator weights
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        scroll.setStyleSheet("QScrollArea { border: none; }")

        scroll_widget = QWidget()
        scroll_layout = QFormLayout(scroll_widget)
        scroll_layout.setSpacing(10)

        self.indicator_weight_spins = {}

        # Group indicators by category
        indicator_categories = {
            "Smart Money Concepts": [
                ('market_structure_trend', 'Market Structure Trend'),
                ('smc_zone_reaction', 'SMC Zone Reaction'),
                ('liquidity_event', 'Liquidity Event'),
            ],
            "Volume & Confluence": [
                ('volume_confirmation', 'Volume Confirmation'),
                ('multi_tf_alignment', 'Multi-TF Alignment'),
                ('volume_profile', 'Volume Profile'),
            ],
            "Technical Analysis": [
                ('momentum_divergence_strong', 'Momentum Divergence Strong'),
                ('htf_trend_clarity', 'HTF Trend Clarity'),
                ('general_ta_score', 'General TA Score'),
                ('momentum_oscillators', 'Momentum Oscillators'),
            ],
            "Price Action & Patterns": [
                ('price_action_patterns', 'Price Action Patterns'),
                ('volatility_analysis', 'Volatility Analysis'),
                ('fibonacci_levels', 'Fibonacci Levels'),
                ('support_resistance', 'Support/Resistance'),
                ('trend_strength', 'Trend Strength'),
                ('market_sentiment', 'Market Sentiment'),
            ]
        }

        for category, indicators in indicator_categories.items():
            # Category header
            category_label = QLabel(f"📊 {category}")
            category_label.setFont(QFont("Inter", 12, QFont.Bold))
            category_label.setStyleSheet(f"color: {COLOR_PRIMARY_ACCENT_APP.name()}; margin-top: 15px; margin-bottom: 5px;")
            scroll_layout.addRow(category_label)

            for key, display_name in indicators:
                if key in self.config.INDICATOR_WEIGHTS:
                    spin = QDoubleSpinBox()
                    spin.setRange(0.0, 10.0)
                    spin.setSingleStep(0.1)
                    spin.setDecimals(1)
                    spin.setValue(self.config.INDICATOR_WEIGHTS[key])
                    spin.valueChanged.connect(self.on_indicator_weight_changed)
                    spin.setToolTip(f"Bobot untuk {display_name} (0.0-10.0)")

                    self.indicator_weight_spins[key] = spin
                    scroll_layout.addRow(f"{display_name}:", spin)

        scroll.setWidget(scroll_widget)
        layout.addWidget(scroll)

        # Reset weights button
        reset_weights_btn = QPushButton("🔄 Reset ke Default")
        reset_weights_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {COLOR_WARNING_APP.name()};
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 8px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {COLOR_WARNING_APP.darker(110).name()};
            }}
        """)
        reset_weights_btn.clicked.connect(self.reset_indicator_weights)
        layout.addWidget(reset_weights_btn)

        self.tab_widget.addTab(tab, "⚖️ Indikator")

    def create_filtering_tab(self):
        """Create filtering settings tab"""
        tab = QWidget()
        layout = QFormLayout(tab)
        layout.setSpacing(15)

        # Stablecoin filtering
        stablecoin_group = QGroupBox("💰 Filter Stablecoin")
        stablecoin_group.setStyleSheet(f"""
            QGroupBox {{
                font-weight: bold;
                border: 2px solid {COLOR_PROGRESS_BG_APP.name()};
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }}
        """)
        stablecoin_layout = QFormLayout(stablecoin_group)

        self.enable_stablecoin_cb = QCheckBox("Enable Stablecoin Filtering")
        self.enable_stablecoin_cb.setChecked(self.config.ENABLE_STABLECOIN_FILTERING)
        self.enable_stablecoin_cb.stateChanged.connect(self.on_config_changed)
        self.enable_stablecoin_cb.setToolTip("Aktifkan filtering untuk menghilangkan pasangan stablecoin")
        stablecoin_layout.addRow(self.enable_stablecoin_cb)

        layout.addRow(stablecoin_group)

        # Volume filtering
        volume_group = QGroupBox("📊 Filter Volume & Volatilitas")
        volume_group.setStyleSheet(stablecoin_group.styleSheet())
        volume_layout = QFormLayout(volume_group)

        self.min_volume_spin = QSpinBox()
        self.min_volume_spin.setRange(1000, 10000000)
        self.min_volume_spin.setSingleStep(10000)
        self.min_volume_spin.setValue(self.config.MIN_VOLUME_24H_USD)
        self.min_volume_spin.valueChanged.connect(self.on_config_changed)
        self.min_volume_spin.setToolTip("Volume minimum 24 jam dalam USD")
        volume_layout.addRow("Min Volume 24H (USD):", self.min_volume_spin)

        self.min_atr_spin = QDoubleSpinBox()
        self.min_atr_spin.setRange(0.001, 10.0)
        self.min_atr_spin.setSingleStep(0.01)
        self.min_atr_spin.setDecimals(3)
        self.min_atr_spin.setValue(self.config.MIN_ATR_PERCENTAGE)
        self.min_atr_spin.valueChanged.connect(self.on_config_changed)
        self.min_atr_spin.setToolTip("Persentase ATR minimum untuk volatilitas")
        volume_layout.addRow("Min ATR Percentage:", self.min_atr_spin)

        self.max_atr_spin = QDoubleSpinBox()
        self.max_atr_spin.setRange(1.0, 100.0)
        self.max_atr_spin.setSingleStep(1.0)
        self.max_atr_spin.setDecimals(1)
        self.max_atr_spin.setValue(self.config.MAX_ATR_PERCENTAGE)
        self.max_atr_spin.valueChanged.connect(self.on_config_changed)
        self.max_atr_spin.setToolTip("Persentase ATR maksimum untuk volatilitas")
        volume_layout.addRow("Max ATR Percentage:", self.max_atr_spin)

        self.min_rrr_spin = QDoubleSpinBox()
        self.min_rrr_spin.setRange(0.1, 5.0)
        self.min_rrr_spin.setSingleStep(0.1)
        self.min_rrr_spin.setDecimals(1)
        self.min_rrr_spin.setValue(self.config.MIN_RRR_RATIO)
        self.min_rrr_spin.valueChanged.connect(self.on_config_changed)
        self.min_rrr_spin.setToolTip("Rasio Risk-Reward minimum")
        volume_layout.addRow("Min RRR Ratio:", self.min_rrr_spin)

        self.volume_confirmation_spin = QDoubleSpinBox()
        self.volume_confirmation_spin.setRange(1.0, 3.0)
        self.volume_confirmation_spin.setSingleStep(0.01)
        self.volume_confirmation_spin.setDecimals(2)
        self.volume_confirmation_spin.setValue(self.config.VOLUME_CONFIRMATION_MULTIPLIER)
        self.volume_confirmation_spin.valueChanged.connect(self.on_config_changed)
        self.volume_confirmation_spin.setToolTip("Multiplier untuk konfirmasi volume")
        volume_layout.addRow("Volume Confirmation Multiplier:", self.volume_confirmation_spin)

        layout.addRow(volume_group)

        self.tab_widget.addTab(tab, "🔍 Filtering")

    def create_advanced_tab(self):
        """Create advanced technical indicator settings tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setSpacing(15)

        # Title
        title = QLabel("🔧 Pengaturan Indikator Teknikal per Timeframe")
        title.setFont(QFont("Inter", 14, QFont.Bold))
        title.setStyleSheet(f"color: {COLOR_TEXT_HEADLINE.name()};")
        layout.addWidget(title)

        # Timeframe tabs for indicator settings
        self.indicator_tab_widget = QTabWidget()
        self.indicator_tab_widget.setStyleSheet(f"""
            QTabWidget::pane {{
                border: 1px solid {COLOR_PROGRESS_BG_APP.name()};
                border-radius: 8px;
                background-color: {COLOR_CARD_BG.name()};
            }}
            QTabBar::tab {{
                background-color: {COLOR_PROGRESS_BG_APP.name()};
                color: {COLOR_TEXT_BODY.name()};
                padding: 6px 12px;
                margin-right: 1px;
                border-top-left-radius: 6px;
                border-top-right-radius: 6px;
            }}
            QTabBar::tab:selected {{
                background-color: {COLOR_SECONDARY_ACCENT_APP.name()};
                color: white;
            }}
        """)

        self.indicator_settings_widgets = {}

        # Create tabs for each timeframe
        for tf in ['4h', '1h', '30m', '15m', '5m']:
            tf_tab = QWidget()
            tf_layout = QFormLayout(tf_tab)
            tf_layout.setSpacing(10)

            # Get settings for this timeframe
            tf_settings = self.config.INDICATOR_SETTINGS.get(tf, {})

            # Create widgets for each setting
            tf_widgets = {}

            # RSI settings
            rsi_group = QGroupBox("📊 RSI Settings")
            rsi_group.setStyleSheet(f"""
                QGroupBox {{
                    font-weight: bold;
                    border: 1px solid {COLOR_PROGRESS_BG_APP.name()};
                    border-radius: 6px;
                    margin-top: 8px;
                    padding-top: 8px;
                }}
                QGroupBox::title {{
                    subcontrol-origin: margin;
                    left: 8px;
                    padding: 0 4px 0 4px;
                }}
            """)
            rsi_layout = QFormLayout(rsi_group)

            # RSI Period
            rsi_period_spin = QSpinBox()
            rsi_period_spin.setRange(5, 50)
            rsi_period_spin.setValue(tf_settings.get('rsi_period', 14))
            rsi_period_spin.valueChanged.connect(lambda v, tf=tf, key='rsi_period': self.on_indicator_setting_changed(tf, key, v))
            tf_widgets['rsi_period'] = rsi_period_spin
            rsi_layout.addRow("RSI Period:", rsi_period_spin)

            # RSI Oversold
            rsi_oversold_spin = QSpinBox()
            rsi_oversold_spin.setRange(10, 40)
            rsi_oversold_spin.setValue(tf_settings.get('rsi_oversold', 30))
            rsi_oversold_spin.valueChanged.connect(lambda v, tf=tf, key='rsi_oversold': self.on_indicator_setting_changed(tf, key, v))
            tf_widgets['rsi_oversold'] = rsi_oversold_spin
            rsi_layout.addRow("RSI Oversold:", rsi_oversold_spin)

            # RSI Overbought
            rsi_overbought_spin = QSpinBox()
            rsi_overbought_spin.setRange(60, 90)
            rsi_overbought_spin.setValue(tf_settings.get('rsi_overbought', 70))
            rsi_overbought_spin.valueChanged.connect(lambda v, tf=tf, key='rsi_overbought': self.on_indicator_setting_changed(tf, key, v))
            tf_widgets['rsi_overbought'] = rsi_overbought_spin
            rsi_layout.addRow("RSI Overbought:", rsi_overbought_spin)

            tf_layout.addRow(rsi_group)

            # MACD settings
            macd_group = QGroupBox("📈 MACD Settings")
            macd_group.setStyleSheet(rsi_group.styleSheet())
            macd_layout = QFormLayout(macd_group)

            # MACD Fast
            macd_fast_spin = QSpinBox()
            macd_fast_spin.setRange(5, 20)
            macd_fast_spin.setValue(tf_settings.get('macd_fast', 12))
            macd_fast_spin.valueChanged.connect(lambda v, tf=tf, key='macd_fast': self.on_indicator_setting_changed(tf, key, v))
            tf_widgets['macd_fast'] = macd_fast_spin
            macd_layout.addRow("MACD Fast:", macd_fast_spin)

            # MACD Slow
            macd_slow_spin = QSpinBox()
            macd_slow_spin.setRange(15, 40)
            macd_slow_spin.setValue(tf_settings.get('macd_slow', 26))
            macd_slow_spin.valueChanged.connect(lambda v, tf=tf, key='macd_slow': self.on_indicator_setting_changed(tf, key, v))
            tf_widgets['macd_slow'] = macd_slow_spin
            macd_layout.addRow("MACD Slow:", macd_slow_spin)

            # MACD Signal
            macd_signal_spin = QSpinBox()
            macd_signal_spin.setRange(5, 15)
            macd_signal_spin.setValue(tf_settings.get('macd_signal', 9))
            macd_signal_spin.valueChanged.connect(lambda v, tf=tf, key='macd_signal': self.on_indicator_setting_changed(tf, key, v))
            tf_widgets['macd_signal'] = macd_signal_spin
            macd_layout.addRow("MACD Signal:", macd_signal_spin)

            tf_layout.addRow(macd_group)

            # EMA settings
            ema_group = QGroupBox("📉 EMA Settings")
            ema_group.setStyleSheet(rsi_group.styleSheet())
            ema_layout = QFormLayout(ema_group)

            # EMA Short
            ema_short_spin = QSpinBox()
            ema_short_spin.setRange(5, 50)
            ema_short_spin.setValue(tf_settings.get('ema_short', 21))
            ema_short_spin.valueChanged.connect(lambda v, tf=tf, key='ema_short': self.on_indicator_setting_changed(tf, key, v))
            tf_widgets['ema_short'] = ema_short_spin
            ema_layout.addRow("EMA Short:", ema_short_spin)

            # EMA Medium
            ema_medium_spin = QSpinBox()
            ema_medium_spin.setRange(20, 100)
            ema_medium_spin.setValue(tf_settings.get('ema_medium', 55))
            ema_medium_spin.valueChanged.connect(lambda v, tf=tf, key='ema_medium': self.on_indicator_setting_changed(tf, key, v))
            tf_widgets['ema_medium'] = ema_medium_spin
            ema_layout.addRow("EMA Medium:", ema_medium_spin)

            # EMA Long
            ema_long_spin = QSpinBox()
            ema_long_spin.setRange(50, 300)
            ema_long_spin.setValue(tf_settings.get('ema_long', 100))
            ema_long_spin.valueChanged.connect(lambda v, tf=tf, key='ema_long': self.on_indicator_setting_changed(tf, key, v))
            tf_widgets['ema_long'] = ema_long_spin
            ema_layout.addRow("EMA Long:", ema_long_spin)

            tf_layout.addRow(ema_group)

            # Bollinger Bands settings
            bb_group = QGroupBox("📊 Bollinger Bands Settings")
            bb_group.setStyleSheet(rsi_group.styleSheet())
            bb_layout = QFormLayout(bb_group)

            # BB Period
            bb_period_spin = QSpinBox()
            bb_period_spin.setRange(10, 50)
            bb_period_spin.setValue(tf_settings.get('bb_period', 20))
            bb_period_spin.valueChanged.connect(lambda v, tf=tf, key='bb_period': self.on_indicator_setting_changed(tf, key, v))
            tf_widgets['bb_period'] = bb_period_spin
            bb_layout.addRow("BB Period:", bb_period_spin)

            # BB Std Dev
            bb_std_spin = QDoubleSpinBox()
            bb_std_spin.setRange(1.0, 3.0)
            bb_std_spin.setSingleStep(0.1)
            bb_std_spin.setDecimals(1)
            bb_std_spin.setValue(tf_settings.get('bb_std_dev', 2.0))
            bb_std_spin.valueChanged.connect(lambda v, tf=tf, key='bb_std_dev': self.on_indicator_setting_changed(tf, key, v))
            tf_widgets['bb_std_dev'] = bb_std_spin
            bb_layout.addRow("BB Std Dev:", bb_std_spin)

            tf_layout.addRow(bb_group)

            # Other indicators
            other_group = QGroupBox("🔧 Other Indicators")
            other_group.setStyleSheet(rsi_group.styleSheet())
            other_layout = QFormLayout(other_group)

            # ATR Period
            atr_period_spin = QSpinBox()
            atr_period_spin.setRange(5, 30)
            atr_period_spin.setValue(tf_settings.get('atr_period', 14))
            atr_period_spin.valueChanged.connect(lambda v, tf=tf, key='atr_period': self.on_indicator_setting_changed(tf, key, v))
            tf_widgets['atr_period'] = atr_period_spin
            other_layout.addRow("ATR Period:", atr_period_spin)

            # ADX Period
            adx_period_spin = QSpinBox()
            adx_period_spin.setRange(5, 30)
            adx_period_spin.setValue(tf_settings.get('adx_period', 14))
            adx_period_spin.valueChanged.connect(lambda v, tf=tf, key='adx_period': self.on_indicator_setting_changed(tf, key, v))
            tf_widgets['adx_period'] = adx_period_spin
            other_layout.addRow("ADX Period:", adx_period_spin)

            tf_layout.addRow(other_group)

            self.indicator_settings_widgets[tf] = tf_widgets
            self.indicator_tab_widget.addTab(tf_tab, tf.upper())

        layout.addWidget(self.indicator_tab_widget)

        # Reset button for indicator settings
        reset_indicators_btn = QPushButton("🔄 Reset Indikator ke Default")
        reset_indicators_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {COLOR_WARNING_APP.name()};
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 8px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {COLOR_WARNING_APP.darker(110).name()};
            }}
        """)
        reset_indicators_btn.clicked.connect(self.reset_indicator_settings)
        layout.addWidget(reset_indicators_btn)

        self.tab_widget.addTab(tab, "🔧 Advanced")

    def create_action_buttons(self, layout):
        """Create action buttons for save/load/reset configuration"""
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)

        # Save configuration
        save_btn = QPushButton("💾 Simpan Konfigurasi")
        save_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {COLOR_SUCCESS_APP.name()};
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 12px;
            }}
            QPushButton:hover {{
                background-color: {COLOR_SUCCESS_APP.darker(110).name()};
            }}
        """)
        save_btn.clicked.connect(self.save_configuration)
        buttons_layout.addWidget(save_btn)

        # Load configuration
        load_btn = QPushButton("📂 Muat Konfigurasi")
        load_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {COLOR_SECONDARY_ACCENT_APP.name()};
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 12px;
            }}
            QPushButton:hover {{
                background-color: {COLOR_SECONDARY_ACCENT_APP.darker(110).name()};
            }}
        """)
        load_btn.clicked.connect(self.load_configuration)
        buttons_layout.addWidget(load_btn)

        # Reset to defaults
        reset_btn = QPushButton("🔄 Reset ke Default")
        reset_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {COLOR_WARNING_APP.name()};
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 12px;
            }}
            QPushButton:hover {{
                background-color: {COLOR_WARNING_APP.darker(110).name()};
            }}
        """)
        reset_btn.clicked.connect(self.reset_all_configuration)
        buttons_layout.addWidget(reset_btn)

        # Apply configuration
        apply_btn = QPushButton("✅ Terapkan Konfigurasi")
        apply_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {COLOR_PRIMARY_ACCENT_APP.name()};
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 12px;
            }}
            QPushButton:hover {{
                background-color: {COLOR_PRIMARY_ACCENT_APP.darker(110).name()};
            }}
        """)
        apply_btn.clicked.connect(self.apply_configuration)
        buttons_layout.addWidget(apply_btn)

        layout.addLayout(buttons_layout)

    def on_timeframe_changed(self):
        """Handle timeframe checkbox changes"""
        selected_timeframes = []
        for tf, cb in self.timeframe_checkboxes.items():
            if cb.isChecked():
                selected_timeframes.append(tf)

        # Ensure at least one timeframe is selected
        if not selected_timeframes:
            # Re-check the first one
            first_tf = list(self.timeframe_checkboxes.keys())[0]
            self.timeframe_checkboxes[first_tf].setChecked(True)
            selected_timeframes = [first_tf]

        self.config.TIMEFRAMES_TO_ANALYZE = selected_timeframes
        self.config_changed.emit()

    def on_config_changed(self):
        """Handle general configuration changes"""
        # Update config from UI elements
        self.config.REFERENCE_TIMEFRAME_FOR_PRICE = self.ref_timeframe_combo.currentText()
        self.config.MAX_PAIRS_DISPLAY = self.max_pairs_spin.value()
        self.config.CANDLE_LIMIT_PER_TIMEFRAME = self.candle_limit_spin.value()

        # Mode settings
        self.config.DISCOVERY_MODE = self.discovery_mode_cb.isChecked()
        self.config.EMERGENCY_MODE = self.emergency_mode_cb.isChecked()
        self.config.DEBUG_FILTERING = self.debug_filtering_cb.isChecked()
        self.config.DEMO_MODE = self.demo_mode_cb.isChecked()

        # Performance settings
        if hasattr(self, 'max_workers_spin'):
            self.config.MAX_WORKERS_ANALYSIS = self.max_workers_spin.value()
            self.config.MAX_CONCURRENT_DOWNLOADS = self.max_concurrent_spin.value()
            self.config.DOWNLOAD_BATCH_SIZE = self.download_batch_spin.value()
            self.config.ANALYSIS_BATCH_SIZE = self.analysis_batch_spin.value()
            self.config.API_REQUEST_DELAY_MS = self.api_delay_spin.value()
            self.config.API_RETRY_DELAY_S = self.api_retry_delay_spin.value()
            self.config.MAX_API_RETRIES = self.max_retries_spin.value()
            self.config.REQUEST_TIMEOUT_S = self.request_timeout_spin.value()
            self.config.ASYNC_TIMEOUT_SECONDS = self.async_timeout_spin.value()
            self.config.CONNECTION_POOL_SIZE = self.connection_pool_spin.value()

        # Threshold settings
        if hasattr(self, 'min_confidence_spin'):
            self.config.MIN_CONFIDENCE_THRESHOLD = self.min_confidence_spin.value()
            self.config.ADVANCED_SIGNAL_THRESHOLD = self.advanced_signal_spin.value()
            self.config.CONFLUENCE_MULTIPLIER = self.confluence_multiplier_spin.value()
            self.config.VOLUME_SPIKE_THRESHOLD = self.volume_spike_spin.value()
            self.config.MOMENTUM_LOOKBACK = self.momentum_lookback_spin.value()
            self.config.TREND_STRENGTH_PERIODS = self.trend_strength_spin.value()

        # Filtering settings
        if hasattr(self, 'enable_stablecoin_cb'):
            self.config.ENABLE_STABLECOIN_FILTERING = self.enable_stablecoin_cb.isChecked()
            self.config.MIN_VOLUME_24H_USD = self.min_volume_spin.value()
            self.config.MIN_ATR_PERCENTAGE = self.min_atr_spin.value()
            self.config.MAX_ATR_PERCENTAGE = self.max_atr_spin.value()
            self.config.MIN_RRR_RATIO = self.min_rrr_spin.value()
            self.config.VOLUME_CONFIRMATION_MULTIPLIER = self.volume_confirmation_spin.value()

        self.config_changed.emit()

    def on_indicator_weight_changed(self):
        """Handle indicator weight changes"""
        for key, spin in self.indicator_weight_spins.items():
            self.config.INDICATOR_WEIGHTS[key] = spin.value()
        self.config_changed.emit()

    def on_indicator_setting_changed(self, timeframe, setting_key, value):
        """Handle indicator setting changes"""
        if timeframe not in self.config.INDICATOR_SETTINGS:
            self.config.INDICATOR_SETTINGS[timeframe] = {}
        self.config.INDICATOR_SETTINGS[timeframe][setting_key] = value
        self.config_changed.emit()

    def reset_indicator_weights(self):
        """Reset indicator weights to default values"""
        reply = QMessageBox.question(
            self,
            "Reset Bobot Indikator",
            "Apakah Anda yakin ingin mereset semua bobot indikator ke nilai default?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # Reset to default weights
            default_weights = {
                'market_structure_trend': 4.5,
                'smc_zone_reaction': 4.0,
                'liquidity_event': 3.0,
                'volume_confirmation': 3.5,
                'multi_tf_alignment': 5.0,
                'momentum_divergence_strong': 3.5,
                'htf_trend_clarity': 2.5,
                'general_ta_score': 1.5,
                'price_action_patterns': 3.0,
                'volatility_analysis': 2.0,
                'fibonacci_levels': 2.5,
                'support_resistance': 3.0,
                'trend_strength': 2.8,
                'momentum_oscillators': 2.2,
                'volume_profile': 2.8,
                'market_sentiment': 2.0,
            }

            self.config.INDICATOR_WEIGHTS = default_weights.copy()

            # Update UI
            for key, spin in self.indicator_weight_spins.items():
                if key in default_weights:
                    spin.setValue(default_weights[key])

            self.config_changed.emit()
            QMessageBox.information(self, "Reset Berhasil", "Bobot indikator telah direset ke nilai default.")

    def reset_indicator_settings(self):
        """Reset indicator settings to default values"""
        reply = QMessageBox.question(
            self,
            "Reset Pengaturan Indikator",
            "Apakah Anda yakin ingin mereset semua pengaturan indikator ke nilai default?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # Reset to default settings
            default_settings = {
                '4h': {'rsi_period': 14, 'rsi_oversold': 25, 'rsi_overbought': 75, 'macd_fast': 12, 'macd_slow': 26, 'macd_signal': 9, 'bb_period': 20, 'bb_std_dev': 2.0, 'ema_short': 34, 'ema_medium': 89, 'ema_long': 200, 'atr_period': 14, 'adx_period': 14},
                '1h': {'rsi_period': 14, 'rsi_oversold': 28, 'rsi_overbought': 72, 'macd_fast': 12, 'macd_slow': 26, 'macd_signal': 9, 'bb_period': 20, 'bb_std_dev': 2.0, 'ema_short': 21, 'ema_medium': 55, 'ema_long': 100, 'atr_period': 14, 'adx_period': 14},
                '30m': {'rsi_period': 12, 'rsi_oversold': 30, 'rsi_overbought': 70, 'macd_fast': 10, 'macd_slow': 21, 'macd_signal': 8, 'bb_period': 20, 'bb_std_dev': 2.1, 'ema_short': 13, 'ema_medium': 34, 'ema_long': 55, 'atr_period': 12, 'adx_period': 12},
                '15m': {'rsi_period': 10, 'rsi_oversold': 20, 'rsi_overbought': 80, 'macd_fast': 9, 'macd_slow': 18, 'macd_signal': 6, 'bb_period': 15, 'bb_std_dev': 2.0, 'ema_short': 9, 'ema_medium': 21, 'ema_long': 34, 'atr_period': 10, 'adx_period': 10},
                '5m': {'rsi_period': 8, 'rsi_oversold': 15, 'rsi_overbought': 85, 'macd_fast': 8, 'macd_slow': 15, 'macd_signal': 5, 'bb_period': 12, 'bb_std_dev': 2.0, 'ema_short': 5, 'ema_medium': 13, 'ema_long': 21, 'atr_period': 8, 'adx_period': 8}
            }

            self.config.INDICATOR_SETTINGS = default_settings.copy()

            # Update UI
            for tf, widgets in self.indicator_settings_widgets.items():
                if tf in default_settings:
                    tf_settings = default_settings[tf]
                    for key, widget in widgets.items():
                        if key in tf_settings:
                            widget.setValue(tf_settings[key])

            self.config_changed.emit()
            QMessageBox.information(self, "Reset Berhasil", "Pengaturan indikator telah direset ke nilai default.")

    def save_configuration(self):
        """Save current configuration to file"""
        filename, _ = QFileDialog.getSaveFileName(
            self,
            "Simpan Konfigurasi Trading",
            "trading_config.json",
            "JSON Files (*.json);;All Files (*)"
        )

        if filename:
            success, message = self.config.save_to_file(filename)
            if success:
                QMessageBox.information(self, "Simpan Berhasil", message)
            else:
                QMessageBox.critical(self, "Error Simpan", message)

    def load_configuration(self):
        """Load configuration from file"""
        filename, _ = QFileDialog.getOpenFileName(
            self,
            "Muat Konfigurasi Trading",
            "",
            "JSON Files (*.json);;All Files (*)"
        )

        if filename:
            success, message = self.config.load_from_file(filename)
            if success:
                self.update_ui_from_config()
                self.config_changed.emit()
                QMessageBox.information(self, "Muat Berhasil", message)
            else:
                QMessageBox.critical(self, "Error Muat", message)

    def reset_all_configuration(self):
        """Reset all configuration to default values"""
        reply = QMessageBox.question(
            self,
            "Reset Semua Konfigurasi",
            "Apakah Anda yakin ingin mereset SEMUA konfigurasi ke nilai default?\n\nIni akan menghapus semua pengaturan custom Anda.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            self.config.reset_to_defaults()
            self.update_ui_from_config()
            self.config_changed.emit()
            QMessageBox.information(self, "Reset Berhasil", "Semua konfigurasi telah direset ke nilai default.")

    def apply_configuration(self):
        """Apply current configuration to global variables"""
        # Update global variables
        global MAX_PAIRS_DISPLAY, CANDLE_LIMIT_PER_TIMEFRAME, TIMEFRAMES_TO_ANALYZE
        global REFERENCE_TIMEFRAME_FOR_PRICE, MAX_WORKERS_ANALYSIS, MAX_CONCURRENT_DOWNLOADS
        global DOWNLOAD_BATCH_SIZE, ANALYSIS_BATCH_SIZE, ASYNC_TIMEOUT_SECONDS
        global CONNECTION_POOL_SIZE, MIN_CONFIDENCE_THRESHOLD, MIN_VOLUME_24H_USD
        global MIN_ATR_PERCENTAGE, MAX_ATR_PERCENTAGE, MIN_RRR_RATIO
        global VOLUME_CONFIRMATION_MULTIPLIER, DISCOVERY_MODE, EMERGENCY_MODE
        global DEBUG_FILTERING, ADVANCED_SIGNAL_THRESHOLD, CONFLUENCE_MULTIPLIER
        global VOLUME_SPIKE_THRESHOLD, MOMENTUM_LOOKBACK, TREND_STRENGTH_PERIODS
        global API_REQUEST_DELAY_MS, API_RETRY_DELAY_S, MAX_API_RETRIES
        global REQUEST_TIMEOUT_S, DEMO_MODE

        MAX_PAIRS_DISPLAY = self.config.MAX_PAIRS_DISPLAY
        CANDLE_LIMIT_PER_TIMEFRAME = self.config.CANDLE_LIMIT_PER_TIMEFRAME
        TIMEFRAMES_TO_ANALYZE = self.config.TIMEFRAMES_TO_ANALYZE.copy()
        REFERENCE_TIMEFRAME_FOR_PRICE = self.config.REFERENCE_TIMEFRAME_FOR_PRICE
        MAX_WORKERS_ANALYSIS = self.config.MAX_WORKERS_ANALYSIS
        MAX_CONCURRENT_DOWNLOADS = self.config.MAX_CONCURRENT_DOWNLOADS
        DOWNLOAD_BATCH_SIZE = self.config.DOWNLOAD_BATCH_SIZE
        ANALYSIS_BATCH_SIZE = self.config.ANALYSIS_BATCH_SIZE
        ASYNC_TIMEOUT_SECONDS = self.config.ASYNC_TIMEOUT_SECONDS
        CONNECTION_POOL_SIZE = self.config.CONNECTION_POOL_SIZE
        MIN_CONFIDENCE_THRESHOLD = self.config.MIN_CONFIDENCE_THRESHOLD
        MIN_VOLUME_24H_USD = self.config.MIN_VOLUME_24H_USD
        MIN_ATR_PERCENTAGE = self.config.MIN_ATR_PERCENTAGE
        MAX_ATR_PERCENTAGE = self.config.MAX_ATR_PERCENTAGE
        MIN_RRR_RATIO = self.config.MIN_RRR_RATIO
        VOLUME_CONFIRMATION_MULTIPLIER = self.config.VOLUME_CONFIRMATION_MULTIPLIER
        DISCOVERY_MODE = self.config.DISCOVERY_MODE
        EMERGENCY_MODE = self.config.EMERGENCY_MODE
        DEBUG_FILTERING = self.config.DEBUG_FILTERING
        ADVANCED_SIGNAL_THRESHOLD = self.config.ADVANCED_SIGNAL_THRESHOLD
        CONFLUENCE_MULTIPLIER = self.config.CONFLUENCE_MULTIPLIER
        VOLUME_SPIKE_THRESHOLD = self.config.VOLUME_SPIKE_THRESHOLD
        MOMENTUM_LOOKBACK = self.config.MOMENTUM_LOOKBACK
        TREND_STRENGTH_PERIODS = self.config.TREND_STRENGTH_PERIODS
        API_REQUEST_DELAY_MS = self.config.API_REQUEST_DELAY_MS
        API_RETRY_DELAY_S = self.config.API_RETRY_DELAY_S
        MAX_API_RETRIES = self.config.MAX_API_RETRIES
        REQUEST_TIMEOUT_S = self.config.REQUEST_TIMEOUT_S
        DEMO_MODE = self.config.DEMO_MODE

        QMessageBox.information(self, "Konfigurasi Diterapkan", "Konfigurasi telah berhasil diterapkan ke sistem.\n\nPerubahan akan efektif pada analisis berikutnya.")

    def update_ui_from_config(self):
        """Update UI elements from current configuration"""
        # Update timeframe checkboxes
        for tf, cb in self.timeframe_checkboxes.items():
            cb.setChecked(tf in self.config.TIMEFRAMES_TO_ANALYZE)

        # Update general settings
        self.ref_timeframe_combo.setCurrentText(self.config.REFERENCE_TIMEFRAME_FOR_PRICE)
        self.max_pairs_spin.setValue(self.config.MAX_PAIRS_DISPLAY)
        self.candle_limit_spin.setValue(self.config.CANDLE_LIMIT_PER_TIMEFRAME)

        # Update mode settings
        self.discovery_mode_cb.setChecked(self.config.DISCOVERY_MODE)
        self.emergency_mode_cb.setChecked(self.config.EMERGENCY_MODE)
        self.debug_filtering_cb.setChecked(self.config.DEBUG_FILTERING)
        self.demo_mode_cb.setChecked(self.config.DEMO_MODE)

        # Update performance settings
        if hasattr(self, 'max_workers_spin'):
            self.max_workers_spin.setValue(self.config.MAX_WORKERS_ANALYSIS)
            self.max_concurrent_spin.setValue(self.config.MAX_CONCURRENT_DOWNLOADS)
            self.download_batch_spin.setValue(self.config.DOWNLOAD_BATCH_SIZE)
            self.analysis_batch_spin.setValue(self.config.ANALYSIS_BATCH_SIZE)
            self.api_delay_spin.setValue(self.config.API_REQUEST_DELAY_MS)
            self.api_retry_delay_spin.setValue(self.config.API_RETRY_DELAY_S)
            self.max_retries_spin.setValue(self.config.MAX_API_RETRIES)
            self.request_timeout_spin.setValue(self.config.REQUEST_TIMEOUT_S)
            self.async_timeout_spin.setValue(self.config.ASYNC_TIMEOUT_SECONDS)
            self.connection_pool_spin.setValue(self.config.CONNECTION_POOL_SIZE)

        # Update threshold settings
        if hasattr(self, 'min_confidence_spin'):
            self.min_confidence_spin.setValue(self.config.MIN_CONFIDENCE_THRESHOLD)
            self.advanced_signal_spin.setValue(self.config.ADVANCED_SIGNAL_THRESHOLD)
            self.confluence_multiplier_spin.setValue(self.config.CONFLUENCE_MULTIPLIER)
            self.volume_spike_spin.setValue(self.config.VOLUME_SPIKE_THRESHOLD)
            self.momentum_lookback_spin.setValue(self.config.MOMENTUM_LOOKBACK)
            self.trend_strength_spin.setValue(self.config.TREND_STRENGTH_PERIODS)

        # Update filtering settings
        if hasattr(self, 'enable_stablecoin_cb'):
            self.enable_stablecoin_cb.setChecked(self.config.ENABLE_STABLECOIN_FILTERING)
            self.min_volume_spin.setValue(self.config.MIN_VOLUME_24H_USD)
            self.min_atr_spin.setValue(self.config.MIN_ATR_PERCENTAGE)
            self.max_atr_spin.setValue(self.config.MAX_ATR_PERCENTAGE)
            self.min_rrr_spin.setValue(self.config.MIN_RRR_RATIO)
            self.volume_confirmation_spin.setValue(self.config.VOLUME_CONFIRMATION_MULTIPLIER)

        # Update indicator weights
        for key, spin in self.indicator_weight_spins.items():
            if key in self.config.INDICATOR_WEIGHTS:
                spin.setValue(self.config.INDICATOR_WEIGHTS[key])

        # Update indicator settings
        for tf, widgets in self.indicator_settings_widgets.items():
            if tf in self.config.INDICATOR_SETTINGS:
                tf_settings = self.config.INDICATOR_SETTINGS[tf]
                for key, widget in widgets.items():
                    if key in tf_settings:
                        widget.setValue(tf_settings[key])

class ModernSignalCard(QFrame):
    generate_prompt_requested = Signal(str)

    def __init__(self, pair_symbol, signal_type, score, price, confidence):
        super().__init__()
        self.pair_symbol = pair_symbol
        self.signal_type = signal_type
        self.score = score
        self.price = price
        self.confidence = confidence

        self.setFrameStyle(QFrame.Box)
        self.setStyleSheet(f"""
            QFrame {{
                background-color: {COLOR_CARD_BG.name()};
                border: 2px solid {COLOR_PRIMARY_ACCENT_APP.name()};
                border-radius: 15px;
                margin: 10px;
                padding: 15px;
            }}
        """)

        layout = QVBoxLayout(self)

        # Header
        header_layout = QHBoxLayout()
        pair_label = QLabel(pair_symbol)
        pair_label.setFont(QFont("Inter", 16, QFont.Bold))
        pair_label.setStyleSheet(f"color: {COLOR_TEXT_HEADLINE.name()};")

        signal_label = QLabel(signal_type)
        signal_label.setFont(QFont("Inter", 12, QFont.Bold))
        if "BELI" in signal_type:
            signal_label.setStyleSheet(f"color: {COLOR_SUCCESS_APP.name()}; background-color: {COLOR_SUCCESS_APP.lighter(180).name()}; padding: 5px; border-radius: 5px;")
        elif "JUAL" in signal_type:
            signal_label.setStyleSheet(f"color: {COLOR_ERROR_APP.name()}; background-color: {COLOR_ERROR_APP.lighter(180).name()}; padding: 5px; border-radius: 5px;")
        else:
            signal_label.setStyleSheet(f"color: {COLOR_TEXT_MUTED.name()}; background-color: {COLOR_TEXT_MUTED.lighter(180).name()}; padding: 5px; border-radius: 5px;")

        header_layout.addWidget(pair_label)
        header_layout.addStretch()
        header_layout.addWidget(signal_label)
        layout.addLayout(header_layout)

        # Details
        details_layout = QHBoxLayout()
        price_label = QLabel(f"Price: ${price:.4f}" if price else "Price: N/A")
        score_label = QLabel(f"Score: {score:.2f}")
        conf_label = QLabel(f"Confidence: {confidence*100:.1f}%")

        for label in [price_label, score_label, conf_label]:
            label.setStyleSheet(f"color: {COLOR_TEXT_BODY.name()}; font-size: 11px;")

        details_layout.addWidget(price_label)
        details_layout.addWidget(score_label)
        details_layout.addWidget(conf_label)
        layout.addLayout(details_layout)

        # Action button
        action_button = QPushButton("📝 Generate AI Prompt")
        action_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {COLOR_SECONDARY_ACCENT_APP.name()};
                color: white;
                border: none;
                padding: 8px;
                border-radius: 8px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {COLOR_SECONDARY_ACCENT_APP.darker(110).name()};
            }}
        """)
        action_button.clicked.connect(lambda: self.generate_prompt_requested.emit(self.pair_symbol))
        layout.addWidget(action_button)

# --- Fungsi Helper Pengambilan Data (Manual REST API) ---
def get_active_futures_markets_manual(log_signal=None):
    """Get active futures markets with enhanced error handling and rate limiting"""
    endpoint = "/fapi/v1/exchangeInfo"
    url = BINANCE_FUTURES_API_BASE_URL + endpoint
    active_symbols_api_format = []

    # Enhanced retry mechanism for rate limiting
    max_retries = 5
    base_delay = 5  # Start with 5 seconds

    for attempt in range(max_retries):
        try:
            if attempt > 0:
                delay = base_delay * (2 ** attempt)  # Exponential backoff
                log_to_console_and_gui(f"Manual REST: Retry {attempt+1}/{max_retries} setelah {delay}s delay...", log_signal, "WARNING")
                time.sleep(delay)

            log_to_console_and_gui(f"Manual REST: Mengambil exchange info (attempt {attempt+1})...", log_signal, "DEBUG")

            # Add headers to appear more legitimate
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Accept': 'application/json',
                'Accept-Encoding': 'gzip, deflate',
                'Connection': 'keep-alive'
            }

            response = requests.get(url, timeout=REQUEST_TIMEOUT_S * 2, headers=headers)

            # Handle specific error codes
            if response.status_code == 418:
                log_to_console_and_gui(f"Manual REST: IP banned (418) detected. Switching to fallback mode immediately...", log_signal, "WARNING")
                # Don't wait for IP ban recovery, immediately return error to trigger fallback
                return [], "IP banned - switching to popular pairs fallback"

            elif response.status_code == 429:
                retry_after = int(response.headers.get('Retry-After', 60))
                log_to_console_and_gui(f"Manual REST: Rate limited (429). Waiting {retry_after}s...", log_signal, "WARNING")
                if attempt < max_retries - 1:
                    time.sleep(retry_after)
                    continue
                else:
                    return [], "Rate limited - please try again later"

            response.raise_for_status()
            data = response.json()

            # Process the data
            for mi in data.get('symbols', []):
                if (mi.get('contractType') == 'PERPETUAL' and
                    mi.get('status') == 'TRADING' and
                    mi.get('quoteAsset') == 'USDT' and
                    mi.get('marginAsset') == 'USDT'):
                    active_symbols_api_format.append(mi['symbol'])

            log_to_console_and_gui(f"Manual REST: {len(active_symbols_api_format)} pasar futures USDT aktif ditemukan.", log_signal)
            return active_symbols_api_format, None

        except requests.exceptions.HTTPError as e:
            err_msg = f"HTTPError - {e}"
            # Check if it's a 418 error and immediately trigger fallback
            if "418" in str(e):
                log_to_console_and_gui(f"Manual REST: 418 IP ban detected in exception. Triggering fallback...", log_signal, "WARNING")
                return [], "IP banned - switching to popular pairs fallback"
            log_to_console_and_gui(f"Manual REST: Error mengambil exchange info: {err_msg} (URL: {url})", log_signal, "ERROR")
            if attempt == max_retries - 1:
                return [], err_msg
        except Exception as e:
            err_msg = f"Error - {type(e).__name__}: {e}"
            log_to_console_and_gui(f"Manual REST: Error mengambil exchange info: {err_msg}", log_signal, "ERROR")
            if attempt == max_retries - 1:
                return [], err_msg

    return [], "Failed after all retry attempts"

def get_popular_pairs_fallback():
    """Fallback function that returns popular trading pairs when API is unavailable"""
    popular_pairs = [
        'BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'ADAUSDT', 'SOLUSDT', 'XRPUSDT', 'DOTUSDT', 'DOGEUSDT',
        'AVAXUSDT', 'MATICUSDT', 'LINKUSDT', 'LTCUSDT', 'UNIUSDT', 'ATOMUSDT', 'FILUSDT', 'ETCUSDT',
        'XLMUSDT', 'VETUSDT', 'ICPUSDT', 'THETAUSDT', 'FTMUSDT', 'TRXUSDT', 'EOSUSDT', 'AAVEUSDT',
        'ALGOUSDT', 'AXSUSDT', 'SANDUSDT', 'MANAUSDT', 'ENJUSDT', 'CHZUSDT', 'NEARUSDT', 'APTUSDT',
        'OPUSDT', 'ARBUSDT', 'SUIUSDT', 'INJUSDT', 'TIAUSDT', 'SEIUSDT', 'WLDUSDT', 'PEPEUSDT'
    ]
    return popular_pairs, None

# --- V5 PERFORMANCE: Asynchronous Data Download Functions ---
async def fetch_ohlcv_async(session: aiohttp.ClientSession, pair_symbol: str, timeframe: str, limit: int = CANDLE_LIMIT_PER_TIMEFRAME) -> Tuple[str, str, Optional[pd.DataFrame], Optional[str]]:
    """Async function to fetch OHLCV data for a single pair and timeframe"""
    endpoint = "/fapi/v1/klines"
    url = BINANCE_FUTURES_API_BASE_URL + endpoint
    params = {'symbol': pair_symbol, 'interval': timeframe, 'limit': limit}

    for attempt in range(MAX_API_RETRIES):
        try:
            async with session.get(url, params=params, timeout=aiohttp.ClientTimeout(total=ASYNC_TIMEOUT_SECONDS)) as response:
                if response.status == 429 or response.status == 418:
                    retry_after = int(response.headers.get('Retry-After', API_RETRY_DELAY_S * (attempt + 1)))
                    if attempt < MAX_API_RETRIES - 1:
                        await asyncio.sleep(retry_after)
                        continue
                    else:
                        return pair_symbol, timeframe, None, f"Rate limit exceeded: {pair_symbol} {timeframe}"

                response.raise_for_status()
                ohlcv_data = await response.json()

                if not ohlcv_data:
                    return pair_symbol, timeframe, None, f"No data: {pair_symbol} {timeframe}"

                # Process data
                df = pd.DataFrame(ohlcv_data, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume', 'ct', 'qav', 'not', 'tbba', 'tbqa', 'ig'])
                df = df[['timestamp', 'open', 'high', 'low', 'close', 'volume']]
                df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
                df.set_index('timestamp', inplace=True)
                df.sort_index(inplace=True)

                for col in ['open', 'high', 'low', 'close', 'volume']:
                    df[col] = pd.to_numeric(df[col], errors='coerce')
                df.dropna(subset=['open', 'high', 'low', 'close', 'volume'], inplace=True)

                return pair_symbol, timeframe, df, None

        except Exception as e:
            if attempt < MAX_API_RETRIES - 1:
                await asyncio.sleep(API_RETRY_DELAY_S * (attempt + 1))
                continue
            return pair_symbol, timeframe, None, f"Error: {type(e).__name__} - {e}"

    return pair_symbol, timeframe, None, f"Failed after {MAX_API_RETRIES} attempts"

async def fetch_ohlcv_async_with_semaphore(session, semaphore, pair_symbol, timeframe):
    """Fetch OHLCV data with semaphore control to prevent overload"""
    async with semaphore:  # Limit concurrent requests
        try:
            # Small delay to prevent API hammering
            await asyncio.sleep(0.02)  # 20ms delay between requests
            return await fetch_ohlcv_async(session, pair_symbol, timeframe)
        except Exception as e:
            logging.warning(f"Semaphore-controlled fetch failed for {pair_symbol} {timeframe}: {e}")
            return pair_symbol, timeframe, None, f"Semaphore fetch error: {e}"

async def download_all_data_async(pairs: List[str], timeframes: List[str], progress_callback=None) -> Dict[str, Dict[str, pd.DataFrame]]:
    """Download ALL data for ALL pairs and timeframes asynchronously"""
    all_data = {}
    total_requests = len(pairs) * len(timeframes)
    completed_requests = 0

    # RELIABILITY OPTIMIZATION: Balanced connection pool for stable downloads
    connector = aiohttp.TCPConnector(
        limit=CONNECTION_POOL_SIZE,  # Balanced connection pool
        limit_per_host=30,  # Conservative per-host limit to prevent overload
        ttl_dns_cache=300,  # DNS cache for faster lookups
        use_dns_cache=True,
        keepalive_timeout=60,  # Longer keep-alive for stability
        enable_cleanup_closed=True,
        force_close=False,  # Reuse connections
        limit_per_host_timeout=30  # Timeout for host connections
    )
    timeout = aiohttp.ClientTimeout(total=ASYNC_TIMEOUT_SECONDS, connect=10, sock_read=30)

    async with aiohttp.ClientSession(connector=connector, timeout=timeout) as session:
        # RELIABILITY OPTIMIZATION: Use semaphore to control concurrent requests
        semaphore = asyncio.Semaphore(MAX_CONCURRENT_DOWNLOADS)

        # Create tasks with semaphore control
        tasks = []
        for pair in pairs:
            for timeframe in timeframes:
                task = fetch_ohlcv_async_with_semaphore(session, semaphore, pair, timeframe)
                tasks.append(task)

        # RELIABILITY OPTIMIZATION: Smaller batches with proper timeout handling
        batch_size = DOWNLOAD_BATCH_SIZE // 2  # Smaller batches to prevent timeouts
        total_batches = (len(tasks) + batch_size - 1) // batch_size

        for i in range(0, len(tasks), batch_size):
            batch = tasks[i:i + batch_size]
            batch_num = i // batch_size + 1

            # Execute batch with extended timeout for reliability
            try:
                batch_results = await asyncio.wait_for(
                    asyncio.gather(*batch, return_exceptions=True),
                    timeout=ASYNC_TIMEOUT_SECONDS * 1.5  # 1.5x timeout for reliability
                )
                logging.info(f"Batch {batch_num}/{total_batches} completed successfully")
            except asyncio.TimeoutError:
                logging.warning(f"Batch {batch_num}/{total_batches} timed out, retrying with smaller chunks...")
                # Retry with even smaller chunks
                chunk_size = len(batch) // 4  # Split into 4 smaller chunks
                batch_results = []
                for j in range(0, len(batch), chunk_size):
                    chunk = batch[j:j + chunk_size]
                    try:
                        chunk_results = await asyncio.wait_for(
                            asyncio.gather(*chunk, return_exceptions=True),
                            timeout=ASYNC_TIMEOUT_SECONDS // 2
                        )
                        batch_results.extend(chunk_results)
                    except asyncio.TimeoutError:
                        logging.warning(f"Chunk {j//chunk_size + 1} failed, skipping...")
                        batch_results.extend([None] * len(chunk))

            for result in batch_results:
                if result is None or isinstance(result, Exception):
                    completed_requests += 1
                    continue

                pair_symbol, timeframe, df, error = result

                if error:
                    completed_requests += 1
                    continue

                if pair_symbol not in all_data:
                    all_data[pair_symbol] = {}
                all_data[pair_symbol][timeframe] = df

                completed_requests += 1

                # SPEED OPTIMIZATION: Update progress less frequently
                if completed_requests % 25 == 0 or completed_requests == total_requests:
                    if progress_callback:
                        progress = int((completed_requests / total_requests) * 100)
                        progress_callback(progress, f"Downloaded {completed_requests}/{total_requests} datasets")

            # RELIABILITY OPTIMIZATION: Proper delay for API rate limiting
            await asyncio.sleep(0.5)  # Increased delay to prevent API overload

    return all_data

def fetch_ohlcv_manual_rest(pair_symbol_binance_format, timeframe_str, limit, log_signal=None):
    """Legacy sync function - kept for compatibility"""
    endpoint = "/fapi/v1/klines"; url = BINANCE_FUTURES_API_BASE_URL + endpoint
    params = {'symbol': pair_symbol_binance_format, 'interval': timeframe_str, 'limit': limit}
    if API_REQUEST_DELAY_MS > 0: time.sleep(API_REQUEST_DELAY_MS / 1000.0)
    for attempt in range(MAX_API_RETRIES):
        try:
            log_to_console_and_gui(f"Manual REST: Fetching {pair_symbol_binance_format} TF {timeframe_str} (Attempt {attempt+1})...", log_signal, "DEBUG")
            response = requests.get(url, params=params, timeout=REQUEST_TIMEOUT_S)
            remaining_weight = response.headers.get('X-MBX-USED-WEIGHT-1M')
            if remaining_weight: log_to_console_and_gui(f"Manual REST: Bobot terpakai (X-MBX-USED-WEIGHT-1M) untuk {pair_symbol_binance_format}: {remaining_weight}", log_signal, "DEBUG")
            if response.status_code == 429 or response.status_code == 418:
                retry_after = int(response.headers.get('Retry-After', API_RETRY_DELAY_S * (attempt + 1)))
                log_msg = f"Manual REST: Rate limit {response.status_code} untuk {pair_symbol_binance_format} TF {timeframe_str}. Menunggu {retry_after}s..."
                log_to_console_and_gui(log_msg, log_signal, "WARNING")
                if attempt < MAX_API_RETRIES - 1: time.sleep(retry_after); continue
                else: return pd.DataFrame(), f"Rate limit terlampaui: {pair_symbol_binance_format} TF {timeframe_str}."
            response.raise_for_status()
            ohlcv_data = response.json()
            if not ohlcv_data: return pd.DataFrame(), f"No data: {pair_symbol_binance_format} TF {timeframe_str}."
            df = pd.DataFrame(ohlcv_data, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume', 'ct', 'qav', 'not', 'tbba', 'tbqa', 'ig'])
            df = df[['timestamp', 'open', 'high', 'low', 'close', 'volume']]
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            # V4 Fix: Set timestamp as index for proper VWAP calculation
            df.set_index('timestamp', inplace=True)
            df.sort_index(inplace=True)  # Ensure chronological order
            for col in ['open', 'high', 'low', 'close', 'volume']: df[col] = pd.to_numeric(df[col], errors='coerce')
            df.dropna(subset=['open', 'high', 'low', 'close', 'volume'], inplace=True)
            return df, None
        except Exception as e:
            err_msg = f"Error fetch {pair_symbol_binance_format} TF {timeframe_str}: {type(e).__name__} - {e}"
            log_to_console_and_gui(f"Manual REST: {err_msg}", log_signal, "ERROR" if attempt == MAX_API_RETRIES -1 else "WARNING")
            if attempt < MAX_API_RETRIES - 1: time.sleep(API_RETRY_DELAY_S * (attempt + 1)); continue
            return pd.DataFrame(), err_msg
    return pd.DataFrame(), f"Gagal fetch {pair_symbol_binance_format} TF {timeframe_str} setelah {MAX_API_RETRIES} percobaan."

# --- Pengaturan Indikator Kunci (Updated to use config) ---
def get_key_indicator_settings_for_tf(timeframe_str):
    """Get indicator settings for specific timeframe from configuration"""
    global trading_config

    # Get settings from config, with fallback to defaults
    if timeframe_str in trading_config.INDICATOR_SETTINGS:
        base_settings = trading_config.INDICATOR_SETTINGS[timeframe_str].copy()
    else:
        # Fallback to default settings
        base_settings = {'rsi_period': 14, 'rsi_oversold': 30, 'rsi_overbought': 70, 'macd_fast': 12, 'macd_slow': 26, 'macd_signal': 9, 'bb_period': 20, 'bb_std_dev': 2.0, 'ema_short': 21, 'ema_medium': 55, 'ema_long': 100, 'atr_period': 14, 'adx_period': 14}

    # Add additional settings that are not configurable via GUI but needed for analysis
    additional_settings = {
        'stoch_k': 14, 'stoch_d': 3, 'stoch_smooth_k': 3, 'stoch_oversold': 20, 'stoch_overbought': 80,
        'vwap_period': 20, 'volume_sma_period': 20, 'zigzag_len': 10
    }

    # Merge base settings with additional settings
    settings = {**base_settings, **additional_settings}

    # Adjust additional settings based on timeframe for better performance
    if timeframe_str == '4h':
        settings.update({'vwap_period': 20, 'volume_sma_period': 20, 'zigzag_len': 12})
    elif timeframe_str == '1h':
        settings.update({'vwap_period': 20, 'volume_sma_period': 20, 'zigzag_len': 10})
    elif timeframe_str == '30m':
        settings.update({'vwap_period': 14, 'volume_sma_period': 20, 'zigzag_len': 8})
    elif timeframe_str == '15m':
        settings.update({'vwap_period': 10, 'volume_sma_period': 15, 'zigzag_len': 5})
    elif timeframe_str == '5m':
        settings.update({'vwap_period': 8, 'volume_sma_period': 12, 'zigzag_len': 3})

    return settings

# --- Advanced Algorithm Functions (NEW V4) ---
def calculate_fibonacci_levels(df, lookback=50):
    """Calculate Fibonacci retracement levels"""
    if len(df) < lookback:
        return {}

    recent_data = df.tail(lookback)
    high_price = recent_data['high'].max()
    low_price = recent_data['low'].min()
    diff = high_price - low_price

    fib_levels = {
        'high': high_price,
        'low': low_price,
        '23.6': high_price - (diff * 0.236),
        '38.2': high_price - (diff * 0.382),
        '50.0': high_price - (diff * 0.5),
        '61.8': high_price - (diff * 0.618),
        '78.6': high_price - (diff * 0.786)
    }
    return fib_levels

def calculate_support_resistance_levels(df, window=20):
    """Calculate dynamic support and resistance levels"""
    if len(df) < window * 2:
        return {'support': [], 'resistance': []}

    highs = df['high'].rolling(window=window, center=True).max()
    lows = df['low'].rolling(window=window, center=True).min()

    # Find pivot points
    resistance_levels = []
    support_levels = []

    for i in range(window, len(df) - window):
        if df['high'].iloc[i] == highs.iloc[i]:
            resistance_levels.append(df['high'].iloc[i])
        if df['low'].iloc[i] == lows.iloc[i]:
            support_levels.append(df['low'].iloc[i])

    # Remove duplicates and sort
    resistance_levels = sorted(list(set(resistance_levels)), reverse=True)[:5]
    support_levels = sorted(list(set(support_levels)))[:5]

    return {'support': support_levels, 'resistance': resistance_levels}

def calculate_trend_strength(df, period=50):
    """Calculate trend strength using multiple methods"""
    if len(df) < period:
        return 0

    # Method 1: Price position relative to moving averages
    sma_20 = df['close'].rolling(20).mean().iloc[-1]
    sma_50 = df['close'].rolling(50).mean().iloc[-1] if len(df) >= 50 else sma_20
    current_price = df['close'].iloc[-1]

    # Method 2: ADX-like calculation
    high_low = df['high'] - df['low']
    high_close_prev = abs(df['high'] - df['close'].shift(1))
    low_close_prev = abs(df['low'] - df['close'].shift(1))
    true_range = pd.concat([high_low, high_close_prev, low_close_prev], axis=1).max(axis=1)

    plus_dm = (df['high'] - df['high'].shift(1)).where((df['high'] - df['high'].shift(1)) > (df['low'].shift(1) - df['low']), 0)
    minus_dm = (df['low'].shift(1) - df['low']).where((df['low'].shift(1) - df['low']) > (df['high'] - df['high'].shift(1)), 0)

    plus_di = 100 * (plus_dm.rolling(14).mean() / true_range.rolling(14).mean())
    minus_di = 100 * (minus_dm.rolling(14).mean() / true_range.rolling(14).mean())

    dx = 100 * abs(plus_di - minus_di) / (plus_di + minus_di)
    adx = dx.rolling(14).mean().iloc[-1]

    # Combine methods
    ma_strength = 1 if current_price > sma_20 > sma_50 else (-1 if current_price < sma_20 < sma_50 else 0)
    trend_strength = (ma_strength * 0.6) + (adx / 100 * 0.4)

    return min(1, max(-1, trend_strength))

def calculate_volume_profile(df, bins=20):
    """Calculate volume profile for price levels"""
    if len(df) < 20:
        return {}

    price_range = df['high'].max() - df['low'].min()
    bin_size = price_range / bins

    volume_profile = {}
    for i in range(bins):
        price_level = df['low'].min() + (i * bin_size)
        volume_at_level = 0

        for _, row in df.iterrows():
            if price_level <= row['high'] and price_level >= row['low']:
                volume_at_level += row['volume']

        volume_profile[round(price_level, 4)] = volume_at_level

    # Find POC (Point of Control) - highest volume level
    poc_price = max(volume_profile, key=volume_profile.get)

    return {
        'profile': volume_profile,
        'poc': poc_price,
        'high_volume_levels': sorted(volume_profile.items(), key=lambda x: x[1], reverse=True)[:3]
    }

def calculate_momentum_oscillators(df):
    """Calculate advanced momentum oscillators"""
    if len(df) < 50:
        return {}

    try:
        # Williams %R
        high_14 = df['high'].rolling(14).max()
        low_14 = df['low'].rolling(14).min()
        williams_r = -100 * (high_14 - df['close']) / (high_14 - low_14)

        # Commodity Channel Index (CCI)
        typical_price = (df['high'] + df['low'] + df['close']) / 3
        sma_tp = typical_price.rolling(20).mean()
        mad = typical_price.rolling(20).apply(lambda x: abs(x - x.mean()).mean())
        cci = (typical_price - sma_tp) / (0.015 * mad)

        # Ultimate Oscillator - Fixed shift issue
        close_prev = df['close'].shift(1)
        low_prev = df['low'].shift(1)
        high_prev = df['high'].shift(1)

        bp = df['close'] - pd.concat([df['low'], close_prev], axis=1).min(axis=1)
        tr = pd.concat([df['high'], close_prev], axis=1).max(axis=1) - pd.concat([df['low'], close_prev], axis=1).min(axis=1)

        # Avoid division by zero
        tr = tr.replace(0, 0.0001)

        avg7 = bp.rolling(7).sum() / tr.rolling(7).sum()
        avg14 = bp.rolling(14).sum() / tr.rolling(14).sum()
        avg28 = bp.rolling(28).sum() / tr.rolling(28).sum()

        uo = 100 * (4 * avg7 + 2 * avg14 + avg28) / 7

        return {
            'williams_r': williams_r.iloc[-1] if not williams_r.empty and not pd.isna(williams_r.iloc[-1]) else 0,
            'cci': cci.iloc[-1] if not cci.empty and not pd.isna(cci.iloc[-1]) else 0,
            'ultimate_oscillator': uo.iloc[-1] if not uo.empty and not pd.isna(uo.iloc[-1]) else 0
        }
    except Exception as e:
        logging.debug(f"Momentum oscillators calculation error: {e}")
        return {'williams_r': 0, 'cci': 0, 'ultimate_oscillator': 0}

def calculate_price_action_patterns(df):
    """Detect advanced price action patterns"""
    if len(df) < 10:
        return []

    patterns = []
    recent_candles = df.tail(5)

    # Doji detection
    for i, row in recent_candles.iterrows():
        body_size = abs(row['close'] - row['open'])
        candle_range = row['high'] - row['low']
        if candle_range > 0 and body_size / candle_range < 0.1:
            patterns.append({
                'type': 'Doji',
                'timestamp': i,
                'significance': 'High' if candle_range > df['high'].rolling(20).max().iloc[-1] * 0.02 else 'Medium'
            })

    # Hammer/Shooting Star detection
    for i, row in recent_candles.iterrows():
        body_size = abs(row['close'] - row['open'])
        upper_shadow = row['high'] - max(row['open'], row['close'])
        lower_shadow = min(row['open'], row['close']) - row['low']
        candle_range = row['high'] - row['low']

        if candle_range > 0:
            if lower_shadow > body_size * 2 and upper_shadow < body_size * 0.5:
                patterns.append({
                    'type': 'Hammer',
                    'timestamp': i,
                    'significance': 'High' if lower_shadow > body_size * 3 else 'Medium'
                })
            elif upper_shadow > body_size * 2 and lower_shadow < body_size * 0.5:
                patterns.append({
                    'type': 'Shooting Star',
                    'timestamp': i,
                    'significance': 'High' if upper_shadow > body_size * 3 else 'Medium'
                })

    # Engulfing patterns
    if len(recent_candles) >= 2:
        for i in range(1, len(recent_candles)):
            prev_candle = recent_candles.iloc[i-1]
            curr_candle = recent_candles.iloc[i]

            prev_body = abs(prev_candle['close'] - prev_candle['open'])
            curr_body = abs(curr_candle['close'] - curr_candle['open'])

            # Bullish engulfing
            if (prev_candle['close'] < prev_candle['open'] and
                curr_candle['close'] > curr_candle['open'] and
                curr_candle['open'] < prev_candle['close'] and
                curr_candle['close'] > prev_candle['open']):
                patterns.append({
                    'type': 'Bullish Engulfing',
                    'timestamp': recent_candles.index[i],
                    'significance': 'High'
                })

            # Bearish engulfing
            if (prev_candle['close'] > prev_candle['open'] and
                curr_candle['close'] < curr_candle['open'] and
                curr_candle['open'] > prev_candle['close'] and
                curr_candle['close'] < prev_candle['open']):
                patterns.append({
                    'type': 'Bearish Engulfing',
                    'timestamp': recent_candles.index[i],
                    'significance': 'High'
                })

    return patterns[-3:]  # Return last 3 patterns

# --- Performance Optimization Functions V4 ---
def optimize_dataframe_memory(df):
    """Optimize DataFrame memory usage"""
    if df.empty:
        return df

    # Convert to more efficient data types
    for col in ['open', 'high', 'low', 'close']:
        if col in df.columns:
            df[col] = pd.to_numeric(df[col], downcast='float')

    if 'volume' in df.columns:
        df['volume'] = pd.to_numeric(df['volume'], downcast='unsigned')

    return df

def batch_calculate_indicators(df_dict, settings_dict):
    """Calculate indicators in batches for better performance"""
    results = {}

    # Group similar calculations
    for pair, df in df_dict.items():
        if pair in settings_dict:
            try:
                settings = settings_dict[pair]

                # Optimize memory first
                df = optimize_dataframe_memory(df)

                # Calculate only essential indicators first
                essential_indicators = {}

                # RSI (fast calculation)
                if len(df) >= settings['rsi_period']:
                    essential_indicators['rsi'] = df.ta.rsi(length=settings['rsi_period'])

                # MACD (vectorized)
                if len(df) >= settings['macd_slow']:
                    macd_result = df.ta.macd(fast=settings['macd_fast'],
                                           slow=settings['macd_slow'],
                                           signal=settings['macd_signal'])
                    if macd_result is not None:
                        essential_indicators['macd'] = macd_result

                # EMA (efficient calculation)
                for ema_period in [settings['ema_short'], settings['ema_medium'], settings['ema_long']]:
                    if len(df) >= ema_period:
                        essential_indicators[f'ema_{ema_period}'] = df.ta.ema(length=ema_period)

                results[pair] = essential_indicators

            except Exception as e:
                logging.warning(f"Batch indicator calculation failed for {pair}: {e}")
                results[pair] = {}

    return results

# --- V5 Enhanced: Comprehensive Stablecoin Filtering ---
def filter_stablecoins(pairs_list):
    """V5 Enhanced: Remove all stablecoin pairs for better analysis focus"""
    if not pairs_list or not ENABLE_STABLECOIN_FILTERING:
        return pairs_list, 0

    import re

    original_count = len(pairs_list)
    filtered_pairs = []
    stablecoin_patterns = []

    # Create regex patterns for stablecoin detection
    for stablecoin in ALL_STABLECOINS:
        # Pattern 1: STABLECOINUSDT (e.g., USDCUSDT, BUSDUSDT)
        stablecoin_patterns.append(f"^{stablecoin}USDT$")
        # Pattern 2: STABLECOINBUSD (e.g., USDCBUSD)
        stablecoin_patterns.append(f"^{stablecoin}BUSD$")
        # Pattern 3: Cross-stablecoin pairs
        for other_stable in ALL_STABLECOINS:
            if stablecoin != other_stable:
                stablecoin_patterns.append(f"^{stablecoin}{other_stable}$")

    # Compile regex patterns for efficiency
    compiled_patterns = [re.compile(pattern, re.IGNORECASE) for pattern in stablecoin_patterns]

    # Filter out stablecoin pairs
    for pair in pairs_list:
        is_stablecoin_pair = False
        for pattern in compiled_patterns:
            if pattern.match(pair):
                is_stablecoin_pair = True
                break

        if not is_stablecoin_pair:
            filtered_pairs.append(pair)

    filtered_count = original_count - len(filtered_pairs)

    return filtered_pairs, filtered_count

def smart_pair_filtering(pairs_list, min_volume_threshold=1000000):
    """V5 Enhanced: Analyze ALL non-stablecoin futures pairs for comprehensive market coverage"""
    if not pairs_list:
        return pairs_list

    # V5 Enhanced: Apply stablecoin filtering first
    if ENABLE_STABLECOIN_FILTERING:
        pairs_list, stablecoin_filtered = filter_stablecoins(pairs_list)
        if stablecoin_filtered > 0:
            logging.info(f"V5 Stablecoin Filter: Removed {stablecoin_filtered} stablecoin pairs")

    # Priority pairs for better ordering (most liquid first)
    priority_pairs = [
        'BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'XRPUSDT', 'ADAUSDT', 'SOLUSDT',
        'DOGEUSDT', 'MATICUSDT', 'DOTUSDT', 'AVAXUSDT', 'LINKUSDT', 'UNIUSDT',
        'LTCUSDT', 'BCHUSDT', 'FILUSDT', 'TRXUSDT', 'ETCUSDT', 'XLMUSDT',
        'VETUSDT', 'ICPUSDT', 'FTMUSDT', 'HBARUSDT', 'NEARUSDT', 'ATOMUSDT'
    ]

    # Prioritize high-volume pairs first, then add all remaining pairs
    filtered_pairs = []
    for pair in priority_pairs:
        if pair in pairs_list:
            filtered_pairs.append(pair)

    # Add ALL remaining pairs (no limit for comprehensive analysis)
    remaining_pairs = [p for p in pairs_list if p not in filtered_pairs]
    filtered_pairs.extend(remaining_pairs)

    # V5 Enhanced: Return ALL non-stablecoin pairs for complete market coverage
    return filtered_pairs  # No limit - analyze ALL non-stablecoin futures pairs

def parallel_feature_calculation(df, timeframe_str, settings):
    """Calculate features in parallel for better performance"""
    try:
        # Use ThreadPoolExecutor for I/O bound operations
        with ThreadPoolExecutor(max_workers=3) as executor:
            # Submit different feature calculations
            future_basic = executor.submit(calculate_basic_features, df, settings)
            future_advanced = executor.submit(calculate_advanced_features_parallel, df, timeframe_str)
            future_smc = executor.submit(calculate_smc_features_parallel, df, settings)

            # Collect results
            basic_features = future_basic.result()
            advanced_features = future_advanced.result()
            smc_features = future_smc.result()

            # Merge results
            all_features = {**basic_features, **advanced_features, **smc_features}
            return all_features

    except Exception as e:
        logging.warning(f"Parallel feature calculation failed: {e}")
        # Fallback to sequential calculation
        return calculate_advanced_market_features_v3(df, timeframe_str, settings)

def calculate_basic_features(df, settings):
    """Calculate basic technical indicators"""
    features = {}
    try:
        if len(df) >= 20:
            features['sma_20'] = df['close'].rolling(20).mean().iloc[-1]
            features['volume_avg'] = df['volume'].rolling(20).mean().iloc[-1]
            features['volatility'] = df['close'].pct_change().rolling(20).std().iloc[-1]
    except Exception as e:
        logging.debug(f"Basic features calculation error: {e}")
    return features

def calculate_advanced_features_parallel(df, timeframe_str):
    """Calculate advanced features optimized for parallel execution"""
    features = {}
    try:
        if len(df) >= 50:
            # Fibonacci levels (lightweight)
            fib = calculate_fibonacci_levels(df, lookback=30)
            features['fibonacci'] = fib

            # Support/Resistance (optimized)
            sr = calculate_support_resistance_levels(df, window=10)
            features['support_resistance'] = sr

    except Exception as e:
        logging.debug(f"Advanced features calculation error: {e}")
    return features

def calculate_smc_features_parallel(df, settings):
    """Calculate SMC features optimized for parallel execution"""
    features = {}
    try:
        if len(df) >= 30:
            # Order blocks (simplified)
            features['order_blocks'] = []

            # FVG detection (optimized)
            features['fvgs'] = []

            # Volume analysis
            if len(df) >= 20:
                vol_avg = df['volume'].rolling(20).mean().iloc[-1]
                vol_current = df['volume'].iloc[-1]
                if vol_avg > 0:
                    vol_ratio = vol_current / vol_avg
                    features['volume_analysis'] = {
                        'ratio': vol_ratio,
                        'spike': 'High' if vol_ratio > 2.0 else 'Normal'
                    }

    except Exception as e:
        logging.debug(f"SMC features calculation error: {e}")
    return features

# --- V5 PERFORMANCE: Optimized Offline Analysis Functions ---
def analyze_pair_offline(pair_data: Dict[str, pd.DataFrame], pair_symbol: str) -> Dict:
    """V5 SPEED OPTIMIZED: Ultra-fast analysis with minimal overhead"""
    try:
        # SPEED OPTIMIZATION: Quick reference price extraction
        ref_tf_data = pair_data.get(REFERENCE_TIMEFRAME_FOR_PRICE)
        if ref_tf_data is None or ref_tf_data.empty:
            return {'pair': pair_symbol, 'error': 'No reference timeframe data'}

        current_price = ref_tf_data['close'].iloc[-1]

        # SPEED OPTIMIZATION: Skip heavy ATR calculation in Emergency/Discovery mode
        atr_percentage = 1.0  # Default acceptable value

        if not (EMERGENCY_MODE or DISCOVERY_MODE):
            # Only calculate ATR in normal mode
            try:
                main_df = pair_data.get('4h', ref_tf_data)
                if len(main_df) >= 14:
                    # Fast ATR calculation without pandas_ta overhead
                    high_low = main_df['high'] - main_df['low']
                    high_close = abs(main_df['high'] - main_df['close'].shift(1))
                    low_close = abs(main_df['low'] - main_df['close'].shift(1))
                    true_range = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
                    atr = true_range.rolling(14).mean().iloc[-1]
                    atr_percentage = (atr / current_price) * 100 if current_price > 0 else 1.0

                    # Quick ATR filter
                    if atr_percentage < MIN_ATR_PERCENTAGE or atr_percentage > MAX_ATR_PERCENTAGE:
                        return {'pair': pair_symbol, 'error': f'ATR filter: {atr_percentage:.2f}%'}
            except:
                atr_percentage = 1.0

        # SPEED OPTIMIZATION: Ultra-fast multi-timeframe analysis
        tf_scores = {}
        tf_features = {}
        has_volume_confirmation = False

        # SPEED OPTIMIZATION: Process only essential timeframes in Emergency/Discovery mode
        timeframes_to_process = TIMEFRAMES_TO_ANALYZE
        if EMERGENCY_MODE or DISCOVERY_MODE:
            # Use only 2-3 most important timeframes for speed
            timeframes_to_process = ['4h', '1h', '15m'][:len(TIMEFRAMES_TO_ANALYZE)]

        for timeframe in timeframes_to_process:
            if timeframe not in pair_data or pair_data[timeframe].empty:
                continue

            df = pair_data[timeframe]

            try:
                # SPEED OPTIMIZATION: Skip heavy TA library, use fast calculations
                if EMERGENCY_MODE:
                    # Emergency mode: Ultra-fast basic scoring
                    tf_score_data = calculate_fast_score_emergency(df)
                elif DISCOVERY_MODE:
                    # Discovery mode: Fast scoring with essential indicators
                    tf_score_data = calculate_fast_score_discovery(df)
                else:
                    # Normal mode: Full analysis
                    settings = get_key_indicator_settings_for_tf(timeframe)
                    df_ta = df.copy()
                    df_ta = add_all_ta_features(df_ta, open="open", high="high", low="low", close="close", volume="volume", fillna=True)
                    advanced_features = calculate_advanced_features_parallel(df, timeframe)
                    tf_features[timeframe] = advanced_features
                    tf_score_data = calculate_timeframe_score_optimized(df_ta, settings, advanced_features)

                tf_scores[timeframe] = tf_score_data

                # Quick volume check
                if isinstance(tf_score_data, dict):
                    volume_data = tf_score_data.get('volume_data', {})
                    if volume_data.get('volume_ratio', 1.0) >= VOLUME_CONFIRMATION_MULTIPLIER:
                        has_volume_confirmation = True

            except Exception as e:
                logging.debug(f"Error analyzing {pair_symbol} {timeframe}: {e}")
                continue

        if not tf_scores:
            return {'pair': pair_symbol, 'error': 'No valid timeframe analysis'}

        # Calculate final score and signal with enhanced validation
        final_score, signal_type, confidence = calculate_final_signal_v5(tf_scores, tf_features, current_price)

        if DEBUG_FILTERING:
            logging.info(f"SIGNAL CALCULATION: {pair_symbol} score={final_score:.3f}, type={signal_type}, confidence={confidence:.3f}")

        # 2. Confidence Filter - Emergency Mode Adaptive
        if EMERGENCY_MODE:
            # Emergency Mode: Very permissive confidence threshold - BALANCED
            if confidence < 0.15:  # Raised from 0.10 to 0.15 for better quality
                if DEBUG_FILTERING:
                    logging.info(f"EMERGENCY CONFIDENCE FILTER: {pair_symbol} confidence {confidence:.2f} < 15%")
                return {'pair': pair_symbol, 'error': f'Emergency: Confidence {confidence:.2f} below 15%'}
        elif DISCOVERY_MODE:
            # Discovery Mode: Very permissive confidence threshold - LOWERED
            if confidence < 0.25:  # Lowered from 0.40 to 0.25 for more opportunities
                if DEBUG_FILTERING:
                    logging.info(f"DISCOVERY CONFIDENCE FILTER: {pair_symbol} confidence {confidence:.2f} < 25%")
                return {'pair': pair_symbol, 'error': f'Discovery: Confidence {confidence:.2f} below 25%'}
        else:
            # Normal Mode: Standard threshold
            if confidence < MIN_CONFIDENCE_THRESHOLD:
                if DEBUG_FILTERING:
                    logging.info(f"NORMAL CONFIDENCE FILTER: {pair_symbol} confidence {confidence:.2f} < {MIN_CONFIDENCE_THRESHOLD}")
                return {'pair': pair_symbol, 'error': f'Confidence {confidence:.2f} below threshold {MIN_CONFIDENCE_THRESHOLD}'}

        # 3. Confluence Filter - Emergency Mode Adaptive
        total_aligned = 0
        for tf, score_data in tf_scores.items():
            if isinstance(score_data, dict):
                total_aligned += score_data.get('aligned_indicators', 0)

        if EMERGENCY_MODE:
            # Emergency Mode: No confluence requirement
            min_required_confluence = 0  # No requirement in emergency mode
        elif DISCOVERY_MODE:
            # Discovery Mode: Minimal confluence requirement
            min_required_confluence = max(1, len(tf_scores))  # At least 1 indicator per timeframe
        else:
            # Normal Mode: Standard confluence requirement
            min_required_confluence = 2 * len(tf_scores)  # At least 2 indicators per timeframe

        if total_aligned < min_required_confluence:
            if DEBUG_FILTERING:
                logging.info(f"CONFLUENCE FILTER: {pair_symbol} aligned {total_aligned} < required {min_required_confluence}")
            return {'pair': pair_symbol, 'error': f'Insufficient confluence: {total_aligned} < {min_required_confluence}'}

        # 4. Volume Confirmation Requirement - Emergency Mode Adaptive
        if EMERGENCY_MODE:
            # Emergency Mode: No volume penalty at all
            volume_penalty = 1.0  # No penalty in emergency mode
        elif DISCOVERY_MODE:
            # Discovery Mode: No volume penalty, just track status
            volume_penalty = 1.0  # No penalty in discovery mode
        else:
            # Normal Mode: Apply volume penalty
            volume_penalty = 1.0 if has_volume_confirmation else 0.8

        adjusted_confidence = confidence * volume_penalty

        return {
            'pair': pair_symbol,
            'signal_type': signal_type,
            'score': final_score,
            'confidence': adjusted_confidence,
            'current_price': current_price,
            'timeframe_scores': tf_scores,
            'features': tf_features,
            'atr_percentage': atr_percentage,
            'volume_confirmed': has_volume_confirmation,
            'total_aligned_indicators': total_aligned
        }

    except Exception as e:
        logging.error(f"Error analyzing pair {pair_symbol}: {e}")
        return {'pair': pair_symbol, 'error': str(e)}

# --- SPEED OPTIMIZATION: Fast scoring functions for Emergency/Discovery modes ---
def calculate_fast_score_emergency(df: pd.DataFrame) -> Dict:
    """Ultra-fast scoring for Emergency mode - minimal calculations"""
    try:
        if len(df) < 10:
            return {'score': 0.0, 'aligned_indicators': 0, 'volume_data': {}}

        # Only essential calculations
        current_price = df['close'].iloc[-1]
        prev_price = df['close'].iloc[-2]
        price_change = (current_price - prev_price) / prev_price if prev_price > 0 else 0

        # Simple volume check
        current_volume = df['volume'].iloc[-1] if 'volume' in df.columns else 1
        avg_volume = df['volume'].rolling(5).mean().iloc[-1] if 'volume' in df.columns and len(df) >= 5 else 1
        volume_ratio = current_volume / avg_volume if avg_volume > 0 else 1.0

        # Ultra-simple scoring based on price momentum
        score = price_change * 10  # Amplify for scoring
        aligned_indicators = 1 if abs(score) > 0.01 else 0

        return {
            'score': score,
            'aligned_indicators': aligned_indicators,
            'volume_data': {'volume_ratio': volume_ratio},
            'indicators': {'price_change': price_change}
        }
    except:
        return {'score': 0.0, 'aligned_indicators': 0, 'volume_data': {}}

def calculate_fast_score_discovery(df: pd.DataFrame) -> Dict:
    """Fast scoring for Discovery mode - essential indicators only"""
    try:
        if len(df) < 20:
            return {'score': 0.0, 'aligned_indicators': 0, 'volume_data': {}}

        current_price = df['close'].iloc[-1]

        # Fast RSI calculation (simplified)
        price_changes = df['close'].diff()
        gains = price_changes.where(price_changes > 0, 0).rolling(14).mean()
        losses = (-price_changes.where(price_changes < 0, 0)).rolling(14).mean()
        rs = gains / losses
        rsi = 100 - (100 / (1 + rs))
        current_rsi = rsi.iloc[-1] if not pd.isna(rsi.iloc[-1]) else 50

        # Fast EMA calculation
        ema_short = df['close'].ewm(span=9).mean().iloc[-1]
        ema_long = df['close'].ewm(span=21).mean().iloc[-1]

        # Volume analysis
        current_volume = df['volume'].iloc[-1] if 'volume' in df.columns else 1
        avg_volume = df['volume'].rolling(10).mean().iloc[-1] if 'volume' in df.columns and len(df) >= 10 else 1
        volume_ratio = current_volume / avg_volume if avg_volume > 0 else 1.0

        # Simple scoring
        signals = []

        # RSI signals
        if current_rsi < 30:
            signals.append(1.0)  # Oversold - bullish
        elif current_rsi > 70:
            signals.append(-1.0)  # Overbought - bearish
        else:
            signals.append(0.0)

        # EMA signals
        if current_price > ema_short > ema_long:
            signals.append(1.0)  # Bullish trend
        elif current_price < ema_short < ema_long:
            signals.append(-1.0)  # Bearish trend
        else:
            signals.append(0.0)

        # Volume signal
        if volume_ratio > 1.5:
            signals.append(0.5)  # Volume confirmation
        else:
            signals.append(0.0)

        score = sum(signals) / len(signals) if signals else 0.0
        aligned_indicators = sum(1 for s in signals if abs(s) > 0.1)

        return {
            'score': score,
            'aligned_indicators': aligned_indicators,
            'volume_data': {'volume_ratio': volume_ratio},
            'indicators': {
                'RSI_14': current_rsi,
                'EMA_9': ema_short,
                'EMA_21': ema_long
            }
        }
    except:
        return {'score': 0.0, 'aligned_indicators': 0, 'volume_data': {}}

# --- V5 Enhanced: Advanced Pattern Recognition ---
def detect_candlestick_patterns(df: pd.DataFrame) -> Dict[str, bool]:
    """Detect classic candlestick patterns"""
    patterns = {}
    try:
        if len(df) < 3:
            return patterns

        # Get last 3 candles
        current = df.iloc[-1]
        prev1 = df.iloc[-2]
        prev2 = df.iloc[-3] if len(df) >= 3 else None

        # Doji pattern
        body_size = abs(current['close'] - current['open'])
        candle_range = current['high'] - current['low']
        patterns['doji'] = body_size < (candle_range * 0.1) if candle_range > 0 else False

        # Hammer pattern
        lower_shadow = current['open'] - current['low'] if current['close'] > current['open'] else current['close'] - current['low']
        upper_shadow = current['high'] - current['open'] if current['close'] > current['open'] else current['high'] - current['close']
        patterns['hammer'] = lower_shadow > (body_size * 2) and upper_shadow < (body_size * 0.5)

        # Engulfing patterns
        if prev1 is not None:
            bullish_engulfing = (current['close'] > current['open'] and
                               prev1['close'] < prev1['open'] and
                               current['close'] > prev1['open'] and
                               current['open'] < prev1['close'])
            bearish_engulfing = (current['close'] < current['open'] and
                               prev1['close'] > prev1['open'] and
                               current['close'] < prev1['open'] and
                               current['open'] > prev1['close'])
            patterns['bullish_engulfing'] = bullish_engulfing
            patterns['bearish_engulfing'] = bearish_engulfing

    except Exception as e:
        logging.debug(f"Error detecting candlestick patterns: {e}")

    return patterns

def calculate_confluence_score(indicators: Dict) -> Tuple[float, int]:
    """Calculate confluence score based on indicator alignment"""
    bullish_signals = 0
    bearish_signals = 0
    total_signals = 0

    for indicator, value in indicators.items():
        if isinstance(value, (int, float)):
            total_signals += 1
            if value > 0.5:
                bullish_signals += 1
            elif value < -0.5:
                bearish_signals += 1

    if total_signals == 0:
        return 0.0, 0

    # Calculate confluence strength
    max_aligned = max(bullish_signals, bearish_signals)
    confluence_ratio = max_aligned / total_signals

    # Emergency Mode: Adaptive confluence requirements - LOWERED FOR MORE OPPORTUNITIES
    if EMERGENCY_MODE:
        # Emergency Mode: No confluence requirement - any signal is valid
        min_required = 0  # No requirement in emergency mode
    elif DISCOVERY_MODE:
        # Discovery Mode: EXTREMELY permissive - LOWERED
        min_required = max(0, total_signals // 5)  # Lowered from //3 to //5 for more opportunities
    else:
        # Normal Mode: Relaxed confluence requirement - LOWERED
        min_required = max(1, total_signals // 3)  # Lowered for more opportunities

    if max_aligned >= min_required:
        confluence_score = confluence_ratio * (1.0 if bullish_signals > bearish_signals else -1.0)
    else:
        confluence_score = 0.0  # Not enough confluence

    return confluence_score, max_aligned

def calculate_timeframe_score_optimized(df_ta: pd.DataFrame, settings: Dict, advanced_features: Dict) -> Dict:
    """V5 Enhanced: Calculate comprehensive score with confluence analysis"""
    try:
        indicators = {}
        volume_data = {}
        pattern_data = {}

        current_price = df_ta['close'].iloc[-1]

        # --- Trend Indicators (40% weight) ---
        trend_score = 0.0
        trend_signals = {}

        # RSI analysis with divergence detection
        if 'RSI_14' in df_ta.columns:
            rsi = df_ta['RSI_14'].iloc[-1]
            if rsi < settings.get('rsi_oversold', 30):
                trend_signals['rsi'] = 1.0
            elif rsi > settings.get('rsi_overbought', 70):
                trend_signals['rsi'] = -1.0
            else:
                trend_signals['rsi'] = 0.0
            indicators['RSI_14'] = rsi

        # MACD analysis with crossover detection
        if all(col in df_ta.columns for col in ['MACD_12_26_9', 'MACDs_12_26_9', 'MACDh_12_26_9']):
            macd = df_ta['MACD_12_26_9'].iloc[-1]
            macd_signal = df_ta['MACDs_12_26_9'].iloc[-1]
            macd_hist = df_ta['MACDh_12_26_9'].iloc[-1]

            if macd > macd_signal and macd_hist > 0:
                trend_signals['macd'] = 1.0
            elif macd < macd_signal and macd_hist < 0:
                trend_signals['macd'] = -1.0
            else:
                trend_signals['macd'] = 0.0

            indicators.update({'MACD': macd, 'MACD_Signal': macd_signal, 'MACD_Histogram': macd_hist})

        # EMA trend analysis (21, 55, 200)
        ema_cols = ['EMA_21', 'EMA_55', 'EMA_200']
        available_emas = [col for col in ema_cols if col in df_ta.columns]

        if len(available_emas) >= 2:
            ema_values = [df_ta[col].iloc[-1] for col in available_emas]
            indicators.update({col: df_ta[col].iloc[-1] for col in available_emas})

            # Check EMA alignment
            if all(current_price > ema for ema in ema_values) and all(ema_values[i] > ema_values[i+1] for i in range(len(ema_values)-1)):
                trend_signals['ema_alignment'] = 1.0
            elif all(current_price < ema for ema in ema_values) and all(ema_values[i] < ema_values[i+1] for i in range(len(ema_values)-1)):
                trend_signals['ema_alignment'] = -1.0
            else:
                trend_signals['ema_alignment'] = 0.0

        # Bollinger Bands analysis
        if all(col in df_ta.columns for col in ['BBL_20_2.0', 'BBM_20_2.0', 'BBU_20_2.0']):
            bb_lower = df_ta['BBL_20_2.0'].iloc[-1]
            bb_middle = df_ta['BBM_20_2.0'].iloc[-1]
            bb_upper = df_ta['BBU_20_2.0'].iloc[-1]

            bb_position = (current_price - bb_lower) / (bb_upper - bb_lower) if bb_upper != bb_lower else 0.5

            if bb_position > 0.8:
                trend_signals['bollinger'] = -0.5  # Near upper band - potential reversal
            elif bb_position < 0.2:
                trend_signals['bollinger'] = 0.5   # Near lower band - potential reversal
            else:
                trend_signals['bollinger'] = 0.0

            indicators.update({'BB_Lower': bb_lower, 'BB_Middle': bb_middle, 'BB_Upper': bb_upper, 'BB_Position': bb_position})

        # --- Momentum Indicators (30% weight) ---
        momentum_signals = {}

        # Stochastic analysis
        if all(col in df_ta.columns for col in ['STOCHk_14_3_3', 'STOCHd_14_3_3']):
            stoch_k = df_ta['STOCHk_14_3_3'].iloc[-1]
            stoch_d = df_ta['STOCHd_14_3_3'].iloc[-1]

            if stoch_k < 20 and stoch_d < 20:
                momentum_signals['stochastic'] = 1.0
            elif stoch_k > 80 and stoch_d > 80:
                momentum_signals['stochastic'] = -1.0
            else:
                momentum_signals['stochastic'] = 0.0

            indicators.update({'Stoch_K': stoch_k, 'Stoch_D': stoch_d})

        # Williams %R
        if 'WILLR_14' in df_ta.columns:
            willr = df_ta['WILLR_14'].iloc[-1]
            if willr < -80:
                momentum_signals['williams_r'] = 1.0
            elif willr > -20:
                momentum_signals['williams_r'] = -1.0
            else:
                momentum_signals['williams_r'] = 0.0

            indicators['Williams_R'] = willr

        # CCI analysis
        if 'CCI_14_0.015' in df_ta.columns:
            cci = df_ta['CCI_14_0.015'].iloc[-1]
            if cci < -100:
                momentum_signals['cci'] = 1.0
            elif cci > 100:
                momentum_signals['cci'] = -1.0
            else:
                momentum_signals['cci'] = 0.0

            indicators['CCI'] = cci

        # --- Volume Analysis (20% weight) ---
        volume_signals = {}

        if 'volume' in df_ta.columns and len(df_ta) >= 20:
            current_volume = df_ta['volume'].iloc[-1]
            avg_volume_20 = df_ta['volume'].rolling(20).mean().iloc[-1]

            volume_ratio = current_volume / avg_volume_20 if avg_volume_20 > 0 else 1.0
            volume_data['current_volume'] = current_volume
            volume_data['avg_volume_20'] = avg_volume_20
            volume_data['volume_ratio'] = volume_ratio

            # Volume confirmation
            if volume_ratio >= VOLUME_CONFIRMATION_MULTIPLIER:
                volume_signals['volume_confirmation'] = 1.0
            else:
                volume_signals['volume_confirmation'] = 0.0

        # --- Pattern Recognition (15% weight) ---
        pattern_signals = {}
        candlestick_patterns = detect_candlestick_patterns(df_ta)
        pattern_data['candlestick_patterns'] = candlestick_patterns

        # Score candlestick patterns
        if candlestick_patterns.get('bullish_engulfing') or candlestick_patterns.get('hammer'):
            pattern_signals['bullish_patterns'] = 1.0
        elif candlestick_patterns.get('bearish_engulfing'):
            pattern_signals['bearish_patterns'] = -1.0
        else:
            pattern_signals['neutral_patterns'] = 0.0

        # --- SMC Analysis (10% weight) ---
        smc_signals = {}
        if 'trend_strength' in advanced_features:
            smc_signals['trend_strength'] = advanced_features['trend_strength']

        # --- Calculate Confluence Scores ---
        all_signals = {**trend_signals, **momentum_signals, **volume_signals, **pattern_signals, **smc_signals}
        confluence_score, aligned_count = calculate_confluence_score(all_signals)

        # --- Weight Distribution ---
        trend_weight = 0.40
        momentum_weight = 0.30
        volume_weight = 0.20
        pattern_weight = 0.15
        smc_weight = 0.10

        # Calculate weighted scores
        trend_score = sum(trend_signals.values()) * trend_weight
        momentum_score = sum(momentum_signals.values()) * momentum_weight
        volume_score = sum(volume_signals.values()) * volume_weight
        pattern_score = sum(pattern_signals.values()) * pattern_weight
        smc_score = sum(smc_signals.values()) * smc_weight

        # Final score with confluence bonus/penalty
        base_score = trend_score + momentum_score + volume_score + pattern_score + smc_score
        final_score = base_score * (1.0 + confluence_score * 0.5)  # Confluence bonus up to 50%

        return {
            'score': final_score,
            'confluence_score': confluence_score,
            'aligned_indicators': aligned_count,
            'indicators': indicators,
            'volume_data': volume_data,
            'pattern_data': pattern_data,
            'component_scores': {
                'trend': trend_score,
                'momentum': momentum_score,
                'volume': volume_score,
                'pattern': pattern_score,
                'smc': smc_score
            },
            'signals': all_signals
        }

    except Exception as e:
        logging.debug(f"Error calculating enhanced timeframe score: {e}")
        return {
            'score': 0.0,
            'confluence_score': 0.0,
            'aligned_indicators': 0,
            'indicators': {},
            'volume_data': {},
            'pattern_data': {},
            'component_scores': {},
            'signals': {}
        }

def calculate_risk_reward_ratio(current_price: float, signal_type: str, support_resistance: Dict) -> float:
    """Calculate risk-reward ratio based on support/resistance levels"""
    try:
        if signal_type == "BUY":
            # For BUY: Risk = current_price - support, Reward = resistance - current_price
            support_level = support_resistance.get('nearest_support', current_price * 0.98)
            resistance_level = support_resistance.get('nearest_resistance', current_price * 1.02)

            risk = current_price - support_level
            reward = resistance_level - current_price

        elif signal_type == "SELL":
            # For SELL: Risk = resistance - current_price, Reward = current_price - support
            support_level = support_resistance.get('nearest_support', current_price * 0.98)
            resistance_level = support_resistance.get('nearest_resistance', current_price * 1.02)

            risk = resistance_level - current_price
            reward = current_price - support_level

        else:
            return 0.0

        if risk <= 0:
            return 0.0

        rrr = reward / risk
        return max(0.0, rrr)

    except Exception as e:
        logging.debug(f"Error calculating RRR: {e}")
        return 0.0

def validate_volume_confirmation(volume_data: Dict) -> bool:
    """Validate if volume confirms the signal"""
    try:
        volume_ratio = volume_data.get('volume_ratio', 1.0)
        return volume_ratio >= VOLUME_CONFIRMATION_MULTIPLIER
    except:
        return False

def validate_market_structure_alignment(tf_scores: Dict, higher_tf_trend: str) -> bool:
    """Validate if signal aligns with higher timeframe trend"""
    try:
        # Get 4h and 1h trends
        h4_score = tf_scores.get('4h', 0.0)
        h1_score = tf_scores.get('1h', 0.0)

        # Determine higher timeframe trend
        if isinstance(h4_score, dict):
            h4_trend = "BULLISH" if h4_score.get('score', 0) > 0 else "BEARISH"
        else:
            h4_trend = "BULLISH" if h4_score > 0 else "BEARISH"

        if isinstance(h1_score, dict):
            h1_trend = "BULLISH" if h1_score.get('score', 0) > 0 else "BEARISH"
        else:
            h1_trend = "BULLISH" if h1_score > 0 else "BEARISH"

        # Check alignment
        return h4_trend == h1_trend == higher_tf_trend

    except:
        return True  # Default to True if can't validate

def calculate_final_signal_v5(tf_scores: Dict, tf_features: Dict, current_price: float) -> Tuple[float, str, float]:
    """V5 Enhanced: Calculate final signal with advanced validation and confluence analysis"""
    try:
        # Weight timeframes by importance
        tf_weights = {
            '4h': 0.35,   # Higher timeframe - most important
            '1h': 0.25,   # Medium-high importance
            '30m': 0.20,  # Medium importance
            '15m': 0.15,  # Lower importance
            '5m': 0.05    # Lowest importance - just for entry timing
        }

        weighted_score = 0.0
        total_weight = 0.0
        confidence_sum = 0.0
        valid_timeframes = 0
        total_confluence = 0.0
        total_aligned = 0
        volume_confirmed = False

        # Collect volume data and confluence scores
        volume_data_collection = {}

        for tf, score_data in tf_scores.items():
            if tf in tf_weights:
                weight = tf_weights[tf]

                # Handle both old format (float) and new format (dict)
                if isinstance(score_data, dict):
                    score = score_data.get('score', 0.0)
                    confluence = score_data.get('confluence_score', 0.0)
                    aligned = score_data.get('aligned_indicators', 0)
                    volume_data = score_data.get('volume_data', {})

                    # Check volume confirmation
                    if validate_volume_confirmation(volume_data):
                        volume_confirmed = True
                        volume_data_collection[tf] = volume_data

                    total_confluence += confluence * weight
                    total_aligned += aligned

                else:
                    score = score_data
                    confluence = 0.0
                    aligned = 0

                if score != 0:
                    weighted_score += score * weight
                    total_weight += weight

                    # Calculate confidence based on score strength and confluence
                    base_confidence = min(abs(score) / 3.0, 1.0)
                    confluence_bonus = abs(confluence) * 0.3  # Up to 30% bonus from confluence
                    confidence_sum += (base_confidence + confluence_bonus) * weight
                    valid_timeframes += 1

        if total_weight == 0 or valid_timeframes == 0:
            return 0.0, "NETRAL", 0.0

        # Normalize scores
        final_score = weighted_score / total_weight if total_weight > 0 else 0.0
        base_confidence = confidence_sum / total_weight if total_weight > 0 else 0.0
        avg_confluence = total_confluence / total_weight if total_weight > 0 else 0.0

        # Determine signal direction
        if final_score > 0.5:
            signal_type = "BUY"
        elif final_score < -0.5:
            signal_type = "SELL"
        else:
            signal_type = "NETRAL"

        # --- V5 Enhanced Validation ---

        # 1. Volume Confirmation Requirement
        if not volume_confirmed and signal_type != "NETRAL":
            # Reduce confidence if no volume confirmation
            base_confidence *= 0.7

        # 2. Market Structure Alignment
        higher_tf_trend = "BULLISH" if final_score > 0 else "BEARISH"
        if not validate_market_structure_alignment(tf_scores, higher_tf_trend):
            # Reduce confidence if not aligned with higher timeframes
            base_confidence *= 0.8

        # 3. Confluence Requirement (relaxed: minimum 2-3 indicators aligned)
        min_required_aligned = 2  # Reduced from 3 to 2
        if total_aligned < min_required_aligned and signal_type != "NETRAL":
            # Reduce confidence if insufficient confluence
            base_confidence *= 0.7  # Less penalty: 0.7 instead of 0.6

        # 4. Risk-Reward Ratio Validation
        # Note: This would require support/resistance data which we'll implement later
        # For now, we'll use a placeholder
        rrr = 2.0  # Placeholder - assume good RRR
        if rrr < MIN_RRR_RATIO:
            base_confidence *= 0.5

        # 5. Apply Enhanced Confidence Threshold
        final_confidence = min(base_confidence, 1.0)

        # Apply confidence threshold - Emergency Mode Adaptive - LOWERED FOR MORE OPPORTUNITIES
        if EMERGENCY_MODE:
            # Emergency Mode: Very permissive threshold - BALANCED
            if final_confidence < 0.12:  # Raised from 0.05 to 0.12 for better quality
                return 0.0, "NETRAL", final_confidence
        elif DISCOVERY_MODE:
            # Discovery Mode: Very permissive threshold - LOWERED
            if final_confidence < 0.20:  # Lowered from 0.35 to 0.20 for more opportunities
                return 0.0, "NETRAL", final_confidence
        else:
            # Normal Mode: Standard threshold
            if final_confidence < MIN_CONFIDENCE_THRESHOLD:
                return 0.0, "NETRAL", final_confidence

        # 6. Confluence Bonus
        if avg_confluence > 0.7:  # Strong confluence
            final_confidence = min(final_confidence * 1.2, 1.0)

        return final_score, signal_type, final_confidence

    except Exception as e:
        logging.debug(f"Error calculating enhanced final signal V5: {e}")
        return 0.0, "NETRAL", 0.0

# --- Kalkulasi Fitur Pasar Lanjutan (Diperbarui untuk V3) ---
def calculate_advanced_market_features_v3(df_input, timeframe_str, settings):
    df = df_input.copy()
    required_data_length = max(settings.get('ema_long', 50), settings.get('adx_period',14)*2, 60, settings.get('zigzag_len',10)*3)
    if df.empty or len(df) < required_data_length:
        logging.warning(f"Data tidak cukup ({len(df)}/{required_data_length}) untuk fitur V3 di TF {timeframe_str}")
        return df, {}, {'divergences': [], 'order_blocks': [], 'fvgs': [], 'market_structure': [], 'volume_analysis': {}, 'liquidity_sweeps': []}

    last_values = {}; features = {'divergences': [], 'order_blocks': [], 'fvgs': [], 'market_structure': [], 'volume_analysis': {}, 'liquidity_sweeps': []}
    
    try: 
        df.ta.rsi(length=settings['rsi_period'], append=True, col_names=('S_RSI',))
        df.ta.macd(fast=settings['macd_fast'], slow=settings['macd_slow'], signal=settings['macd_signal'], append=True, col_names=('S_MACD_line', 'S_MACD_hist', 'S_MACD_signal'))
        df.ta.atr(length=settings['atr_period'], append=True, col_names=('S_ATR',))
        df.ta.ema(length=settings['ema_short'], append=True, col_names=(f"S_EMA_{settings['ema_short']}",))
        df.ta.ema(length=settings['ema_medium'], append=True, col_names=(f"S_EMA_{settings['ema_medium']}",))
        df.ta.ema(length=settings['ema_long'], append=True, col_names=(f"S_EMA_{settings['ema_long']}",))
        # V4 Enhanced: Robust VWAP calculation without pandas_ta dependency
        try:
            # Manual VWAP calculation that always works
            typical_price = (df['high'] + df['low'] + df['close']) / 3
            vwap_period = settings.get('vwap_period', 20)

            # Calculate cumulative VWAP
            pv = typical_price * df['volume']
            cumulative_pv = pv.rolling(window=vwap_period, min_periods=1).sum()
            cumulative_volume = df['volume'].rolling(window=vwap_period, min_periods=1).sum()

            # Avoid division by zero
            df['S_VWAP'] = cumulative_pv / cumulative_volume.replace(0, 1)

        except Exception as vwap_error:
            logging.warning(f"VWAP calculation failed for TF {timeframe_str}: {vwap_error}")
            # Ultimate fallback: Use close price
            df['S_VWAP'] = df['close']
        df['S_VOL_SMA'] = df['volume'].rolling(window=settings.get('volume_sma_period', 20)).mean()
        df.ta.bbands(length=settings['bb_period'], std=settings['bb_std_dev'], append=True, col_names=(f"S_BB_L", f"S_BB_M", f"S_BB_U", f"S_BB_B", f"S_BB_P")) 
        df.ta.adx(length=settings['adx_period'], append=True, col_names=(f"S_ADX", f"S_ADX_DMP", f"S_ADX_DMN"))
    except Exception as e:
        logging.error(f"Error calculating base indicators for V3 features TF {timeframe_str}: {e}", exc_info=True)
        return df, {}, features

    for col in ['S_RSI', 'S_MACD_hist', 'S_ATR', f"S_EMA_{settings['ema_short']}", f"S_EMA_{settings['ema_medium']}", f"S_EMA_{settings['ema_long']}", 'S_VWAP', 'S_VOL_SMA', 'S_BB_P', 'S_ADX', 'S_ADX_DMP', 'S_ADX_DMN']:
        if col in df.columns and pd.notna(df[col].iloc[-1]): last_values[col.lower().replace('s_','')] = df[col].iloc[-1]
    last_values.update({'price_close': df['close'].iloc[-1], 'price_high': df['high'].iloc[-1], 'price_low': df['low'].iloc[-1], 'price_open': df['open'].iloc[-1]})

    last_volume = df['volume'].iloc[-1]; avg_volume = last_values.get('vol_sma')
    if pd.notna(last_volume) and pd.notna(avg_volume) and avg_volume > 0:
        volume_ratio = last_volume / avg_volume; features['volume_analysis']['last_vs_avg_ratio'] = round(volume_ratio,2)
        if volume_ratio > 2.5: features['volume_analysis']['spike'] = "Sangat Tinggi"; features['volume_analysis']['spike_raw'] = last_volume
        elif volume_ratio > 1.7: features['volume_analysis']['spike'] = "Tinggi"; features['volume_analysis']['spike_raw'] = last_volume
        else: features['volume_analysis']['spike'] = "Normal"
    
    sweep_lookback = 10 
    for i in range(len(df) - sweep_lookback, len(df)):
        if i < 1: continue
        current_high = df['high'].iloc[i]; current_low = df['low'].iloc[i]
        prev_high_max = df['high'].iloc[max(0, i - sweep_lookback) : i].max()
        prev_low_min = df['low'].iloc[max(0, i - sweep_lookback) : i].min()
        atr_val_sweep = df['S_ATR'].iloc[i] if pd.notna(df['S_ATR'].iloc[i]) else 0
        if current_high > prev_high_max and df['close'].iloc[i] < current_high - (atr_val_sweep * 0.3):
            features['liquidity_sweeps'].append({'type': 'Sweep High (Bearish Indication)', 'level': round(current_high,5), 'timestamp': df.index[i]})
        if current_low < prev_low_min and df['close'].iloc[i] > current_low + (atr_val_sweep * 0.3): 
            features['liquidity_sweeps'].append({'type': 'Sweep Low (Bullish Indication)', 'level': round(current_low,5), 'timestamp': df.index[i]})
    features['liquidity_sweeps'] = features['liquidity_sweeps'][-3:] 

    # V4 Fix: Simple market structure detection without zigzag dependency
    try:
        zigzag_len = settings.get('zigzag_len', 8)
        current_close = df['close'].iloc[-1]

        # Simple swing high/low detection
        if len(df) >= zigzag_len * 2:
            recent_highs = df['high'].rolling(window=zigzag_len).max()
            recent_lows = df['low'].rolling(window=zigzag_len).min()

            # Check for break of structure patterns
            if len(recent_highs) >= 3 and len(recent_lows) >= 3:
                prev_high = recent_highs.iloc[-2]
                prev_low = recent_lows.iloc[-2]

                # Simple BoS detection
                if current_close > prev_high:
                    features['market_structure'].append({'type': 'Bullish BoS', 'level': round(prev_high,5), 'timestamp': df.index[-1]})
                elif current_close < prev_low:
                    features['market_structure'].append({'type': 'Bearish BoS', 'level': round(prev_low,5), 'timestamp': df.index[-1]})

                features['market_structure'] = features['market_structure'][-2:]
    except Exception as ms_error:
        logging.warning(f"Market structure detection failed: {ms_error}")
        features['market_structure'] = []

    min_body_ob_factor = 0.4; breakout_ob_factor = 0.8; fvg_threshold_factor = 0.25; max_ob_age = 30
    if 'S_ATR' in df.columns and pd.notna(df['S_ATR'].mean()) and 'S_VOL_SMA' in df.columns:
        df['candle_range'] = df['high'] - df['low']; df['body_size'] = abs(df['close'] - df['open']); 
        df['is_bullish_candle'] = df['close'] > df['open']; df['is_bearish_candle'] = df['close'] < df['open']
        for i in range(len(df) - 3, max(0, len(df) - 3 - max_ob_age), -1):
            if i-1 < 0 or i+1 >= len(df): continue
            ob_candidate = df.iloc[i]; prev_candle = df.iloc[i-1]; next_candle = df.iloc[i+1]
            atr_at_ob = df['S_ATR'].iloc[i] if pd.notna(df['S_ATR'].iloc[i]) else df['S_ATR'].mean()
            if pd.isna(atr_at_ob) or atr_at_ob == 0 or ob_candidate['candle_range'] == 0: continue
            swept_liquidity_bullish = ob_candidate['low'] < df['low'].iloc[max(0, i - 5) : i].min() if i >= 5 else False
            swept_liquidity_bearish = ob_candidate['high'] > df['high'].iloc[max(0, i - 5) : i].max() if i >= 5 else False
            volume_spike_at_ob = ob_candidate['volume'] > df['S_VOL_SMA'].iloc[i] * 1.8 if pd.notna(df['S_VOL_SMA'].iloc[i]) and df['S_VOL_SMA'].iloc[i] > 0 else False
            vol_next_candle_conf = next_candle['volume'] > df['S_VOL_SMA'].iloc[i+1] * 1.2 if pd.notna(df['S_VOL_SMA'].iloc[i+1]) and df['S_VOL_SMA'].iloc[i+1] > 0 else False
            if ob_candidate['is_bearish_candle'] and (ob_candidate['body_size'] / ob_candidate['candle_range'] >= min_body_ob_factor if ob_candidate['candle_range'] > 0 else False) and \
               next_candle['close'] > ob_candidate['high'] and (next_candle['high'] - ob_candidate['high']) > (atr_at_ob * breakout_ob_factor) and vol_next_candle_conf:
                fvg_bullish = prev_candle['high'] < next_candle['low'] and (next_candle['low'] - prev_candle['high']) > (atr_at_ob * fvg_threshold_factor)
                strength = 'strong' if (swept_liquidity_bullish and volume_spike_at_ob) else ('medium' if (swept_liquidity_bullish or volume_spike_at_ob or fvg_bullish) else 'weak')
                features['order_blocks'].append({'type': 'Bullish OB', 'timestamp': ob_candidate.name, 'zone': (round(ob_candidate['low'], 5), round(ob_candidate['high'], 5)), 'fvg_nearby': fvg_bullish, 'sweep_before': swept_liquidity_bullish, 'volume_spike_ob': volume_spike_at_ob, 'strength': strength}); break
            if ob_candidate['is_bullish_candle'] and (ob_candidate['body_size'] / ob_candidate['candle_range'] >= min_body_ob_factor if ob_candidate['candle_range'] > 0 else False) and \
               next_candle['close'] < ob_candidate['low'] and (ob_candidate['low'] - next_candle['low']) > (atr_at_ob * breakout_ob_factor) and vol_next_candle_conf:
                fvg_bearish = prev_candle['low'] > next_candle['high'] and (prev_candle['low'] - next_candle['high']) > (atr_at_ob * fvg_threshold_factor)
                strength = 'strong' if (swept_liquidity_bearish and volume_spike_at_ob) else ('medium' if (swept_liquidity_bearish or volume_spike_at_ob or fvg_bearish) else 'weak')
                features['order_blocks'].append({'type': 'Bearish OB', 'timestamp': ob_candidate.name, 'zone': (round(ob_candidate['low'], 5), round(ob_candidate['high'], 5)), 'fvg_nearby': fvg_bearish, 'sweep_before': swept_liquidity_bearish, 'volume_spike_ob': volume_spike_at_ob, 'strength': strength}); break
        for i in range(1, len(df) - 1):
            if i-1 < 0 or i+1 >= len(df): continue
            prev_c = df.iloc[i-1]; current_c = df.iloc[i]; next_c = df.iloc[i+1]; atr_val = df['S_ATR'].iloc[i] if pd.notna(df['S_ATR'].iloc[i]) else df['S_ATR'].mean()
            if pd.isna(atr_val) or atr_val == 0: continue
            if next_c['low'] > prev_c['high'] and (next_c['low'] - prev_c['high']) > (atr_val * fvg_threshold_factor): features['fvgs'].append({'type': 'Bullish FVG', 'zone': (round(prev_c['high'],5), round(next_c['low'],5)), 'mitigated': False, 'timestamp': current_c.name})
            if prev_c['low'] > next_c['high'] and (prev_c['low'] - next_c['high']) > (atr_val * fvg_threshold_factor): features['fvgs'].append({'type': 'Bearish FVG', 'zone': (round(next_c['high'],5), round(prev_c['low'],5)), 'mitigated': False, 'timestamp': current_c.name})
        if last_values.get('price_close') is not None:
            current_price_close = last_values['price_close']
            for fvg in features['fvgs']:
                if not fvg['mitigated']:
                    if fvg['type'] == 'Bullish FVG' and current_price_close <= fvg['zone'][1] and current_price_close >= fvg['zone'][0]: fvg['mitigated'] = True
                    elif fvg['type'] == 'Bearish FVG' and current_price_close >= fvg['zone'][0] and current_price_close <= fvg['zone'][1]: fvg['mitigated'] = True
    features['order_blocks'] = features['order_blocks'][:1]; features['fvgs'] = features['fvgs'][-2:]
    return df, last_values, features

# --- Interpretasi Indikator Luas (Disesuaikan untuk V3) ---
def get_indicator_category_and_interpretation_v3(indicator_name, indicator_value, df_full_ta, current_price, settings, last_values_local):
    name_lower = indicator_name.lower(); category = 'others'; signal_text = "Netral Sistem"
    if "volume_" in name_lower or name_lower in ["mfi", "cmf", "efi", "obv", "vpt", "vwap", "nvi", "vol_sma"]: category = 'volume_strength'
    elif "volatility_" in name_lower or name_lower in ["atr", "ui", "bbp"] or "bb_" in name_lower or "kc_" in name_lower or "dc_" in name_lower: category = 'volatility_context'
    elif "trend_" in name_lower or "ema_" in name_lower or "adx" in name_lower or "macd" in name_lower or "psar" in name_lower or "ichimoku" in name_lower: category = 'general_ta_trend'
    elif "momentum_" in name_lower or "rsi" in name_lower or "stoch" in name_lower or "cci" in name_lower: category = 'general_ta_momentum'
    if pd.isna(indicator_value): return category, "Netral Sistem"
    if name_lower == 'rsi':
        if indicator_value > settings['rsi_overbought']: signal_text = f"RSI Overbought (>{settings['rsi_overbought']})"
        elif indicator_value < settings['rsi_oversold']: signal_text = f"RSI Oversold (<{settings['rsi_oversold']})"
        if indicator_value > 80 : signal_text = "RSI Oversold Ekstrem (>80)"
        if indicator_value < 20 : signal_text = "RSI Oversold Ekstrem (<20)"
    elif name_lower == 'macd_hist':
        atr_val = last_values_local.get('atr', 0.0001); threshold = atr_val * 0.03
        if indicator_value > threshold: signal_text = "MACD Bullish Cross Kuat"
        elif indicator_value < -threshold: signal_text = "MACD Bearish Cross Kuat"
    elif name_lower == 'adx':
        if indicator_value > 25 :
            dmp = last_values_local.get('adx_dmp',0); dmn = last_values_local.get('adx_dmn',0)
            if dmp > dmn: signal_text = "ADX Tren Bullish Kuat (>25, DI+ > DI-)"
            else: signal_text = "ADX Tren Bearish Kuat (>25, DI- > DI+)"
    elif name_lower == 'bb_p':
        if indicator_value > 1.05: signal_text = "Harga di Atas Upper BB (Potensi Overbought)"
        elif indicator_value < -0.05: signal_text = "Harga di Bawah Lower BB (Potensi Oversold)"
    elif current_price is not None and name_lower.startswith("ema_"):
        ema_short = last_values_local.get(f"ema_{settings['ema_short']}"); ema_medium = last_values_local.get(f"ema_{settings['ema_medium']}"); ema_long = last_values_local.get(f"ema_{settings['ema_long']}")
        if pd.notna(ema_short) and pd.notna(ema_medium) and pd.notna(ema_long):
            if current_price > ema_short > ema_medium > ema_long: signal_text = "Harga di Atas EMA Cloud Bullish Kuat"
            elif current_price < ema_short < ema_medium < ema_long: signal_text = "Harga di Bawah EMA Cloud Bearish Kuat"
    elif current_price is not None and name_lower == 'vwap':
        if pd.notna(indicator_value):
            if current_price > indicator_value: signal_text = "Harga di Atas VWAP (Bullish Bias)"
            elif current_price < indicator_value: signal_text = "Harga di Bawah VWAP (Bearish Bias)"
    return category, signal_text

def get_weighted_score_from_all_indicators_v3(all_indicators_last_values, df_full_ta, current_price, settings, last_values_local):
    total_bullish_score = 0.0; total_bearish_score = 0.0; interpreted_indicators = {}
    for name, value in all_indicators_last_values.items():
        processed_name = name.lower().replace('s_','').replace(f"_{settings['ema_short']}",'_short').replace(f"_{settings['ema_medium']}",'_medium').replace(f"_{settings['ema_long']}",'_long')
        category, signal_text = get_indicator_category_and_interpretation_v3(processed_name, value, df_full_ta, current_price, settings, last_values_local)
        base_score = SIGNAL_SCORES_V3.get(signal_text, 0)
        indicator_weights = get_indicator_weights()
        weight_key = category if category in indicator_weights else 'others'
        weight = indicator_weights.get(weight_key, 0.5)
        weighted_score = base_score * weight
        interpreted_indicators[name] = {'text': signal_text, 'score': weighted_score, 'category': category, 'raw_value': value}
        if weighted_score > 0: total_bullish_score += weighted_score
        elif weighted_score < 0: total_bearish_score += abs(weighted_score)
    return total_bullish_score, total_bearish_score, interpreted_indicators

# --- Analisis Sinyal per Timeframe (Diperbarui dengan Logika Baru V3) ---
def analyze_single_timeframe_signals_v3(pair_symbol_ccxt_format, timeframe_str, df_ohlcv):
    settings = get_key_indicator_settings_for_tf(timeframe_str)
    analysis_summary = {'pair': pair_symbol_ccxt_format, 'timeframe': timeframe_str, 'overall_signal': "NETRAL SISTEM", 'confidence': 0.0, 'score': 0.0, 'reasons': [], 'all_indicator_interpretations': {}, 'detected_features': {}, 'key_values': {}, 'settings_used': settings}
    required_len = max(settings.get('ema_long', 50), 60, settings.get('zigzag_len',10)*3)
    if df_ohlcv.empty or len(df_ohlcv) < required_len:
        analysis_summary['reasons'].append(f"Data kurang ({len(df_ohlcv)}/{required_len}) untuk analisis V3 TF {timeframe_str}."); analysis_summary['error'] = "Data tidak cukup"; return analysis_summary
    try:
        df_copy = df_ohlcv.copy(); df_full_ta = add_all_ta_features(df_copy, open="open", high="high", low="low", close="close", volume="volume", fillna=True)
        if f"S_EMA_{settings['ema_short']}" not in df_full_ta.columns: df_full_ta.ta.ema(length=settings['ema_short'], append=True, col_names=(f"S_EMA_{settings['ema_short']}",)); df_full_ta.ta.ema(length=settings['ema_medium'], append=True, col_names=(f"S_EMA_{settings['ema_medium']}",)); df_full_ta.ta.ema(length=settings['ema_long'], append=True, col_names=(f"S_EMA_{settings['ema_long']}",))
        # V4 Enhanced: Robust VWAP calculation without pandas_ta dependency
        if 'S_VWAP' not in df_full_ta.columns:
            try:
                # Manual VWAP calculation that always works
                typical_price = (df_full_ta['high'] + df_full_ta['low'] + df_full_ta['close']) / 3
                vwap_period = settings.get('vwap_period', 20)

                # Calculate cumulative VWAP
                pv = typical_price * df_full_ta['volume']
                cumulative_pv = pv.rolling(window=vwap_period, min_periods=1).sum()
                cumulative_volume = df_full_ta['volume'].rolling(window=vwap_period, min_periods=1).sum()

                # Avoid division by zero
                df_full_ta['S_VWAP'] = cumulative_pv / cumulative_volume.replace(0, 1)

            except Exception as vwap_error:
                logging.warning(f"VWAP calculation failed for TF {timeframe_str}: {vwap_error}")
                # Ultimate fallback: Use close price
                df_full_ta['S_VWAP'] = df_full_ta['close']
        if 'S_BB_P' not in df_full_ta.columns: df_full_ta.ta.bbands(length=settings['bb_period'], std=settings['bb_std_dev'], append=True, col_names=(f"S_BB_L", f"S_BB_M", f"S_BB_U", f"S_BB_B", f"S_BB_P"))
        if 'S_ADX' not in df_full_ta.columns: df_full_ta.ta.adx(length=settings['adx_period'], append=True, col_names=(f"S_ADX", f"S_ADX_DMP", f"S_ADX_DMN"))
        all_indicators_last_values = {col: df_full_ta[col].iloc[-1] for col in df_full_ta.columns if col not in ['open', 'high', 'low', 'close', 'volume', 'timestamp']}
    except Exception as e: logging.error(f"Error kalkulasi indikator dasar TF {timeframe_str} untuk {pair_symbol_ccxt_format}: {e}", exc_info=True); analysis_summary['reasons'].append(f"Gagal kalkulasi indikator dasar TF {timeframe_str}."); analysis_summary['error'] = "Indikator error"; return analysis_summary
    current_price = df_ohlcv['close'].iloc[-1]
    df_with_features, key_values, detected_features = calculate_advanced_market_features_v3(df_ohlcv.copy(), timeframe_str, settings)

    # NEW V4: Advanced Algorithm Integration
    try:
        # Calculate advanced features
        fib_levels = calculate_fibonacci_levels(df_ohlcv)
        sr_levels = calculate_support_resistance_levels(df_ohlcv)
        trend_strength = calculate_trend_strength(df_ohlcv)
        volume_profile = calculate_volume_profile(df_ohlcv)
        momentum_osc = calculate_momentum_oscillators(df_ohlcv)
        price_patterns = calculate_price_action_patterns(df_ohlcv)

        # Add to detected features
        detected_features.update({
            'fibonacci_levels': fib_levels,
            'support_resistance': sr_levels,
            'trend_strength': trend_strength,
            'volume_profile': volume_profile,
            'momentum_oscillators': momentum_osc,
            'price_action_patterns': price_patterns
        })

        # Update key values with advanced metrics
        key_values.update({
            'trend_strength_score': trend_strength,
            'fibonacci_support': fib_levels.get('61.8', 0) if fib_levels else 0,
            'volume_poc': volume_profile.get('poc', 0) if volume_profile else 0,
            'williams_r': momentum_osc.get('williams_r', 0),
            'cci': momentum_osc.get('cci', 0),
            'pattern_count': len(price_patterns)
        })

    except Exception as e:
        logging.warning(f"Advanced features calculation failed for {timeframe_str}: {e}")

    analysis_summary.update({'detected_features': detected_features, 'key_values': key_values})
    broad_bull, broad_bear, temp_interpreted_classic = get_weighted_score_from_all_indicators_v3(all_indicators_last_values, df_full_ta, current_price, settings, key_values) 
    analysis_summary['all_indicator_interpretations'].update(temp_interpreted_classic)
    smc_bull = 0; smc_bear = 0
    advanced_bull = 0; advanced_bear = 0  # NEW V4: Advanced scoring

    # Get indicator weights once for all calculations
    indicator_weights = get_indicator_weights()

    for feat_type, items in detected_features.items():
        if feat_type == 'market_structure':
            for ms in items: score = SIGNAL_SCORES_V3.get(f"{ms['type']} Confirmed", SIGNAL_SCORES_V3.get(ms['type'],0)) * indicator_weights.get('market_structure_trend', 3.0); analysis_summary['reasons'].append(f"{ms['type']} @ {ms['level']:.4f}"); (smc_bull := smc_bull + score) if score > 0 else (smc_bear := smc_bear + abs(score))
        elif feat_type == 'order_blocks':
            for ob in items: score_key = f"{ob['type'].split(' ')[0]} OB {'Strong Confluence' if ob.get('strength') == 'strong' and (ob.get('sweep_before') or ob.get('fvg_nearby')) else 'Validated'}"; score = SIGNAL_SCORES_V3.get(score_key,0) * indicator_weights.get('smc_zone_reaction', 3.0); analysis_summary['reasons'].append(f"{ob['type']} ({ob['strength']})"); (smc_bull := smc_bull + score) if score > 0 else (smc_bear := smc_bear + abs(score))
        elif feat_type == 'fvgs':
            for fvg in items: score_key = f"{fvg['type'].split(' ')[0]} FVG {'Untouched' if not fvg.get('mitigated') else 'Partially Mitigated'}"; score = SIGNAL_SCORES_V3.get(score_key,0) * indicator_weights.get('smc_zone_reaction', 2.0); analysis_summary['reasons'].append(f"{fvg['type']} (Mit: {fvg.get('mitigated')})"); (smc_bull := smc_bull + score) if score > 0 else (smc_bear := smc_bear + abs(score))
        elif feat_type == 'liquidity_sweeps':
            for sweep in items: score_key = f"Liquidity Grab {'Below (Bullish Signal)' if 'Low' in sweep['type'] else 'Above (Bearish Signal)'}"; score = SIGNAL_SCORES_V3.get(score_key,0) * indicator_weights.get('liquidity_event', 2.0); analysis_summary['reasons'].append(sweep['type']); (smc_bull := smc_bull + score) if score > 0 else (smc_bear := smc_bear + abs(score))
        elif feat_type == 'volume_analysis' and items.get('spike') not in ["Normal", None]:
            vol_spike_type = items['spike']; vol_dir = "Bullish" if key_values.get('price_close',0) > key_values.get('price_open',0) else "Bearish"
            score_key = f"Volume Spike Breakout {vol_dir}" if "Spike" in vol_spike_type else None
            if score_key: score = SIGNAL_SCORES_V3.get(score_key,0) * indicator_weights.get('volume_confirmation', 1.8); analysis_summary['reasons'].append(f"{vol_spike_type} Volume"); (smc_bull := smc_bull + score) if score > 0 else (smc_bear := smc_bear + abs(score))

    # NEW V4: Advanced Features Scoring
    try:
        # Fibonacci level scoring
        if 'fibonacci_levels' in detected_features and detected_features['fibonacci_levels']:
            fib = detected_features['fibonacci_levels']
            current_price = key_values.get('price_close', 0)
            if current_price:
                # Check if price is near key Fibonacci levels
                for level_name, level_price in fib.items():
                    if level_name in ['38.2', '50.0', '61.8'] and abs(current_price - level_price) / current_price < 0.01:
                        score = 1.5 * indicator_weights.get('fibonacci_levels', 2.5)
                        if current_price > level_price:
                            advanced_bull += score
                            analysis_summary['reasons'].append(f"Fib {level_name} Support")
                        else:
                            advanced_bear += score
                            analysis_summary['reasons'].append(f"Fib {level_name} Resistance")

        # Support/Resistance scoring
        if 'support_resistance' in detected_features:
            sr = detected_features['support_resistance']
            current_price = key_values.get('price_close', 0)
            if current_price and sr:
                # Check proximity to S/R levels
                for support in sr.get('support', []):
                    if abs(current_price - support) / current_price < 0.005:
                        advanced_bull += 2.0 * indicator_weights.get('support_resistance', 3.0)
                        analysis_summary['reasons'].append("Near Key Support")
                for resistance in sr.get('resistance', []):
                    if abs(current_price - resistance) / current_price < 0.005:
                        advanced_bear += 2.0 * indicator_weights.get('support_resistance', 3.0)
                        analysis_summary['reasons'].append("Near Key Resistance")

        # Trend strength scoring
        trend_strength = detected_features.get('trend_strength', 0)
        if abs(trend_strength) > 0.5:
            score = abs(trend_strength) * 2.0 * indicator_weights.get('trend_strength', 2.8)
            if trend_strength > 0:
                advanced_bull += score
                analysis_summary['reasons'].append("Strong Uptrend")
            else:
                advanced_bear += score
                analysis_summary['reasons'].append("Strong Downtrend")

        # Price action patterns scoring
        patterns = detected_features.get('price_action_patterns', [])
        for pattern in patterns:
            if pattern['significance'] == 'High':
                score = 1.8 * indicator_weights.get('price_action_patterns', 3.0)
                if pattern['type'] in ['Hammer', 'Bullish Engulfing']:
                    advanced_bull += score
                    analysis_summary['reasons'].append(f"{pattern['type']} Pattern")
                elif pattern['type'] in ['Shooting Star', 'Bearish Engulfing']:
                    advanced_bear += score
                    analysis_summary['reasons'].append(f"{pattern['type']} Pattern")

        # Momentum oscillators scoring
        momentum = detected_features.get('momentum_oscillators', {})
        if momentum:
            williams_r = momentum.get('williams_r', 0)
            cci = momentum.get('cci', 0)

            # Williams %R extreme levels
            if williams_r < -80:
                advanced_bull += 1.2 * indicator_weights.get('momentum_oscillators', 2.2)
                analysis_summary['reasons'].append("Williams %R Oversold")
            elif williams_r > -20:
                advanced_bear += 1.2 * indicator_weights.get('momentum_oscillators', 2.2)
                analysis_summary['reasons'].append("Williams %R Overbought")

            # CCI extreme levels
            if cci < -100:
                advanced_bull += 1.0 * indicator_weights.get('momentum_oscillators', 2.2)
                analysis_summary['reasons'].append("CCI Oversold")
            elif cci > 100:
                advanced_bear += 1.0 * indicator_weights.get('momentum_oscillators', 2.2)
                analysis_summary['reasons'].append("CCI Overbought")

        # Volume profile scoring
        vol_profile = detected_features.get('volume_profile', {})
        if vol_profile and 'poc' in vol_profile:
            current_price = key_values.get('price_close', 0)
            poc = vol_profile['poc']
            if current_price and abs(current_price - poc) / current_price < 0.01:
                score = 1.5 * indicator_weights.get('volume_profile', 2.8)
                advanced_bull += score  # POC acts as support/resistance
                analysis_summary['reasons'].append("Near Volume POC")

    except Exception as e:
        logging.warning(f"Advanced scoring failed for {timeframe_str}: {e}")

    total_bull_score = broad_bull + smc_bull + advanced_bull
    total_bear_score = broad_bear + smc_bear + advanced_bear
    net_score = total_bull_score - total_bear_score; analysis_summary['score'] = round(net_score, 2)

    # Enhanced V4: Dynamic scoring with advanced features
    max_possible_score_estimate = 40.0  # Increased due to advanced features
    base_confidence = min(1.0, abs(net_score) / max_possible_score_estimate)

    # Advanced confidence boosters
    strong_features_count = (
        sum(1 for ms in detected_features.get('market_structure',[]) if "BoS" in ms['type'] or "CHoCH" in ms['type']) +
        sum(1 for ob in detected_features.get('order_blocks',[]) if ob.get('strength') == 'strong') +
        sum(1 for pattern in detected_features.get('price_action_patterns',[]) if pattern.get('significance') == 'High') +
        (1 if abs(detected_features.get('trend_strength', 0)) > 0.7 else 0) +
        (1 if detected_features.get('volume_profile', {}).get('poc') else 0)
    )

    confluence_bonus = strong_features_count * 0.08

    # Multi-timeframe confluence check (if available)
    tf_confluence_bonus = 0
    if len(TIMEFRAMES_TO_ANALYZE) >= 3:
        tf_confluence_bonus = 0.1  # Bonus for multi-TF analysis

    final_confidence = base_confidence + confluence_bonus + tf_confluence_bonus
    analysis_summary['confidence'] = min(1.0, round(final_confidence, 2))

    # LOWERED THRESHOLDS: More permissive signal classification for more opportunities
    if EMERGENCY_MODE:
        # Emergency Mode: Very low thresholds for maximum opportunities
        if net_score > 2.0 and analysis_summary['confidence'] > 0.30:
            analysis_summary['overall_signal'] = "KUAT BELI"
        elif net_score > 1.0 and analysis_summary['confidence'] > 0.15:
            analysis_summary['overall_signal'] = "BELI"
        elif net_score < -2.0 and analysis_summary['confidence'] > 0.30:
            analysis_summary['overall_signal'] = "KUAT JUAL"
        elif net_score < -1.0 and analysis_summary['confidence'] > 0.15:
            analysis_summary['overall_signal'] = "JUAL"
        else:
            analysis_summary['overall_signal'] = "NETRAL SISTEM"
            analysis_summary['confidence'] = max(0.0, analysis_summary['confidence'] * 0.6)
    elif DISCOVERY_MODE:
        # Discovery Mode: Moderate thresholds
        if net_score > 3.0 and analysis_summary['confidence'] > 0.40:
            analysis_summary['overall_signal'] = "KUAT BELI"
        elif net_score > 1.5 and analysis_summary['confidence'] > 0.25:
            analysis_summary['overall_signal'] = "BELI"
        elif net_score < -3.0 and analysis_summary['confidence'] > 0.40:
            analysis_summary['overall_signal'] = "KUAT JUAL"
        elif net_score < -1.5 and analysis_summary['confidence'] > 0.25:
            analysis_summary['overall_signal'] = "JUAL"
        else:
            analysis_summary['overall_signal'] = "NETRAL SISTEM"
            analysis_summary['confidence'] = max(0.0, analysis_summary['confidence'] * 0.5)
    else:
        # Normal Mode: Relaxed thresholds (lowered from original)
        if net_score > 4.0 and analysis_summary['confidence'] > 0.50:  # Lowered from 6.0 & 0.75
            analysis_summary['overall_signal'] = "KUAT BELI"
        elif net_score > 2.0 and analysis_summary['confidence'] > 0.35:  # Lowered from 3.0 & 0.60
            analysis_summary['overall_signal'] = "BELI"
        elif net_score < -4.0 and analysis_summary['confidence'] > 0.50:  # Lowered from -6.0 & 0.75
            analysis_summary['overall_signal'] = "KUAT JUAL"
        elif net_score < -2.0 and analysis_summary['confidence'] > 0.35:  # Lowered from -3.0 & 0.60
            analysis_summary['overall_signal'] = "JUAL"
        else:
            analysis_summary['overall_signal'] = "NETRAL SISTEM"
            analysis_summary['confidence'] = max(0.0, analysis_summary['confidence'] * 0.4)

    # LOWERED: Minimum confidence check
    min_confidence_required = 0.10 if EMERGENCY_MODE else (0.15 if DISCOVERY_MODE else 0.20)
    if analysis_summary['overall_signal'] != "NETRAL SISTEM" and analysis_summary['confidence'] < min_confidence_required:
        analysis_summary['overall_signal'] = "NETRAL SISTEM"; analysis_summary['score'] = 0.0; analysis_summary['confidence'] = 0.1
        analysis_summary['reasons'].append("Keyakinan TF rendah setelah evaluasi fitur.")
    analysis_summary['reasons'] = list(set(analysis_summary['reasons']))[:4]
    return analysis_summary

# --- V5 PERFORMANCE: Enhanced Worker Thread dengan Async Download ---
class AnalysisWorkerV5(QThread):
    progress_updated = Signal(int, str)
    log_message_updated = Signal(str)
    analysis_complete = Signal(list)
    error_occurred = Signal(str)
    etr_updated = Signal(int, str)
    download_progress = Signal(int, str)  # New signal for download progress

    def __init__(self):
        super().__init__()
        self._is_running = True
        self.start_time_total_analysis = 0
        self.active_markets_list = []
        self.downloaded_data = {}

    def stop(self):
        log_to_console_and_gui("LOG: Perintah stop worker V5 Performance Enhanced.", self.log_message_updated)
        self._is_running = False

    def run(self):
        """V5 Enhanced: Two-phase analysis with async download and offline processing"""
        try:
            self.start_time_total_analysis = time.time()

            # Phase 1: Get active markets
            self.progress_updated.emit(5, "Mengambil daftar pasar aktif...")
            active_markets, error = get_active_futures_markets_manual(self.log_message_updated)

            if error or not active_markets:
                if "IP banned" in str(error) or "418" in str(error):
                    log_to_console_and_gui("🚨 IP banned detected! Using POPULAR PAIRS FALLBACK (40 pairs)...", self.log_message_updated, "WARNING")
                    active_markets, _ = get_popular_pairs_fallback()
                    log_to_console_and_gui(f"📊 Fallback Mode: Using {len(active_markets)} popular trading pairs", self.log_message_updated, "INFO")
                    error = None
                else:
                    self.error_occurred.emit(f"Gagal mengambil daftar pasar: {error}")
                    return

            if DEMO_MODE:
                active_markets = DEMO_PAIRS
                log_to_console_and_gui(f"V5 Demo Mode: Menggunakan {len(active_markets)} pairs demo", self.log_message_updated)

            # Smart filtering (no limit in V5)
            filtered_pairs = smart_pair_filtering(active_markets)
            log_to_console_and_gui(f"V5 Smart Filter: {len(active_markets)} → {len(filtered_pairs)} pairs", self.log_message_updated)

            if not filtered_pairs:
                self.error_occurred.emit("Tidak ada pasangan yang memenuhi kriteria filtering")
                return

            # Phase 2: Async Data Download
            self.progress_updated.emit(10, "Memulai download data asynchronous...")
            log_to_console_and_gui(f"V5 Async Download: {len(filtered_pairs)} pairs, {len(TIMEFRAMES_TO_ANALYZE)} timeframes", self.log_message_updated)

            # Run async download in event loop
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            try:
                self.downloaded_data = loop.run_until_complete(
                    download_all_data_async(
                        filtered_pairs,
                        TIMEFRAMES_TO_ANALYZE,
                        self.update_download_progress
                    )
                )
            finally:
                loop.close()

            if not self._is_running:
                return

            # Phase 3: Offline Analysis
            self.progress_updated.emit(60, "Memulai analisis offline...")
            log_to_console_and_gui(f"V5 Offline Analysis: {len(self.downloaded_data)} pairs dengan data lengkap", self.log_message_updated)

            # Process pairs in parallel batches
            results = []
            total_pairs = len(self.downloaded_data)
            processed_pairs = 0

            # SPEED OPTIMIZATION: Use ProcessPoolExecutor for CPU-intensive analysis
            max_workers = min(MAX_WORKERS_ANALYSIS, total_pairs, multiprocessing.cpu_count())
            log_to_console_and_gui(f"V5 ULTRA-FAST Processing: {max_workers} workers untuk {total_pairs} pairs", self.log_message_updated)

            # SPEED OPTIMIZATION: Batch processing for better performance
            batch_size = max(1, total_pairs // max_workers)
            pair_items = list(self.downloaded_data.items())

            # Process in batches for optimal performance
            for batch_start in range(0, total_pairs, batch_size):
                if not self._is_running:
                    break

                batch_end = min(batch_start + batch_size, total_pairs)
                batch_items = pair_items[batch_start:batch_end]

                # Use ThreadPoolExecutor for I/O bound operations (faster startup than ProcessPool)
                with ThreadPoolExecutor(max_workers=min(max_workers, len(batch_items))) as executor:
                    # Submit batch analysis tasks
                    future_to_pair = {}
                    for pair_symbol, pair_data in batch_items:
                        if not self._is_running:
                            break
                        future = executor.submit(analyze_pair_offline, pair_data, pair_symbol)
                        future_to_pair[future] = pair_symbol

                    # Collect results as they complete
                    for future in as_completed(future_to_pair):
                        if not self._is_running:
                            break

                        pair_symbol = future_to_pair[future]
                        try:
                            result = future.result(timeout=30)  # Add timeout for faster failure handling
                            if 'error' not in result:
                                results.append(result)

                            processed_pairs += 1
                            progress = 60 + int((processed_pairs / total_pairs) * 30)
                            self.progress_updated.emit(progress, f"Menganalisis {processed_pairs}/{total_pairs} pairs...")

                            # SPEED OPTIMIZATION: Update ETR less frequently
                            if processed_pairs % 10 == 0 and processed_pairs > 10:  # Update every 10 pairs
                                elapsed = time.time() - self.start_time_total_analysis
                                rate = processed_pairs / elapsed
                                remaining_pairs = total_pairs - processed_pairs
                                etr_seconds = remaining_pairs / rate if rate > 0 else 0
                                etr_formatted = f"{int(etr_seconds//60)}m {int(etr_seconds%60)}s"
                                self.etr_updated.emit(int(etr_seconds), etr_formatted)

                        except Exception as e:
                            log_to_console_and_gui(f"Error analyzing {pair_symbol}: {e}", self.log_message_updated, "WARNING")
                            processed_pairs += 1

            if not self._is_running:
                return

            # Phase 4: Filter and rank results
            self.progress_updated.emit(95, "Menyusun hasil...")

            # Filter by confidence threshold - LOWERED FOR MORE OPPORTUNITIES
            if EMERGENCY_MODE:
                confident_signals = [r for r in results if r.get('confidence', 0) >= 0.12]  # Emergency: 12%
            elif DISCOVERY_MODE:
                confident_signals = [r for r in results if r.get('confidence', 0) >= 0.15]  # Discovery: 15%
            else:
                confident_signals = [r for r in results if r.get('confidence', 0) >= 0.30]  # Normal: 30%

            # Sort by confidence and score
            confident_signals.sort(key=lambda x: (x.get('confidence', 0), abs(x.get('score', 0))), reverse=True)

            # Limit to display count
            top_signals = confident_signals[:MAX_PAIRS_DISPLAY]

            # Convert to expected format with proper data structure
            formatted_results = []
            for result in top_signals:
                # Create proper analysis details per timeframe
                analysis_details_per_tf = {}
                tf_scores = result.get('timeframe_scores', {})
                tf_features = result.get('features', {})

                for tf in TIMEFRAMES_TO_ANALYZE:
                    if tf in tf_scores:
                        analysis_details_per_tf[tf] = {
                            'overall_signal': result['signal_type'],
                            'confidence': result['confidence'],
                            'score': tf_scores[tf],
                            'reasons': [f"TF {tf} Score: {tf_scores[tf]:.2f}"],
                            'key_values': {'price_close': result['current_price']},
                            'all_indicator_interpretations': {},
                            'detected_features': tf_features.get(tf, {})
                        }
                    else:
                        analysis_details_per_tf[tf] = {
                            'error': 'No data available',
                            'overall_signal': 'ERROR'
                        }

                formatted_results.append((
                    result['pair'],
                    result['score'],
                    result['signal_type'],
                    {
                        'pair': result['pair'],
                        'current_price_reference': result['current_price'],
                        'final_confidence': result['confidence'],
                        'consolidated_signal': result['signal_type'],
                        'consolidated_score': result['score'],
                        'supporting_reasons_consolidation': [f"TF {tf}: {score:.2f}" for tf, score in tf_scores.items()],
                        'analysis_details_per_tf': analysis_details_per_tf
                    }
                ))

            log_to_console_and_gui(f"V5 Results: {len(formatted_results)} sinyal confident dari {len(results)} total", self.log_message_updated)

            self.progress_updated.emit(100, "Analisis V5 Performance Enhanced selesai!")
            self.analysis_complete.emit(formatted_results)

        except Exception as e:
            log_to_console_and_gui(f"V5 Fatal Error: {e}", self.log_message_updated, "ERROR")
            self.error_occurred.emit(f"V5 Error: {e}")
        finally:
            self._is_running = False

    def update_download_progress(self, progress: int, message: str):
        """Update progress during download phase"""
        # Map download progress to 10-60% range
        mapped_progress = 10 + int((progress / 100) * 50)
        self.progress_updated.emit(mapped_progress, f"Download: {message}")

    def _convert_binance_api_to_ccxt_symbol(self, s):
        return f"{s[:-4]}/{s[-4:]}" if isinstance(s, str) and (s.endswith("USDT") or s.endswith("BUSD")) else s

# --- Legacy Worker (kept for compatibility) ---
class AnalysisWorker(QThread):
    progress_updated = Signal(int, str); log_message_updated = Signal(str)
    analysis_complete = Signal(list); error_occurred = Signal(str); etr_updated = Signal(int, str)
    def __init__(self): super().__init__(); self._is_running = True; self.start_time_total_analysis = 0; self.active_markets_list = []
    def stop(self): log_to_console_and_gui("LOG: Perintah stop worker V4 Enhanced AI.", self.log_message_updated); self._is_running = False
    def _convert_binance_api_to_ccxt_symbol(self, s): return f"{s[:-4]}/{s[-4:]}" if isinstance(s,str) and (s.endswith("USDT") or s.endswith("BUSD")) else s
    def _process_one_pair_all_timeframes(self, pair_api, idx, total):
        if not self._is_running: return {'logs': [f"LOG: Proses {pair_api} dibatalkan."], 'result': None, 'error': True}
        pair_ccxt = self._convert_binance_api_to_ccxt_symbol(pair_api)
        logs = [f"LOG: (T:{threading.get_ident()}) [{idx+1}/{total}] Analisis V4 Enhanced AI {pair_ccxt}..."]
        if idx % 10 == 0 or idx < 3 or (DEMO_MODE and idx < len(DEMO_PAIRS)): log_to_console_and_gui(f"📈 Analisa V4: {idx+1}/{total} - {pair_ccxt}", self.log_message_updated, "DEBUG" if not DEMO_MODE else "INFO")
        analysis_tf = {}; ohlcv_data_pair = {}
        for tf in TIMEFRAMES_TO_ANALYZE:
            if not self._is_running: break
            logs.append(f"LOG: {pair_ccxt} - Fetching TF {tf}...")
            df, err = fetch_ohlcv_manual_rest(pair_api, tf, CANDLE_LIMIT_PER_TIMEFRAME, self.log_message_updated)
            if err: logs.append(f"LOG: {pair_ccxt} TF {tf} - Error fetch: {err}"); analysis_tf[tf] = {'error': err, 'overall_signal': "ERROR"}; continue
            settings = get_key_indicator_settings_for_tf(tf) 
            required_len = max(settings.get('ema_long', 50), 60, settings.get('zigzag_len',10)*3)
            if df.empty or len(df) < required_len :
                logs.append(f"LOG: {pair_ccxt} TF {tf} data kurang ({len(df)}/{required_len})."); analysis_tf[tf] = {'error': "Data kurang", 'overall_signal': "DATA KURANG"}; continue
            ohlcv_data_pair[tf] = df; logs.append(f"LOG: {pair_ccxt} - Menganalisa TF {tf}...")
            summary = analyze_single_timeframe_signals_v3(pair_ccxt, tf, df) 
            analysis_tf[tf] = summary; logs.append(f"LOG: {pair_ccxt} TF {tf} -> {summary['overall_signal']} (Conf: {summary['confidence']:.2f}, Skor: {summary['score']:.2f})")
        if not self._is_running: return {'logs': logs + [f"LOG: {pair_ccxt} dibatalkan."], 'result': None, 'error': True}
        htf_res = analysis_tf.get(TIMEFRAMES_TO_ANALYZE[0]); mtf_res = analysis_tf.get(TIMEFRAMES_TO_ANALYZE[1]); ltf_res = analysis_tf.get(TIMEFRAMES_TO_ANALYZE[2])
        final_sig = "NETRAL SISTEM"; final_conf = 0.0; final_score_val = 0.0; final_reasons_list = []
        if not htf_res or htf_res.get('error'): logs.append(f"LOG: {pair_ccxt} - HTF error, konsolidasi gagal."); return {'logs': logs, 'result': None, 'error': True}
        indicator_weights = get_indicator_weights()
        final_score_val += htf_res.get('score', 0.0) * indicator_weights.get('htf_trend_clarity', 1.8); final_reasons_list.extend([f"HTF({TIMEFRAMES_TO_ANALYZE[0]}): {r}" for r in htf_res.get('reasons',[])])
        if mtf_res and not mtf_res.get('error'):
            if (htf_res.get('overall_signal',"").startswith("BELI") and mtf_res.get('overall_signal',"").startswith("BELI")) or (htf_res.get('overall_signal',"").startswith("JUAL") and mtf_res.get('overall_signal',"").startswith("JUAL")):
                final_score_val += mtf_res.get('score', 0.0) * indicator_weights.get('multi_tf_alignment', 4.0); final_reasons_list.append(f"MTF({TIMEFRAMES_TO_ANALYZE[1]}) Konfirmasi: {mtf_res.get('overall_signal')}"); final_conf += 0.25
            else: final_score_val += mtf_res.get('score', 0.0) * 0.5
        if ltf_res and not ltf_res.get('error') and abs(final_score_val) > 1.0:
            if (final_score_val > 0 and ltf_res.get('overall_signal',"").startswith("BELI")) or (final_score_val < 0 and ltf_res.get('overall_signal',"").startswith("JUAL")):
                final_score_val += ltf_res.get('score', 0.0) * indicator_weights.get('multi_tf_alignment', 4.0) * 0.75; final_reasons_list.append(f"LTF({TIMEFRAMES_TO_ANALYZE[2]}) Pemicu Searah: {ltf_res.get('overall_signal')}"); final_conf += 0.15
        final_conf += htf_res.get('confidence',0.0) * 0.6; 
        if mtf_res and not mtf_res.get('error'): final_conf += mtf_res.get('confidence',0.0) * 0.3
        if ltf_res and not ltf_res.get('error'): final_conf += ltf_res.get('confidence',0.0) * 0.1
        final_conf = min(1.0, max(0.0, final_conf))

        # Enhanced V4: More stringent final signal thresholds
        if final_score_val > 8.0 and final_conf > 0.80: final_sig = "KUAT BELI"
        elif final_score_val > 4.0 and final_conf > 0.65: final_sig = "BELI"
        elif final_score_val < -8.0 and final_conf > 0.80: final_sig = "KUAT JUAL"
        elif final_score_val < -4.0 and final_conf > 0.65: final_sig = "JUAL"
        else: final_sig = "NETRAL SISTEM"; final_conf = max(0.0, final_conf * 0.4); final_score_val = 0.0

        # Enhanced V4: Higher minimum confidence requirement
        if final_sig != "NETRAL SISTEM" and final_conf < 0.50:
            final_sig = "NETRAL SISTEM"; final_score_val = 0.0; final_conf = 0.20
            final_reasons_list.append("Keyakinan konsolidasi akhir tidak memenuhi standar V4.")
        price_ref_df = ohlcv_data_pair.get(REFERENCE_TIMEFRAME_FOR_PRICE); price_disp = price_ref_df['close'].iloc[-1] if price_ref_df is not None and not price_ref_df.empty else (htf_res['key_values'].get('price_close') if htf_res and htf_res.get('key_values') else None)
        if final_sig != "NETRAL SISTEM" and price_disp is not None:
            bundle = {'pair': pair_ccxt, 'current_price_reference': price_disp, 'consolidated_signal': final_sig, 'consolidated_score': round(final_score_val,2), 'final_confidence': round(final_conf,2), 'analysis_details_per_tf': analysis_tf, 'supporting_reasons_consolidation': list(set(final_reasons_list))[:5]}
            logs.append(f"LOG: {pair_ccxt} - Sinyal Final V4: {final_sig} (Conf: {final_conf:.2f}, Skor: {final_score_val:.2f})"); return {'logs': logs, 'result': (pair_ccxt, final_score_val, final_sig, bundle), 'error': False}
        else: logs.append(f"LOG: {pair_ccxt} - Tidak ada sinyal signifikan V4 (Skor: {final_score_val:.2f})."); return {'logs': logs, 'result': None, 'error': False}
    def run(self): 
        self._is_running = True; self.start_time_total_analysis = time.time()
        try:
            log_to_console_and_gui("IntelliTrader X V4 Enhanced AI: Memulai...", self.log_message_updated); self.progress_updated.emit(0, "Menghubungkan V4 Enhanced AI...")
            if DEMO_MODE:
                log_to_console_and_gui("🚧 DEMO MODE (V4 Enhanced) 🚧", self.log_message_updated, "WARNING")
                self.active_markets_list = DEMO_PAIRS
            else:
                self.active_markets_list, err = get_active_futures_markets_manual(self.log_message_updated)

                # Handle IP ban with fallback to popular pairs
                if err and ("IP banned" in str(err) or "418" in str(err)):
                    log_to_console_and_gui("🚨 IP banned detected! Using POPULAR PAIRS FALLBACK (40 pairs)...", self.log_message_updated, "WARNING")
                    self.active_markets_list, _ = get_popular_pairs_fallback()
                    log_to_console_and_gui(f"📊 Fallback Mode: Using {len(self.active_markets_list)} popular trading pairs", self.log_message_updated, "INFO")
                    err = None

                # V4: Smart filtering for better performance
                if self.active_markets_list:
                    original_count = len(self.active_markets_list)
                    self.active_markets_list = smart_pair_filtering(self.active_markets_list)
                    log_to_console_and_gui(f"V4 Smart Filter: {original_count} → {len(self.active_markets_list)} pairs", self.log_message_updated)
            if err: self.error_occurred.emit(f"Gagal muat pasar: {err}"); self.progress_updated.emit(100, "Error Muat Pasar."); return
            if not self.active_markets_list: self.error_occurred.emit("Tidak ada pasar aktif."); self.progress_updated.emit(100, "Selesai (0 pasar)."); self.analysis_complete.emit([]); return
            if not self._is_running: self.progress_updated.emit(100, "Dibatalkan."); self.analysis_complete.emit([]); return
            num_pairs = len(self.active_markets_list); log_to_console_and_gui(f"V4 Enhanced AI: {num_pairs} pairs, max {MAX_WORKERS_ANALYSIS} workers. Delay API: {API_REQUEST_DELAY_MS}ms.", self.log_message_updated); self.progress_updated.emit(10, f"Analisa V4 Enhanced AI {num_pairs} pairs...")
            signals = []; processed = 0
            with ThreadPoolExecutor(max_workers=MAX_WORKERS_ANALYSIS, thread_name_prefix="PairAnalyzerV4Enhanced") as executor:
                futures = {executor.submit(self._process_one_pair_all_timeframes, p, i, num_pairs): p for i, p in enumerate(self.active_markets_list) if self._is_running}
                if not self._is_running: self.progress_updated.emit(100, "Dibatalkan."); self.analysis_complete.emit([]); return
                for future in as_completed(futures):
                    if not self._is_running: log_to_console_and_gui("V4 Enhanced AI: Stop saat proses hasil.", self.log_message_updated, "WARNING"); break
                    pair_api = futures[future]
                    try:
                        res_data = future.result()
                        if res_data: [self.log_message_updated.emit(log) for log in res_data.get('logs',[])]; 
                        if res_data.get('result'): signals.append(res_data['result'])
                    except Exception as exc: log_to_console_and_gui(f"V4 Enhanced AI: Error proses {self._convert_binance_api_to_ccxt_symbol(pair_api)}: {exc}", self.log_message_updated, "ERROR"); logging.error(f"Error proses {pair_api}", exc_info=True)
                    finally:
                        processed += 1
                        if num_pairs > 0: prog = 10 + int((processed / num_pairs) * 80); elapsed = time.time() - self.start_time_total_analysis
                        if processed > 0: avg_time = elapsed / processed; rem = num_pairs - processed; etr_s = int(rem * avg_time); etr_m, etr_sr = divmod(etr_s, 60); etr_str = f"{etr_m:02d}:{etr_sr:02d}"; self.etr_updated.emit(etr_s, etr_str); self.progress_updated.emit(prog, f"Memproses... ({processed}/{num_pairs})")
            if not self._is_running: self.progress_updated.emit(100, "Dibatalkan."); self.analysis_complete.emit([]); return
            log_to_console_and_gui("V4 Enhanced AI: Urutkan sinyal...", self.log_message_updated); signals.sort(key=lambda x: (abs(x[1]), x[3].get('final_confidence', 0) if x[3] else 0), reverse=True)
            # LOWERED THRESHOLD: More permissive display criteria
            if EMERGENCY_MODE:
                strong_signals = [s for s in signals if s[2] != "NETRAL SISTEM" and s[3] and s[3].get('final_confidence', 0) >= 0.12]  # Emergency: 12%
            elif DISCOVERY_MODE:
                strong_signals = [s for s in signals if s[2] != "NETRAL SISTEM" and s[3] and s[3].get('final_confidence', 0) >= 0.15]  # Discovery: 15%
            else:
                strong_signals = [s for s in signals if ("KUAT" in s[2]) or (s[2] != "NETRAL SISTEM" and s[3] and s[3].get('final_confidence', 0) >= 0.30)]  # Normal: 30%
            top_display = strong_signals[:MAX_PAIRS_DISPLAY]
            # Dynamic threshold message based on mode
            threshold_msg = "12%" if EMERGENCY_MODE else ("15%" if DISCOVERY_MODE else "30%")
            mode_msg = "Emergency" if EMERGENCY_MODE else ("Discovery" if DISCOVERY_MODE else "Normal")

            log_to_console_and_gui(f"📊 RINGKASAN V4 Enhanced AI ({mode_msg}): Total: {num_pairs}, Kandidat: {len(signals)}, Conf≥{threshold_msg}: {len(strong_signals)}, Tampil: {len(top_display)}", self.log_message_updated)
            if top_display: [log_to_console_and_gui(f"🏆 {i+1}. {s[0]} - {s[2]} (Skor: {s[1]:.2f}, Conf: {s[3].get('final_confidence',0)*100:.0f}%)", self.log_message_updated) for i,s in enumerate(top_display)]
            else: log_to_console_and_gui(f"⚠️ Tidak ada sinyal memenuhi threshold {mode_msg} (Conf≥{threshold_msg}).", self.log_message_updated, "WARNING")
            self.progress_updated.emit(95, "Menyusun hasil..."); self.analysis_complete.emit(top_display); self.progress_updated.emit(100, "Analisa V4 Enhanced AI Selesai."); log_to_console_and_gui("✅ ANALISIS V4 ENHANCED AI SELESAI!", self.log_message_updated)
        except Exception as e: log_to_console_and_gui(f"V4 Enhanced AI: Kesalahan fatal Worker: {type(e).__name__} - {e}", self.log_message_updated, "ERROR"); logging.error("Fatal error Worker V4 Enhanced AI", exc_info=True); self.error_occurred.emit(f"Kesalahan fatal V4: {e}")
        finally: self.progress_updated.emit(100, "Dibatalkan." if not self._is_running else "Proses V4 Enhanced AI selesai."); self._is_running = False

class BinanceSignalApp(QMainWindow):
    # Define signals for proper communication
    log_message_updated = Signal(str)

    def __init__(self):
        super().__init__(); self.setWindowTitle("IntelliTrader X: V5 Performance Enhanced"); self.setMinimumSize(QSize(600, 1000))
        self.setStyleSheet(f"QMainWindow {{ background-color: {COLOR_BACKGROUND_MAIN.name()}; }}"); QApplication.setFont(QFont("Inter", 10))
        self.analysis_worker = None; self.all_pairs_data_cache = {}; self.current_etr_string = ""
        self.use_v5_worker = True  # Flag to use V5 enhanced worker

        # Connect the log signal to the append method
        self.log_message_updated.connect(self.append_log_message)

        self.init_ui_modern(); self.show_page(self.initial_view_widget)
    def init_ui_modern(self):
        self.main_widget = QWidget(); self.setCentralWidget(self.main_widget); main_layout_for_stack = QVBoxLayout(self.main_widget); main_layout_for_stack.setContentsMargins(0,0,0,0); self.stacked_widget = QStackedWidget(); main_layout_for_stack.addWidget(self.stacked_widget)
        self.initial_view_widget = AnimatedBackgroundWidget(COLOR_PRIMARY_ACCENT_APP.lighter(150), COLOR_SECONDARY_ACCENT_APP.lighter(130))
        initial_page_layout = QVBoxLayout(self.initial_view_widget); initial_page_layout.setContentsMargins(30, 40, 30, 30); initial_page_layout.setAlignment(Qt.AlignTop)
        app_title = QLabel("IntelliTrader X"); app_title.setFont(QFont("Inter", 32, QFont.Bold)); app_title.setStyleSheet(f"color: {COLOR_BUTTON_PRIMARY_TEXT.name()}; qproperty-alignment: AlignCenter; margin-bottom: 5px;"); initial_page_layout.addWidget(app_title)
        app_subtitle_main = QLabel("HYBRID Signal Engine V4 - Enhanced AI"); app_subtitle_main.setFont(QFont("Inter", 13, QFont.Medium)); app_subtitle_main.setStyleSheet(f"color: {COLOR_BUTTON_PRIMARY_TEXT.lighter(150).name()}; qproperty-alignment: AlignCenter; margin-bottom: 25px;"); initial_page_layout.addWidget(app_subtitle_main)
        content_card_initial = QFrame(); content_card_initial.setObjectName("ContentCardInitial"); content_card_initial.setStyleSheet(f"#ContentCardInitial {{ background-color: rgba(255, 255, 255, 0.92); border-radius: 20px; padding: 25px; border: 1px solid rgba(200, 200, 200, 0.3); }}"); content_card_layout_initial = QVBoxLayout(content_card_initial); content_card_layout_initial.setSpacing(15)
        self.status_label = QLabel("Status: Siap Menganalisa..."); self.status_label.setFont(QFont("Inter", 14, QFont.Medium)); self.status_label.setAlignment(Qt.AlignCenter); self.status_label.setStyleSheet(f"color: {COLOR_TEXT_HEADLINE.name()};"); content_card_layout_initial.addWidget(self.status_label)

        # V5 Enhanced: Two-phase progress indicators
        self.phase_label = QLabel("Fase: Siap"); self.phase_label.setFont(QFont("Inter", 11, QFont.Medium)); self.phase_label.setAlignment(Qt.AlignCenter); self.phase_label.setStyleSheet(f"color: {COLOR_TEXT_BODY.name()}; margin-top: 5px;"); content_card_layout_initial.addWidget(self.phase_label)

        self.progress_bar = QProgressBar(); self.progress_bar.setTextVisible(True); self.progress_bar.setFixedHeight(15); self.progress_bar.setStyleSheet(f"QProgressBar {{ border: none; border-radius: 7px; background-color: {COLOR_PROGRESS_BG_APP.name()}; text-align: center; font-weight: bold; }} QProgressBar::chunk {{ background-color: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 {COLOR_PROGRESS_CHUNK_START.name()}, stop:1 {COLOR_SECONDARY_ACCENT_APP.name()}); border-radius: 7px; }}"); content_card_layout_initial.addWidget(self.progress_bar)

        # Performance metrics display
        self.perf_metrics_label = QLabel(""); self.perf_metrics_label.setFont(QFont("Inter", 9)); self.perf_metrics_label.setAlignment(Qt.AlignCenter); self.perf_metrics_label.setStyleSheet(f"color: {COLOR_TEXT_MUTED.name()}; margin-top: 5px;"); content_card_layout_initial.addWidget(self.perf_metrics_label)
        log_label_initial = QLabel("Log Proses:"); log_label_initial.setFont(QFont("Inter", 11, QFont.Bold)); log_label_initial.setStyleSheet(f"color: {COLOR_TEXT_BODY.name()}; margin-top:10px;"); content_card_layout_initial.addWidget(log_label_initial)
        self.initial_log_display = QTextEdit(); self.initial_log_display.setReadOnly(True); self.initial_log_display.setFont(QFont("Consolas", 9)); self.initial_log_display.setStyleSheet(f"QTextEdit {{ background-color: {COLOR_BACKGROUND_MAIN.name()}; border: 1px solid {COLOR_PROGRESS_BG_APP.name()}; border-radius: 8px; padding: 10px; color: {COLOR_TEXT_BODY.name()}; }}"); self.initial_log_display.setMinimumHeight(150); self.initial_log_display.setMaximumHeight(200); content_card_layout_initial.addWidget(self.initial_log_display)
        initial_page_layout.addWidget(content_card_initial); initial_page_layout.addSpacerItem(QSpacerItem(20,20, QSizePolicy.Minimum, QSizePolicy.Expanding))
        self.initial_buttons_layout = QHBoxLayout(); self.initial_buttons_layout.setSpacing(15)
        btn_style_primary = f"QPushButton {{ background-color: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 {COLOR_PRIMARY_ACCENT_APP.name()}, stop:1 {COLOR_SECONDARY_ACCENT_APP.name()}); color: white; border:none; border-radius: 25px; padding: 0px 25px; font-family: Inter; font-size: 13pt; font-weight: bold; min-height: 50px;}} QPushButton:hover {{ background-color: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 {COLOR_PRIMARY_ACCENT_APP.darker(110).name()}, stop:1 {COLOR_SECONDARY_ACCENT_APP.darker(110).name()});}} QPushButton:pressed {{ background-color: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 {COLOR_PRIMARY_ACCENT_APP.darker(120).name()}, stop:1 {COLOR_SECONDARY_ACCENT_APP.darker(120).name()});}} QPushButton:disabled {{ background-color: {COLOR_TEXT_MUTED.name()}; }}"
        btn_style_secondary = f"QPushButton {{ background-color: {COLOR_CARD_BG.name()}; color: {COLOR_PRIMARY_ACCENT_APP.name()}; border: 1px solid {COLOR_PRIMARY_ACCENT_APP.name()}; border-radius: 25px; padding: 0px 20px; font-family: Inter; font-size: 12pt; font-weight: medium; min-height: 50px;}} QPushButton:hover {{ background-color: {COLOR_PRIMARY_ACCENT_APP.lighter(180).name()}; }} QPushButton:pressed {{ background-color: {COLOR_PRIMARY_ACCENT_APP.lighter(170).name()}; }} QPushButton:disabled {{ background-color: {COLOR_TEXT_MUTED.lighter(120).name()}; color: {COLOR_TEXT_MUTED.name()}; border-color: {COLOR_TEXT_MUTED.name()};}}"
        self.start_button = QPushButton("🚀 Mulai Analisa"); self.start_button.setStyleSheet(btn_style_primary); self.start_button.clicked.connect(self.start_analysis); self.start_button.setCursor(Qt.PointingHandCursor)
        self.stop_analysis_button = QPushButton("🛑 Hentikan"); self.stop_analysis_button.setStyleSheet(btn_style_secondary.replace(COLOR_PRIMARY_ACCENT_APP.name(), COLOR_WARNING_APP.name())); self.stop_analysis_button.clicked.connect(self.request_stop_analysis); self.stop_analysis_button.setVisible(False); self.stop_analysis_button.setCursor(Qt.PointingHandCursor)
        # Note: config_button will be connected after config_wrapper is created
        self.config_button_initial = QPushButton("⚙️ Pengaturan"); self.config_button_initial.setStyleSheet(btn_style_secondary.replace(COLOR_PRIMARY_ACCENT_APP.name(), COLOR_SECONDARY_ACCENT_APP.name())); self.config_button_initial.setCursor(Qt.PointingHandCursor)
        self.exit_button_initial = QPushButton("🚪 Keluar"); self.exit_button_initial.setStyleSheet(btn_style_secondary.replace(COLOR_PRIMARY_ACCENT_APP.name(), COLOR_ERROR_APP.name())); self.exit_button_initial.clicked.connect(self.close); self.exit_button_initial.setCursor(Qt.PointingHandCursor)
        self.initial_buttons_layout.addWidget(self.exit_button_initial, 1); self.initial_buttons_layout.addWidget(self.config_button_initial, 1); self.initial_buttons_layout.addWidget(self.stop_analysis_button, 1); self.initial_buttons_layout.addWidget(self.start_button, 2); initial_page_layout.addLayout(self.initial_buttons_layout); self.stacked_widget.addWidget(self.initial_view_widget)
        self.signals_view_widget = QWidget(); self.signals_view_widget.setStyleSheet(f"background-color: {COLOR_BACKGROUND_MAIN.name()};"); self.signals_layout = QVBoxLayout(self.signals_view_widget); self.signals_layout.setContentsMargins(20,25,20,20); self.signals_layout.setSpacing(15)
        signals_title = QLabel("🏆 Sinyal Trading Unggulan"); signals_title.setFont(QFont("Inter", 22, QFont.Bold)); signals_title.setStyleSheet(f"color: {COLOR_TEXT_HEADLINE.name()}; margin-bottom: 10px; qproperty-alignment: AlignCenter;"); self.signals_layout.addWidget(signals_title)
        self.log_card_signals = ModernCollapsibleCard("📜 Detail Log Proses Analisa"); self.signals_log_display = QTextEdit(); self.signals_log_display.setReadOnly(True); self.signals_log_display.setFont(QFont("Consolas", 8)); self.signals_log_display.setStyleSheet(f"QTextEdit {{ background-color: {COLOR_BACKGROUND_MAIN.lighter(102).name()}; border: none; padding: 10px; color: {COLOR_TEXT_BODY.name()}; }}"); self.signals_log_display.setMinimumHeight(120)
        log_card_content_widget = QWidget(); log_card_content_layout = QVBoxLayout(log_card_content_widget); log_card_content_layout.setContentsMargins(0,0,0,0); log_card_content_layout.setSpacing(8); log_card_content_layout.addWidget(self.signals_log_display)
        copy_log_button = QPushButton("📋 Salin Log"); copy_log_button.setFont(QFont("Inter", 9, QFont.Medium)); copy_log_button.setStyleSheet(f"QPushButton {{ background-color: {COLOR_PRIMARY_ACCENT_APP.lighter(180).name()}; color: {COLOR_PRIMARY_ACCENT_APP.darker(110).name()}; border-radius: 15px; padding: 8px 15px; border: none; margin-top: 5px;}} QPushButton:hover {{ background-color: {COLOR_PRIMARY_ACCENT_APP.lighter(170).name()};}}"); copy_log_button.setCursor(Qt.PointingHandCursor); copy_log_button.clicked.connect(self.copy_signals_log_to_clipboard); log_card_content_layout.addWidget(copy_log_button, alignment=Qt.AlignRight); self.log_card_signals.setContentWidget(log_card_content_widget); self.signals_layout.addWidget(self.log_card_signals)
        self.scroll_area = QScrollArea(); self.scroll_area.setWidgetResizable(True); self.scroll_area.setStyleSheet("QScrollArea { border: none; background-color: transparent; margin-top: 10px; }"); self.scroll_content_widget = QWidget(); self.scroll_area.setWidget(self.scroll_content_widget); self.signals_cards_layout = QVBoxLayout(self.scroll_content_widget); self.signals_cards_layout.setAlignment(Qt.AlignTop); self.signals_cards_layout.setSpacing(15); self.signals_layout.addWidget(self.scroll_area)
        signal_actions_layout = QHBoxLayout(); signal_actions_layout.setSpacing(10)
        self.refresh_button_sv = QPushButton("🔄 Analisa Ulang"); self.refresh_button_sv.setStyleSheet(btn_style_primary); self.refresh_button_sv.clicked.connect(self.start_analysis); self.refresh_button_sv.setCursor(Qt.PointingHandCursor)
        self.exit_button_sv = QPushButton("🚪 Keluar"); self.exit_button_sv.setStyleSheet(btn_style_secondary.replace(COLOR_PRIMARY_ACCENT_APP.name(), COLOR_ERROR_APP.name())); self.exit_button_sv.clicked.connect(self.close); self.exit_button_sv.setCursor(Qt.PointingHandCursor)
        # Note: config_button will be connected after config_wrapper is created
        self.config_button_sv = QPushButton("⚙️ Pengaturan"); self.config_button_sv.setStyleSheet(btn_style_secondary.replace(COLOR_PRIMARY_ACCENT_APP.name(), COLOR_SECONDARY_ACCENT_APP.name())); self.config_button_sv.setCursor(Qt.PointingHandCursor)
        signal_actions_layout.addWidget(self.refresh_button_sv, 1); signal_actions_layout.addWidget(self.config_button_sv, 1); signal_actions_layout.addWidget(self.exit_button_sv, 1); self.signals_layout.addLayout(signal_actions_layout); self.stacked_widget.addWidget(self.signals_view_widget)
        self.prompt_view_widget = QWidget(); self.prompt_view_widget.setStyleSheet(f"background-color: {COLOR_BACKGROUND_MAIN.name()};"); prompt_layout = QVBoxLayout(self.prompt_view_widget); prompt_layout.setContentsMargins(20,25,20,20); prompt_layout.setSpacing(15)
        prompt_title = QLabel("💡 Nasihat Sang Legenda Trader"); prompt_title.setFont(QFont("Inter", 22, QFont.Bold)); prompt_title.setStyleSheet(f"color: {COLOR_TEXT_HEADLINE.name()}; margin-bottom: 10px; qproperty-alignment: AlignCenter;"); prompt_layout.addWidget(prompt_title)
        self.prompt_text_edit = QTextEdit(); self.prompt_text_edit.setReadOnly(True); self.prompt_text_edit.setFont(QFont("Inter", 10)); self.prompt_text_edit.setStyleSheet(f"QTextEdit {{ background-color: {COLOR_CARD_BG.name()}; border: 1px solid {COLOR_PROGRESS_BG_APP.name()}; border-radius: 12px; padding: 15px; color: {COLOR_TEXT_BODY.name()}; }}"); prompt_layout.addWidget(self.prompt_text_edit)
        prompt_actions_layout = QHBoxLayout(); prompt_actions_layout.setSpacing(10)
        self.back_to_signals_button = QPushButton("📊 Kembali ke Sinyal"); self.back_to_signals_button.setStyleSheet(btn_style_secondary); self.back_to_signals_button.clicked.connect(lambda: self.show_page(self.signals_view_widget)); self.back_to_signals_button.setCursor(Qt.PointingHandCursor)
        self.copy_prompt_button = QPushButton("📋 Salin Nasihat"); self.copy_prompt_button.setStyleSheet(btn_style_secondary.replace(COLOR_PRIMARY_ACCENT_APP.name(), COLOR_SECONDARY_ACCENT_APP.name())); self.copy_prompt_button.clicked.connect(self.copy_prompt_to_clipboard); self.copy_prompt_button.setCursor(Qt.PointingHandCursor)
        prompt_actions_layout.addWidget(self.back_to_signals_button); prompt_actions_layout.addWidget(self.copy_prompt_button); prompt_layout.addLayout(prompt_actions_layout); self.stacked_widget.addWidget(self.prompt_view_widget)

        # Initialize configuration widget
        self.config_widget = ConfigurationWidget(trading_config)
        self.config_widget.config_changed.connect(self.on_config_changed)

        # Add configuration widget to stacked widget
        config_wrapper = QWidget()
        config_wrapper.setStyleSheet(f"background-color: {COLOR_BACKGROUND_MAIN.name()};")
        config_layout = QVBoxLayout(config_wrapper)
        config_layout.setContentsMargins(0, 0, 0, 0)

        # Add back button to config page
        config_header = QHBoxLayout()
        self.back_from_config_button = QPushButton("🔙 Kembali")
        self.back_from_config_button.setStyleSheet(btn_style_secondary)
        self.back_from_config_button.clicked.connect(lambda: self.show_page(self.initial_view_widget))
        self.back_from_config_button.setCursor(Qt.PointingHandCursor)
        config_header.addWidget(self.back_from_config_button)
        config_header.addStretch()

        config_layout.addLayout(config_header)
        config_layout.addWidget(self.config_widget)

        self.stacked_widget.addWidget(config_wrapper)

        # Connect config buttons after config_wrapper is created
        self.config_button_initial.clicked.connect(lambda: self.show_page(config_wrapper))
        self.config_button_sv.clicked.connect(lambda: self.show_page(config_wrapper))

        # Removed QGraphicsOpacityEffect to prevent QPainter conflicts
        # Simple page transitions without graphics effects
    def show_page(self, page_widget):
        """Simple page switching without animations to prevent QPainter conflicts"""
        if self.stacked_widget.currentWidget() == page_widget:
            return
        self.stacked_widget.setCurrentWidget(page_widget)
    def append_log_message(self, message):
        clean_message = message.split("] ", 1)[-1] if "] " in message else message 
        self.initial_log_display.append(clean_message); self.initial_log_display.verticalScrollBar().setValue(self.initial_log_display.verticalScrollBar().maximum())
        self.signals_log_display.append(clean_message); self.signals_log_display.verticalScrollBar().setValue(self.signals_log_display.verticalScrollBar().maximum())
    def clear_logs(self): self.initial_log_display.clear(); self.signals_log_display.clear()
    def copy_signals_log_to_clipboard(self): QApplication.clipboard().setText(self.signals_log_display.toPlainText()); self.status_label.setText("Status: Log disalin!"); QTimer.singleShot(2000, lambda: self.status_label.setText("Status: Siap Menganalisa..."))
    def show_initial_view(self):
        self.show_page(self.initial_view_widget); self.status_label.setText("Status: Siap Menganalisa..."); self.progress_bar.setValue(0)
        self.start_button.setEnabled(True); self.start_button.setVisible(True); self.exit_button_initial.setEnabled(True); self.stop_analysis_button.setVisible(False)
        self.status_label.setStyleSheet(f"color: {COLOR_TEXT_HEADLINE.name()};"); self.current_etr_string = ""
        if hasattr(self, 'log_card_signals'): self.log_card_signals.toggle_button.setChecked(False)
    def show_loading_view(self):
        self.clear_logs(); log_to_console_and_gui("IntelliTrader X V5 Performance Enhanced: Mempersiapkan analisa baru...", self.log_message_updated)
        self.show_page(self.initial_view_widget); self.status_label.setText("Status: Menghubungkan V5 Performance Enhanced..."); self.progress_bar.setValue(0)
        self.start_button.setEnabled(False); self.start_button.setVisible(False); self.exit_button_initial.setEnabled(True)
        self.stop_analysis_button.setVisible(True); self.stop_analysis_button.setEnabled(True); self.current_etr_string = ""

        # V5 Enhanced: Reset phase and performance indicators
        self.phase_label.setText("Fase: Memulai...")
        self.phase_label.setStyleSheet(f"color: {COLOR_TEXT_BODY.name()};")
        self.perf_metrics_label.setText("Mempersiapkan optimasi performa...")
    def request_stop_analysis(self):
        log_to_console_and_gui("IntelliTrader X V4 Enhanced AI: Tombol Stop Analisa ditekan.", self.log_message_updated)
        if self.analysis_worker and self.analysis_worker.isRunning(): self.analysis_worker.stop(); self.status_label.setText("Status: Menghentikan analisa V4 Enhanced AI..."); self.stop_analysis_button.setEnabled(False)
    def start_analysis(self):
        if self.analysis_worker and self.analysis_worker.isRunning(): self.show_error_message("Analisa IntelliTrader X V4 Enhanced AI sedang berjalan."); return
        self.show_loading_view(); layout = self.signals_cards_layout
        while layout.count():
            item = layout.takeAt(0)
            widget = item.widget()
            if widget is not None:
                widget.deleteLater()
        self.all_pairs_data_cache.clear()

        # V5 Enhanced: Use new performance-optimized worker
        if self.use_v5_worker:
            self.analysis_worker = AnalysisWorkerV5()
            self.analysis_worker.download_progress.connect(self.update_download_progress)
        else:
            self.analysis_worker = AnalysisWorker()

        self.analysis_worker.progress_updated.connect(self.update_progress_and_status)
        self.analysis_worker.etr_updated.connect(self.update_etr_status)
        self.analysis_worker.log_message_updated.connect(self.append_log_message)
        self.analysis_worker.analysis_complete.connect(self.display_signals)
        self.analysis_worker.error_occurred.connect(self.show_error_message)
        self.analysis_worker.start()
    def update_progress_and_status(self, value, message):
        self.progress_bar.setValue(value)
        status_text = f"Status: {message}"
        if self.current_etr_string and value < 100 and value > 0:
            status_text += f" (ETR: {self.current_etr_string})"
        self.status_label.setText(status_text)
        self.status_label.setStyleSheet(f"color: {COLOR_TEXT_HEADLINE.name()};")

        # V5 Enhanced: Update phase indicator
        if value <= 10:
            self.phase_label.setText("Fase: Persiapan")
            self.phase_label.setStyleSheet(f"color: {COLOR_WARNING_APP.name()};")
        elif value <= 60:
            self.phase_label.setText("Fase: Download Data Asynchronous")
            self.phase_label.setStyleSheet(f"color: {COLOR_SECONDARY_ACCENT_APP.name()};")
        elif value <= 95:
            self.phase_label.setText("Fase: Analisis Offline Multi-Threading")
            self.phase_label.setStyleSheet(f"color: {COLOR_PRIMARY_ACCENT_APP.name()};")
        else:
            self.phase_label.setText("Fase: Selesai")
            self.phase_label.setStyleSheet(f"color: {COLOR_SUCCESS_APP.name()};")

    def update_download_progress(self, progress, message):
        """V5 Enhanced: Handle download progress updates"""
        self.update_progress_and_status(progress, message)

        # Update performance metrics
        if hasattr(self, 'analysis_worker') and self.analysis_worker:
            elapsed = time.time() - self.analysis_worker.start_time_total_analysis
            if elapsed > 0:
                pairs_downloaded = message.split('/')[0].split(' ')[-1] if '/' in message else "0"
                try:
                    pairs_count = int(pairs_downloaded)
                    rate = pairs_count / elapsed if elapsed > 0 else 0
                    self.perf_metrics_label.setText(f"Kecepatan: {rate:.1f} datasets/detik | Waktu: {elapsed:.1f}s")
                except:
                    self.perf_metrics_label.setText(f"Waktu: {elapsed:.1f}s")

    def update_etr_status(self, etr_seconds, etr_formatted_string):
        # V4 Fix: etr_seconds parameter kept for compatibility but not used
        self.current_etr_string = etr_formatted_string
        current_status_message = self.status_label.text().split(" (ETR:")[0]
        if "Status: " in current_status_message:
            current_status_message = current_status_message.replace("Status: ", "")
        self.update_progress_and_status(self.progress_bar.value(), current_status_message)
    def display_signals(self, top_signals_tuples):
        if self.analysis_worker and not self.analysis_worker._is_running and "Menghentikan" not in self.status_label.text() and "Dibatalkan" not in self.status_label.text() : self.status_label.setText("Status: Analisa IntelliTrader X V4 Enhanced AI Selesai.")
        self.start_button.setEnabled(True); self.start_button.setVisible(True); self.exit_button_initial.setEnabled(True); self.stop_analysis_button.setVisible(False)
        layout = self.signals_cards_layout
        while layout.count():
            item = layout.takeAt(0)
            widget = item.widget()
            if widget is not None:
                widget.deleteLater()
        if not top_signals_tuples:
            if "Menghentikan" not in self.status_label.text() and "Dibatalkan" not in self.status_label.text(): self.status_label.setText("Status: Tidak ada sinyal signifikan ditemukan V4 Enhanced AI.")
            log_to_console_and_gui("IntelliTrader X V4 Enhanced AI: Tidak ada sinyal signifikan (KUAT atau Conf >= 0.60) ditemukan.", self.log_message_updated, "WARNING")
            no_signal_label = QLabel("Tidak ada sinyal trading signifikan yang memenuhi kriteria saat ini.\nPasar mungkin sedang konsolidasi atau tidak memberikan peluang jelas."); no_signal_label.setFont(QFont("Inter", 12)); no_signal_label.setAlignment(Qt.AlignCenter); no_signal_label.setWordWrap(True); no_signal_label.setStyleSheet(f"color: {COLOR_TEXT_BODY.name()}; padding: 20px;"); self.signals_cards_layout.addWidget(no_signal_label); self.show_page(self.signals_view_widget); return
        if "Menghentikan" not in self.status_label.text() and "Dibatalkan" not in self.status_label.text(): self.status_label.setText(f"Status: Ditemukan {len(top_signals_tuples)} sinyal signifikan V4 Enhanced AI.")
        log_to_console_and_gui(f"IntelliTrader X V4 Enhanced AI: Menampilkan {len(top_signals_tuples)} sinyal signifikan.", self.log_message_updated)
        for pair_symbol, final_score, consolidated_signal_type, data_bundle in top_signals_tuples:
            self.all_pairs_data_cache[pair_symbol] = data_bundle
            card = ModernSignalCard(pair_symbol, consolidated_signal_type, final_score, data_bundle.get('current_price_reference'), data_bundle.get('final_confidence', 0.0))
            card.generate_prompt_requested.connect(self.generate_and_show_prompt); self.signals_cards_layout.addWidget(card)
        self.signals_cards_layout.addSpacerItem(QSpacerItem(1,1,QSizePolicy.Minimum, QSizePolicy.Expanding)); self.show_page(self.signals_view_widget)
    def generate_and_show_prompt(self, pair_symbol):
        if pair_symbol not in self.all_pairs_data_cache: self.show_error_message(f"Data cache IntelliTrader X V4 Enhanced AI untuk {pair_symbol} tidak ditemukan."); return
        data_bundle = self.all_pairs_data_cache[pair_symbol]; prompt = self.create_ai_prompt_hybrid_engine_v3(data_bundle); self.prompt_text_edit.setText(prompt); self.show_page(self.prompt_view_widget)
    def create_ai_prompt_hybrid_engine_v3(self, data_bundle):
        pair = data_bundle.get('pair', 'N/A')
        price_ref = data_bundle.get('current_price_reference')
        price_ref_str = f"${price_ref:,.4f}" if price_ref is not None else "N/A"
        consolidated_signal = data_bundle.get('consolidated_signal', 'NETRAL')
        final_score = data_bundle.get('consolidated_score', 0.0)
        final_confidence = data_bundle.get('final_confidence', 0.0)
        prompt_engine_name = "IntelliTrader X V5 Performance Enhanced"

        # V5 Enhanced: Extract comprehensive technical data
        analysis_details_per_tf = data_bundle.get('analysis_details_per_tf', {})

        prompt = f"""🚀 **ANALISIS TRADING GRUP INDONESIA - {pair}** 🚀

Halo kak! 👋 Admin grup trading di sini dengan analisis lengkap untuk **{pair}**

📊 **RINGKASAN CEPAT:**
• Pair: {pair}
• Harga Sekarang: {price_ref_str} USDT
• Sinyal Sistem: {consolidated_signal}
• Confidence Level: {final_confidence*100:.1f}%
• Score: {final_score:.2f}

💡 **DATA TEKNIKAL REAL-TIME:**"""

        # Add comprehensive technical data for each timeframe
        for tf_str in TIMEFRAMES_TO_ANALYZE:
            tf_analysis = analysis_details_per_tf.get(tf_str)
            prompt += f"\n\n📈 **TIMEFRAME {tf_str.upper()}:**"

            if not tf_analysis or (isinstance(tf_analysis, dict) and tf_analysis.get('error')):
                prompt += f"\n❌ Data tidak tersedia untuk TF {tf_str}"
                continue

            # Safe access to tf_analysis data
            overall_signal = tf_analysis.get('overall_signal', 'N/A') if isinstance(tf_analysis, dict) else 'N/A'
            confidence = tf_analysis.get('confidence', 0.0) if isinstance(tf_analysis, dict) else 0.0
            score = tf_analysis.get('score', 0.0) if isinstance(tf_analysis, dict) else 0.0

            prompt += f"\n• Sinyal: {overall_signal} (Confidence: {confidence*100:.0f}%)"
            prompt += f"\n• Score: {score:.2f}"

            # Extract key technical indicators
            indicators = tf_analysis.get('indicators', {}) if isinstance(tf_analysis, dict) else {}
            if indicators:
                prompt += f"\n• **Indikator Kunci:**"

                # RSI
                if 'RSI_14' in indicators:
                    rsi = indicators['RSI_14']
                    rsi_status = "Overbought" if rsi > 70 else "Oversold" if rsi < 30 else "Normal"
                    prompt += f"\n  - RSI(14): {rsi:.1f} ({rsi_status})"

                # MACD
                if all(key in indicators for key in ['MACD', 'MACD_Signal', 'MACD_Histogram']):
                    macd = indicators['MACD']
                    macd_signal = indicators['MACD_Signal']
                    macd_hist = indicators['MACD_Histogram']
                    macd_trend = "Bullish" if macd > macd_signal else "Bearish"
                    prompt += f"\n  - MACD: {macd:.4f} vs Signal: {macd_signal:.4f} ({macd_trend})"
                    prompt += f"\n  - MACD Histogram: {macd_hist:.4f}"

                # EMA Analysis
                ema_keys = [key for key in indicators.keys() if key.startswith('EMA_')]
                if ema_keys:
                    prompt += f"\n  - **EMA Levels:**"
                    for ema_key in sorted(ema_keys):
                        ema_value = indicators[ema_key]
                        ema_period = ema_key.split('_')[1]
                        price_vs_ema = "Above" if price_ref and price_ref > ema_value else "Below"
                        prompt += f"\n    • EMA({ema_period}): ${ema_value:.4f} (Price {price_vs_ema})"

                # Bollinger Bands
                if all(key in indicators for key in ['BB_Lower', 'BB_Middle', 'BB_Upper', 'BB_Position']):
                    bb_lower = indicators['BB_Lower']
                    bb_middle = indicators['BB_Middle']
                    bb_upper = indicators['BB_Upper']
                    bb_pos = indicators['BB_Position']
                    bb_status = "Upper Band" if bb_pos > 0.8 else "Lower Band" if bb_pos < 0.2 else "Middle Range"
                    prompt += f"\n  - **Bollinger Bands:**"
                    prompt += f"\n    • Lower: ${bb_lower:.4f}"
                    prompt += f"\n    • Middle: ${bb_middle:.4f}"
                    prompt += f"\n    • Upper: ${bb_upper:.4f}"
                    prompt += f"\n    • Position: {bb_pos:.2f} ({bb_status})"

                # Stochastic
                if all(key in indicators for key in ['Stoch_K', 'Stoch_D']):
                    stoch_k = indicators['Stoch_K']
                    stoch_d = indicators['Stoch_D']
                    stoch_status = "Overbought" if stoch_k > 80 else "Oversold" if stoch_k < 20 else "Normal"
                    prompt += f"\n  - Stochastic: %K={stoch_k:.1f}, %D={stoch_d:.1f} ({stoch_status})"

                # Williams %R
                if 'Williams_R' in indicators:
                    willr = indicators['Williams_R']
                    willr_status = "Overbought" if willr > -20 else "Oversold" if willr < -80 else "Normal"
                    prompt += f"\n  - Williams %R: {willr:.1f} ({willr_status})"

                # CCI
                if 'CCI' in indicators:
                    cci = indicators['CCI']
                    cci_status = "Overbought" if cci > 100 else "Oversold" if cci < -100 else "Normal"
                    prompt += f"\n  - CCI: {cci:.1f} ({cci_status})"

            # Volume Analysis
            volume_data = tf_analysis.get('volume_data', {}) if isinstance(tf_analysis, dict) else {}
            if volume_data:
                volume_ratio = volume_data.get('volume_ratio', 1.0)
                volume_status = "High" if volume_ratio > 1.5 else "Normal"
                prompt += f"\n• **Volume:** {volume_ratio:.2f}x average ({volume_status})"

            # Pattern Recognition
            pattern_data = tf_analysis.get('pattern_data', {}) if isinstance(tf_analysis, dict) else {}
            if pattern_data and 'candlestick_patterns' in pattern_data:
                patterns = pattern_data['candlestick_patterns']
                active_patterns = [name for name, active in patterns.items() if active]
                if active_patterns:
                    prompt += f"\n• **Pola Candlestick:** {', '.join(active_patterns)}"

            # Support/Resistance and Key Levels
            key_values = tf_analysis.get('key_values', {}) if isinstance(tf_analysis, dict) else {}
            if key_values:
                prompt += f"\n• **Key Levels:**"
                if 'fibonacci_support' in key_values and key_values['fibonacci_support']:
                    prompt += f"\n  - Fib Support: ${key_values['fibonacci_support']:.4f}"
                if 'volume_poc' in key_values and key_values['volume_poc']:
                    prompt += f"\n  - Volume POC: ${key_values['volume_poc']:.4f}"

        # SMC Analysis Summary
        prompt += f"\n\n🎯 **SMART MONEY CONCEPTS (SMC):**"

        # Collect SMC data from all timeframes
        all_order_blocks = []
        all_fvgs = []
        all_liquidity_sweeps = []
        all_market_structure = []

        for tf_str in TIMEFRAMES_TO_ANALYZE:
            tf_analysis = analysis_details_per_tf.get(tf_str)
            if tf_analysis and isinstance(tf_analysis, dict):
                features = tf_analysis.get('detected_features', {})
                if isinstance(features, dict):
                    all_order_blocks.extend(features.get('order_blocks', []))
                    all_fvgs.extend(features.get('fvgs', []))
                    all_liquidity_sweeps.extend(features.get('liquidity_sweeps', []))
                    all_market_structure.extend(features.get('market_structure', []))

        # Display SMC data
        if all_order_blocks:
            prompt += f"\n• **Order Blocks:**"
            for ob in all_order_blocks[:3]:  # Show top 3
                if isinstance(ob, dict):
                    ob_type = ob.get('type', 'Unknown')
                    ob_strength = ob.get('strength', 'Unknown')
                    ob_zone = ob.get('zone', [0, 0])
                    if isinstance(ob_zone, list) and len(ob_zone) >= 2:
                        prompt += f"\n  - {ob_type} ({ob_strength}): ${ob_zone[0]:.4f} - ${ob_zone[1]:.4f}"

        if all_fvgs:
            prompt += f"\n• **Fair Value Gaps (FVG):**"
            for fvg in all_fvgs[:3]:  # Show top 3
                if isinstance(fvg, dict):
                    fvg_type = fvg.get('type', 'Unknown')
                    fvg_zone = fvg.get('zone', [0, 0])
                    fvg_mitigated = fvg.get('mitigated', False)
                    if isinstance(fvg_zone, list) and len(fvg_zone) >= 2:
                        status = "Mitigated" if fvg_mitigated else "Open"
                        prompt += f"\n  - {fvg_type}: ${fvg_zone[0]:.4f} - ${fvg_zone[1]:.4f} ({status})"

        if all_liquidity_sweeps:
            prompt += f"\n• **Liquidity Sweeps:**"
            for sweep in all_liquidity_sweeps[:3]:  # Show top 3
                if isinstance(sweep, dict):
                    sweep_type = sweep.get('type', 'Unknown')
                    sweep_level = sweep.get('level', 0)
                    prompt += f"\n  - {sweep_type}: ${sweep_level:.4f}"

        if all_market_structure:
            prompt += f"\n• **Market Structure:**"
            for ms in all_market_structure[:3]:  # Show top 3
                if isinstance(ms, dict):
                    ms_type = ms.get('type', 'Unknown')
                    ms_level = ms.get('level', 0)
                    prompt += f"\n  - {ms_type}: ${ms_level:.4f}"

        # Multi-timeframe confluence analysis
        prompt += f"\n\n🔄 **CONFLUENCE ANALYSIS:**"

        bullish_tfs = []
        bearish_tfs = []
        neutral_tfs = []

        for tf_str in TIMEFRAMES_TO_ANALYZE:
            tf_analysis = analysis_details_per_tf.get(tf_str)
            if tf_analysis and isinstance(tf_analysis, dict):
                overall_signal = tf_analysis.get('overall_signal', 'NETRAL')
                if 'BELI' in overall_signal or 'BUY' in overall_signal:
                    bullish_tfs.append(tf_str)
                elif 'JUAL' in overall_signal or 'SELL' in overall_signal:
                    bearish_tfs.append(tf_str)
                else:
                    neutral_tfs.append(tf_str)

        prompt += f"\n• **Bullish TFs:** {', '.join(bullish_tfs) if bullish_tfs else 'None'}"
        prompt += f"\n• **Bearish TFs:** {', '.join(bearish_tfs) if bearish_tfs else 'None'}"
        prompt += f"\n• **Neutral TFs:** {', '.join(neutral_tfs) if neutral_tfs else 'None'}"

        # Confluence strength
        total_tfs = len(TIMEFRAMES_TO_ANALYZE)
        confluence_strength = max(len(bullish_tfs), len(bearish_tfs)) / total_tfs
        confluence_direction = "Bullish" if len(bullish_tfs) > len(bearish_tfs) else "Bearish" if len(bearish_tfs) > len(bullish_tfs) else "Mixed"

        prompt += f"\n• **Confluence Strength:** {confluence_strength:.1%} ({confluence_direction})"

        prompt += f"""

📝 **INSTRUKSI UNTUK ADMIN GRUP:**

Guys, tolong analisis data teknikal di atas dan berikan:

1. **📊 SUMMARY ANALISIS:**
   • Trend direction utama (bullish/bearish/sideways)
   • Key support dan resistance levels yang penting
   • Volume confirmation status
   • Risk level (High/Medium/Low)

2. **🎯 REKOMENDASI TRADING:**
   • Entry zone yang optimal (harga spesifik)
   • Stop Loss level dengan reasoning
   • Take Profit targets (minimal 3 level)
   • Position sizing recommendation
   • Timeframe terbaik untuk entry

3. **⚠️ RISK MANAGEMENT:**
   • Maximum risk per trade (% dari portfolio)
   • Invalidation scenario (kapan cut loss)
   • Market condition yang harus dihindari
   • Alternative setup jika gagal

4. **💬 CHAT GRUP STYLE:**
   • Gunakan bahasa santai tapi informatif
   • Sertakan emoji yang relevan
   • Format bullet points yang mudah dibaca
   • Berikan reasoning yang jelas untuk setiap rekomendasi

**FORMAT JAWABAN:**
Tolong jawab dalam format chat grup trading Indonesia yang santai tapi profesional. Gunakan "kak", "guys", "teman-teman" dan hindari bahasa yang terlalu formal. Sertakan analisis teknikal yang detail tapi mudah dipahami.

---
**Data dari {prompt_engine_name} by Bobacheese - Analisis V5 Performance Enhanced! 🚀**

Semoga membantu trading kalian ya guys! 💪📈"""

        return prompt.strip()
    def copy_prompt_to_clipboard(self): QApplication.clipboard().setText(self.prompt_text_edit.toPlainText()); original_text = self.status_label.text(); self.status_label.setText("Status: Nasihat Legendaris disalin!"); self.status_label.setStyleSheet(f"color: {COLOR_SUCCESS_APP.name()};"); QTimer.singleShot(2000, lambda: (self.status_label.setText(original_text), self.status_label.setStyleSheet(f"color: {COLOR_TEXT_HEADLINE.name()};")))
    def show_error_message(self, message): self.show_initial_view(); self.status_label.setText(f"Error IntelliTrader X: {message}"); self.status_label.setStyleSheet(f"color: {COLOR_ERROR_APP.name()};"); self.append_log_message(f"ERROR: {message}"); self.progress_bar.setValue(0); self.start_button.setEnabled(True); self.start_button.setVisible(True); self.exit_button_initial.setEnabled(True); self.stop_analysis_button.setVisible(False)
    def on_config_changed(self):
        """Handle configuration changes from the config widget"""
        log_to_console_and_gui("Konfigurasi telah diubah. Perubahan akan diterapkan pada analisis berikutnya.", self.log_message_updated)

    def closeEvent(self, event):
        log_to_console_and_gui("IntelliTrader X V4 Enhanced AI: Aplikasi diminta untuk keluar.", self.log_message_updated)
        if self.analysis_worker and self.analysis_worker.isRunning():
            log_to_console_and_gui("IntelliTrader X V4: Analisa berjalan, mencoba menghentikan worker...", self.log_message_updated)
            self.analysis_worker.stop()
            if not self.analysis_worker.wait(5000): log_to_console_and_gui("IntelliTrader X V4: Worker gagal stop baik-baik, force terminate...", self.log_message_updated, "WARNING"); self.analysis_worker.terminate(); self.analysis_worker.wait(); log_to_console_and_gui("IntelliTrader X V4: Worker di-terminate.", self.log_message_updated)
            else: log_to_console_and_gui("IntelliTrader X V4: Worker berhenti dengan baik.", self.log_message_updated)
        else: log_to_console_and_gui("IntelliTrader X V4: Tidak ada analisa berjalan.", self.log_message_updated)
        event.accept()

if __name__ == '__main__':
    # V4 Fix: Use modern Qt High DPI handling
    if hasattr(QApplication, 'setHighDpiScaleFactorRoundingPolicy'):
        QApplication.setHighDpiScaleFactorRoundingPolicy(Qt.HighDpiScaleFactorRoundingPolicy.PassThrough)
    if hasattr(Qt, 'AA_EnableHighDpiScaling'): QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    if hasattr(Qt, 'AA_UseHighDpiPixmaps'): QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
    print("=" * 80 + f"\n🚀 INTELLITRADER X: HYBRID ENGINE V4 (ENHANCED AI) - STARTING UP @ {time.ctime()}\n" + "=" * 80)
    logging.info("🚀 INTELLITRADER X: HYBRID ENGINE V4 (ENHANCED AI) - Program dimulai")
    app = QApplication.instance(); 
    if not app: app = QApplication(sys.argv)
    try: default_font = QFont("Inter", 10 if sys.platform != "darwin" else 11) 
    except RuntimeError: default_font = QFont()
    app.setFont(default_font)
    if not hasattr(app, 'main_window'):
        class MockAppRef:
            pass
        app.main_window = MockAppRef() # type: ignore
    logging.info("🖥️  Memulai GUI IntelliTrader X V4 Enhanced AI...")
    window = BinanceSignalApp(); window.show()
    logging.info("✅ GUI IntelliTrader X V4 Enhanced AI siap! Aplikasi berjalan...")
    try: sys.exit(app.exec())
    except KeyboardInterrupt: logging.info("⚠️  Program dihentikan oleh user (Ctrl+C)"); print("\n⚠️  Program dihentikan oleh user (Ctrl+C)")
    except Exception as e: logging.error(f"❌ Error fatal saat menjalankan aplikasi: {e}", exc_info=True); print(f"\n❌ Error fatal: {e}")
    finally: logging.info("🔚 IntelliTrader X V4 Enhanced AI Program selesai."); print("🔚 IntelliTrader X V4 Enhanced AI Program selesai.")
