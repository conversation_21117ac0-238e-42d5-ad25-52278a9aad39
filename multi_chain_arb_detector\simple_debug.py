"""
File debug sederhana untuk menguji koneksi ke jaringan.
"""
import yaml
import sys
from web3 import Web3

def main():
    """
    Fungsi utama untuk debugging.
    """
    print("Memulai debugging sederhana...")
    
    # Coba memuat konfigurasi
    try:
        with open("config.yaml", 'r') as file:
            config = yaml.safe_load(file)
        print("Konfigurasi berhasil dimuat.")
        
        # Tampilkan jaringan yang dikonfigurasi
        print("Jaringan yang Dikonfigurasi:")
        for network_name in config['networks'].keys():
            print(f"  • {network_name}")
        
        # Coba terhubung ke jaringan Ethereum
        ethereum_config = config['networks']['ethereum']
        rpc_url = ethereum_config['rpc_url']
        
        print(f"Mencoba terhubung ke Ethereum dengan RPC: {rpc_url}")
        
        # Inisialisasi Web3
        w3 = Web3(Web3.HTTPProvider(rpc_url))
        
        # Cek koneksi
        if w3.is_connected():
            print("Berhasil terhubung ke Ethereum!")
            
            # Dapatkan nomor blok terbaru
            latest_block = w3.eth.block_number
            print(f"Nomor blok terbaru: {latest_block}")
        else:
            print("Gagal terhubung ke Ethereum.")
    
    except Exception as e:
        print(f"Error: {e}")
        print(f"Type: {type(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
