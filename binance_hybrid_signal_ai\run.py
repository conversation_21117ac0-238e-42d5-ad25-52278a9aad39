#!/usr/bin/env python3
"""
Binance Hybrid Signal AI - Launcher Script
==========================================

Script untuk menjalankan Binance Hybrid Signal AI dengan berbagai opsi.
"""

import sys
import os
import subprocess
import argparse
import logging

def setup_logging():
    """Setup basic logging untuk launcher"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

def check_dependencies():
    """Cek apakah semua dependencies terinstall"""
    required_packages = [
        ('ccxt', 'ccxt'),
        ('pandas', 'pandas'),
        ('numpy', 'numpy'),
        ('ta', 'ta'),
        # ('pandas_ta', 'pandas_ta'),  # Commented out karena compatibility issue
        ('PySide6', 'PySide6')
    ]

    missing_packages = []

    for display_name, import_name in required_packages:
        try:
            __import__(import_name)
        except ImportError:
            missing_packages.append(display_name)

    if missing_packages:
        print("❌ Missing required packages:")
        for pkg in missing_packages:
            print(f"   - {pkg}")
        print("\n💡 Install missing packages with:")
        print("   pip install -r requirements.txt")
        return False

    print("✅ All dependencies are installed!")
    return True

def run_main_program():
    """Jalankan program utama"""
    main_file = "binance3timeframe (1).py"

    if not os.path.exists(main_file):
        print(f"❌ File {main_file} tidak ditemukan!")
        return False

    print("🚀 Starting Binance Hybrid Signal AI...")
    print("=" * 60)

    try:
        subprocess.run([sys.executable, main_file], check=True)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error running program: {e}")
        return False
    except KeyboardInterrupt:
        print("\n⚠️ Program dihentikan oleh user (Ctrl+C)")
        return True

def run_test_logging():
    """Jalankan test logging"""
    test_file = "test_logging.py"

    if not os.path.exists(test_file):
        print(f"❌ File {test_file} tidak ditemukan!")
        return False

    print("🧪 Running logging test...")
    print("=" * 60)

    try:
        subprocess.run([sys.executable, test_file], check=True)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error running test: {e}")
        return False

def install_dependencies():
    """Install dependencies dari requirements.txt"""
    requirements_file = "requirements.txt"

    if not os.path.exists(requirements_file):
        print(f"❌ File {requirements_file} tidak ditemukan!")
        return False

    print("📦 Installing dependencies...")
    print("=" * 60)

    try:
        subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", requirements_file
        ], check=True)
        print("✅ Dependencies installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error installing dependencies: {e}")
        return False

def show_info():
    """Tampilkan informasi program"""
    print("=" * 80)
    print("🚀 BINANCE HYBRID SIGNAL AI (ASYNC)")
    print("=" * 80)
    print("📊 Fitur:")
    print("   • Multi-timeframe analysis (1h, 30m, 15m)")
    print("   • 60+ Technical Indicators")
    print("   • Smart Money Concepts (Order Blocks, FVG, Divergences)")
    print("   • Async processing untuk performa optimal")
    print("   • Modern GUI dengan real-time logging")
    print("   • AI Prompt generator untuk trading signals")
    print("   • Auto geo-blocking detection & alternative exchanges")
    print()
    print("🎯 Target: Sinyal trading Binance Futures dengan confidence scoring")
    print("⚡ Engine: Async processing dengan ThreadPoolExecutor")
    print()
    print("🚫 Geo-blocking Issues?")
    print("   • Gunakan VPN (ExpressVPN, NordVPN)")
    print("   • Program otomatis coba exchange alternatif")
    print("   • Baca TROUBLESHOOTING.md untuk solusi lengkap")
    print("=" * 80)

def show_troubleshooting():
    """Tampilkan panduan troubleshooting"""
    print("=" * 80)
    print("🔧 TROUBLESHOOTING GUIDE")
    print("=" * 80)
    print("🚫 Geo-blocking (Error 451):")
    print("   1. 🌐 Gunakan VPN (ExpressVPN, NordVPN)")
    print("   2. 🔄 Program otomatis coba Bybit/OKX")
    print("   3. 📧 Hubungi Binance support")
    print()
    print("🌐 Network Issues:")
    print("   1. Cek koneksi internet")
    print("   2. Disable firewall/antivirus")
    print("   3. Coba dari lokasi lain")
    print()
    print("📦 Dependencies Issues:")
    print("   1. python run.py --install")
    print("   2. pip install -r requirements.txt")
    print("   3. Gunakan virtual environment")
    print()
    print("🎭 Testing tanpa koneksi:")
    print("   python run_demo.py")
    print()
    print("📚 Baca file TROUBLESHOOTING.md untuk panduan lengkap!")
    print("=" * 80)

def run_demo_mode():
    """Jalankan demo mode"""
    demo_file = "run_demo.py"

    if not os.path.exists(demo_file):
        print(f"❌ File {demo_file} tidak ditemukan!")
        return False

    print("🎭 Menjalankan demo mode...")
    try:
        subprocess.run([sys.executable, demo_file], check=True)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error running demo: {e}")
        return False

def run_event_loop_test():
    """Jalankan test event loop fix"""
    test_file = "test_event_loop_fix.py"

    if not os.path.exists(test_file):
        print(f"❌ File {test_file} tidak ditemukan!")
        return False

    print("🔄 Menjalankan test event loop fix...")
    try:
        subprocess.run([sys.executable, test_file], check=True)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error running event loop test: {e}")
        return False

def run_thread_exchange_test():
    """Jalankan test thread-specific exchange"""
    test_file = "test_thread_exchange.py"

    if not os.path.exists(test_file):
        print(f"❌ File {test_file} tidak ditemukan!")
        return False

    print("🧵 Menjalankan test thread-specific exchange...")
    try:
        subprocess.run([sys.executable, test_file], check=True)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error running thread exchange test: {e}")
        return False

def main():
    """Main function"""
    setup_logging()

    parser = argparse.ArgumentParser(
        description="Binance Hybrid Signal AI Launcher",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python run.py                    # Run main program
  python run.py --test             # Run logging test
  python run.py --install          # Install dependencies
  python run.py --info             # Show program info
  python run.py --check            # Check dependencies
        """
    )

    parser.add_argument('--test', action='store_true',
                       help='Run logging test')
    parser.add_argument('--install', action='store_true',
                       help='Install dependencies from requirements.txt')
    parser.add_argument('--check', action='store_true',
                       help='Check if all dependencies are installed')
    parser.add_argument('--info', action='store_true',
                       help='Show program information')
    parser.add_argument('--troubleshoot', action='store_true',
                       help='Show troubleshooting guide')
    parser.add_argument('--demo', action='store_true',
                       help='Run in demo mode (no exchange connection)')
    parser.add_argument('--test-eventloop', action='store_true',
                       help='Test event loop fix for threading issues')
    parser.add_argument('--test-thread-exchange', action='store_true',
                       help='Test thread-specific exchange instances')

    args = parser.parse_args()

    # Show info
    if args.info:
        show_info()
        return

    # Show troubleshooting
    if args.troubleshoot:
        show_troubleshooting()
        return

    # Run demo mode
    if args.demo:
        if check_dependencies():
            run_demo_mode()
        return

    # Test event loop fix
    if args.test_eventloop:
        if check_dependencies():
            run_event_loop_test()
        return

    # Test thread-specific exchange
    if args.test_thread_exchange:
        if check_dependencies():
            run_thread_exchange_test()
        return

    # Check dependencies
    if args.check:
        check_dependencies()
        return

    # Install dependencies
    if args.install:
        if install_dependencies():
            print("\n✅ Installation complete! You can now run the program.")
        return

    # Run test
    if args.test:
        if check_dependencies():
            run_test_logging()
        return

    # Default: run main program
    if check_dependencies():
        run_main_program()
    else:
        print("\n💡 Run 'python run.py --install' to install missing dependencies.")

if __name__ == "__main__":
    main()
