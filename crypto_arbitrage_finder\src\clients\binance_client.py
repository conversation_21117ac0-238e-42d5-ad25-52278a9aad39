#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Binance WebSocket client for cryptocurrency exchange data.
"""

import json
import logging
import time
from datetime import datetime
import aiohttp
import websockets
from .base_client import BaseClient

class BinanceClient(BaseClient):
    """Binance WebSocket client implementation."""

    def __init__(self, websocket_url, logger=None):
        """
        Initialize the Binance client.

        Args:
            websocket_url (str): The WebSocket URL for Binance
            logger (logging.Logger, optional): Logger instance
        """
        super().__init__('binance', websocket_url, logger)
        self.rest_api_url = "https://api.binance.com/api/v3"
        self.stream_url = "wss://stream.binance.com:9443/stream"  # Use combined stream URL
        self.symbols_info = {}  # Store symbol information
        self.orderbook_depth = 20  # Default depth for order book
        self.use_combined_stream = True  # Use combined stream for multiple subscriptions

    async def connect(self):
        """Establish WebSocket connection to the exchange."""
        try:
            # If we have multiple subscriptions, use the combined stream URL
            url_to_use = self.stream_url if self.use_combined_stream and len(self.subscribed_pairs) > 1 else self.websocket_url

            self.logger.info(f"Connecting to {self.exchange_id} WebSocket at {url_to_use}")
            self.ws = await websockets.connect(url_to_use)
            self.connected = True
            self.reconnect_delay = 1  # Reset reconnect delay on successful connection
            self.last_heartbeat = time.time()
            self.logger.info(f"Connected to {self.exchange_id} WebSocket")

            # Perform any exchange-specific initialization
            await self.on_connect()

            return True
        except Exception as e:
            self.connected = False
            self.logger.error(f"Failed to connect to {self.exchange_id} WebSocket: {e}")
            return False

    async def on_connect(self):
        """Called after a successful connection to Binance WebSocket."""
        # Binance doesn't require any initialization after connection
        pass

    async def _subscribe_ticker(self, symbol):
        """
        Subscribe to ticker updates for a symbol on Binance.

        Args:
            symbol (str): The trading pair symbol (e.g., 'BTC/USDT')
        """
        # Convert symbol format from 'BTC/USDT' to 'btcusdt'
        formatted_symbol = self._format_symbol(symbol).lower()

        # Binance uses a different WebSocket URL for combined streams
        stream_name = f"{formatted_symbol}@ticker"

        # For combined streams, we need to use the stream URL
        if '/stream' in self.websocket_url:
            message = {
                "method": "SUBSCRIBE",
                "params": [stream_name],
                "id": int(time.time() * 1000)
            }
            await self.send_message(message)
        else:
            # If we're using individual streams, we need to reconnect to a new URL
            self.logger.info(f"Individual streams not supported for subscription, using combined stream")
            # This would require reconnecting to a new URL, which we'll handle differently

    async def _subscribe_orderbook(self, symbol):
        """
        Subscribe to order book updates for a symbol on Binance.

        Args:
            symbol (str): The trading pair symbol (e.g., 'BTC/USDT')
        """
        # Convert symbol format from 'BTC/USDT' to 'btcusdt'
        formatted_symbol = self._format_symbol(symbol).lower()

        # Store the mapping between formatted symbol and original symbol
        self.symbols_info[symbol] = self.symbols_info.get(symbol, {})
        self.symbols_info[symbol]['formatted'] = formatted_symbol

        # Binance provides different depth options: 5, 10, 20
        stream_name = f"{formatted_symbol}@depth{self.orderbook_depth}@100ms"

        # For combined streams, we need to use the stream URL
        if '/stream' in self.websocket_url:
            message = {
                "method": "SUBSCRIBE",
                "params": [stream_name],
                "id": int(time.time() * 1000)
            }
            self.logger.info(f"Subscribing to {stream_name} for {symbol}")
            await self.send_message(message)
        else:
            # If we're using individual streams, we need to reconnect to a new URL
            self.logger.info(f"Individual streams not supported for subscription, using combined stream")
            # This would require reconnecting to a new URL, which we'll handle differently

    async def subscribe_to_multiple_orderbooks(self, symbols):
        """
        Subscribe to order book updates for multiple symbols at once.

        Args:
            symbols (list): List of symbols to subscribe to

        Returns:
            bool: True if subscription was successful, False otherwise
        """
        try:
            if not symbols:
                return True

            # Format the symbols for Binance
            streams = []
            for symbol in symbols:
                formatted_symbol = self._format_symbol(symbol).lower()
                stream = f"{formatted_symbol}@depth{self.orderbook_depth}@100ms"
                streams.append(stream)

                # Store the mapping between formatted symbol and original symbol
                self.symbols_info[symbol] = self.symbols_info.get(symbol, {})
                self.symbols_info[symbol]['formatted'] = formatted_symbol

            self.logger.info(f"Subscribing to {len(streams)} streams: {streams}")

            # Send subscription message for all symbols at once
            message = {
                "method": "SUBSCRIBE",
                "params": streams,
                "id": int(time.time() * 1000)
            }

            await self.send_message(message)

            # Add all symbols to subscribed pairs
            for symbol in symbols:
                self.subscribed_pairs.add(symbol)

            self.logger.info(f"Subscribed to {len(symbols)} orderbooks on {self.exchange_id}")
            return True
        except Exception as e:
            self.logger.error(f"Failed to subscribe to multiple orderbooks on {self.exchange_id}: {e}")
            return False

    async def _send_heartbeat(self):
        """Send heartbeat to keep the connection alive if needed."""
        # Binance WebSocket doesn't require explicit heartbeats
        # The connection is kept alive by the server
        pass

    async def _process_message(self, message):
        """
        Process incoming WebSocket message from Binance.

        Args:
            message (str): The raw message from the WebSocket
        """
        try:
            data = json.loads(message)

            # Check if it's a subscription response
            if 'id' in data and 'result' in data:
                self.logger.debug(f"Subscription response: {data}")
                return

            # Check if it's an error message
            if 'error' in data:
                self.logger.error(f"Error from Binance WebSocket: {data['error']}")
                return

            # Process ticker data
            if 'e' in data and data['e'] == '24hrTicker':
                await self._process_ticker(data)

            # Process order book data
            elif 'lastUpdateId' in data and 'bids' in data and 'asks' in data:
                # This is a direct order book update without a symbol
                # We'll need to determine the symbol from the context
                # For now, we'll just log a debug message and skip it
                self.logger.debug(f"Received direct order book update without symbol")
                # We don't process these updates as they don't have symbol information
                # await self._process_orderbook(data)

            # Process stream data (combined streams)
            elif 'stream' in data and 'data' in data:
                # Pass the whole message to the appropriate handler
                # This allows the handler to extract the symbol from the stream name
                if '@ticker' in data['stream']:
                    await self._process_ticker(data['data'])
                elif '@depth' in data['stream']:
                    await self._process_orderbook(data)

        except json.JSONDecodeError:
            self.logger.error(f"Failed to parse message from Binance: {message}")
        except Exception as e:
            self.logger.error(f"Error processing message from Binance: {e}")

    async def _process_ticker(self, data):
        """
        Process ticker data from Binance.

        Args:
            data (dict): Ticker data from Binance
        """
        try:
            # Extract symbol and convert to standard format (e.g., 'BTCUSDT' to 'BTC/USDT')
            symbol = self._normalize_symbol(data['s'])

            # Extract ticker data
            ticker_data = {
                'symbol': symbol,
                'bid': float(data['b']),
                'ask': float(data['a']),
                'bid_size': float(data['B']),
                'ask_size': float(data['A']),
                'timestamp': int(data['E']),
                'datetime': datetime.fromtimestamp(int(data['E']) / 1000).isoformat(),
                'last': float(data['c']),
                'volume': float(data['v']),
                'change': float(data['p']),
                'percentage': float(data['P']),
                'high': float(data['h']),
                'low': float(data['l']),
                'vwap': float(data['w']) if 'w' in data else None,
                'open': float(data['o']),
                'close': float(data['c']),
                'first': float(data['o']),
                'exchange': 'binance'
            }

            # Update market data
            if symbol not in self.market_data:
                self.market_data[symbol] = {}

            self.market_data[symbol]['ticker'] = ticker_data

            self.logger.debug(f"Updated ticker for {symbol} on Binance: bid={ticker_data['bid']}, ask={ticker_data['ask']}")

        except Exception as e:
            self.logger.error(f"Error processing ticker data from Binance: {e}")

    async def _process_orderbook(self, data):
        """
        Process order book data from Binance.

        Args:
            data (dict): Order book data from Binance
        """
        try:
            # Store the original data for debugging
            original_data = data

            # If this is a combined stream message, extract the actual data and symbol
            stream_name = None
            if 'stream' in data and 'data' in data:
                stream_name = data['stream']
                symbol_part = stream_name.split('@')[0].upper()
                data = data['data']
                # Store the symbol from the stream name
                data['_symbol_from_stream'] = symbol_part

            # Extract symbol and convert to standard format
            symbol = None

            # Try to get symbol from the data
            if 's' in data:
                symbol = self._normalize_symbol(data['s'])
            # Try to get symbol from the stream name stored earlier
            elif '_symbol_from_stream' in data:
                symbol = self._normalize_symbol(data['_symbol_from_stream'])
            # Try to get symbol from the subscribed pairs
            elif len(self.subscribed_pairs) == 1:
                # If we only have one subscribed pair, use that
                symbol = next(iter(self.subscribed_pairs))

            # If we have a stream name, try to match it with our subscribed pairs
            if not symbol and stream_name:
                # Extract the symbol part from the stream name (e.g., 'btcusdt@depth20' -> 'btcusdt')
                if '@' in stream_name:
                    symbol_part = stream_name.split('@')[0].lower()

                    # Try to find a matching subscribed pair
                    for subscribed_symbol in self.subscribed_pairs:
                        formatted = self._format_symbol(subscribed_symbol).lower()
                        if formatted == symbol_part:
                            symbol = subscribed_symbol
                            self.logger.info(f"Matched stream {stream_name} to symbol {symbol}")
                            break

            if not symbol:
                # Map the order book to a subscribed pair based on the stream name
                # This is a workaround for when we can't determine the symbol directly
                if 'stream' in original_data:
                    stream = original_data['stream']
                    for subscribed_symbol in self.subscribed_pairs:
                        # Get the formatted symbol from our stored mapping
                        if subscribed_symbol in self.symbols_info:
                            formatted_symbol = self.symbols_info[subscribed_symbol].get('formatted', '').lower()
                            if formatted_symbol and formatted_symbol in stream:
                                symbol = subscribed_symbol
                                self.logger.debug(f"Matched stream {stream} to symbol {symbol}")
                                break
                # If still no symbol, try the old way
                if not symbol:
                    for subscribed_symbol in self.subscribed_pairs:
                        formatted_symbol = self._format_symbol(subscribed_symbol).lower()
                        if f"{formatted_symbol}@depth" in str(original_data):
                            symbol = subscribed_symbol
                            break

            if not symbol:
                self.logger.error(f"Could not determine symbol from order book data: {original_data}")
                return

            # Extract best bid and ask
            bids = data['bids'] if 'bids' in data else []
            asks = data['asks'] if 'asks' in data else []

            if not bids or not asks:
                return

            best_bid = float(bids[0][0])
            best_bid_size = float(bids[0][1])
            best_ask = float(asks[0][0])
            best_ask_size = float(asks[0][1])

            # Create order book data
            current_time = datetime.now()
            orderbook_data = {
                'symbol': symbol,
                'bid': best_bid,
                'bid_size': best_bid_size,
                'ask': best_ask,
                'ask_size': best_ask_size,
                'bids': bids[:self.orderbook_depth],
                'asks': asks[:self.orderbook_depth],
                'timestamp': int(time.time() * 1000),
                'datetime': current_time,
                'exchange': 'binance'
            }

            # Update market data
            if symbol not in self.market_data:
                self.market_data[symbol] = {}

            self.market_data[symbol]['orderbook'] = orderbook_data

            self.logger.debug(f"Updated order book for {symbol} on Binance: bid={best_bid}, ask={best_ask}")

        except Exception as e:
            self.logger.error(f"Error processing order book data from Binance: {e}")

    async def get_all_symbols(self):
        """
        Get all available trading pairs from Binance.

        Returns:
            list: List of trading pair symbols (e.g., ['BTC/USDT', 'ETH/USDT'])
        """
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.rest_api_url}/exchangeInfo") as response:
                    if response.status == 200:
                        data = await response.json()
                        symbols = []

                        for symbol_data in data['symbols']:
                            # Only include SPOT trading symbols that are currently trading
                            if symbol_data['status'] == 'TRADING':
                                base = symbol_data['baseAsset']
                                quote = symbol_data['quoteAsset']
                                symbol = f"{base}/{quote}"
                                symbols.append(symbol)

                                # Store additional symbol information
                                self.symbols_info[symbol] = {
                                    'base': base,
                                    'quote': quote,
                                    'baseAssetPrecision': symbol_data['baseAssetPrecision'],
                                    'quoteAssetPrecision': symbol_data['quoteAssetPrecision'],
                                    'filters': symbol_data['filters']
                                }

                        self.logger.info(f"Retrieved {len(symbols)} symbols from Binance")
                        return symbols
                    else:
                        self.logger.error(f"Failed to get symbols from Binance: {response.status}")
                        return []
        except Exception as e:
            self.logger.error(f"Error getting symbols from Binance: {e}")
            return []

    def _format_symbol(self, symbol):
        """
        Format symbol from standard format to Binance format.

        Args:
            symbol (str): Symbol in standard format (e.g., 'BTC/USDT')

        Returns:
            str: Symbol in Binance format (e.g., 'BTCUSDT')
        """
        return symbol.replace('/', '')

    def _normalize_symbol(self, symbol):
        """
        Normalize symbol from Binance format to standard format.

        Args:
            symbol (str): Symbol in Binance format (e.g., 'BTCUSDT')

        Returns:
            str: Symbol in standard format (e.g., 'BTC/USDT')
        """
        if not symbol:
            return None

        # Convert to uppercase for consistency
        symbol = symbol.upper()

        # Check if it's already in the correct format
        if '/' in symbol:
            return symbol

        # Check if we have this symbol in our symbols_info
        for base_quote in self.symbols_info:
            base, quote = base_quote.split('/')
            if symbol == f"{base}{quote}":
                return base_quote

        # Try to match with subscribed pairs
        for subscribed_pair in self.subscribed_pairs:
            formatted = self._format_symbol(subscribed_pair).upper()
            if symbol == formatted:
                return subscribed_pair

        # If we don't have the symbol info yet, make a best guess
        # Common quote currencies ordered by length (longest first)
        # to avoid misidentifying symbols like 'ETHBTC' as 'ET/HBTC'
        quote_currencies = sorted(['USDT', 'BUSD', 'USDC', 'BTC', 'ETH', 'BNB', 'USD', 'EUR'], key=len, reverse=True)

        for quote in quote_currencies:
            if symbol.endswith(quote):
                base = symbol[:-len(quote)]
                # Make sure the base is not empty
                if base:
                    return f"{base}/{quote}"

        # If we can't determine the base/quote, return as is with a slash in the middle
        # This is just a fallback and might not be accurate
        if len(symbol) > 3:
            # Try to find a sensible split point
            # For most symbols, the quote currency is 3-4 characters
            if len(symbol) >= 7:  # Like 'BTCUSDT'
                return f"{symbol[:-4]}/{symbol[-4:]}"
            elif len(symbol) >= 6:  # Like 'ETHBTC'
                return f"{symbol[:-3]}/{symbol[-3:]}"
            else:
                # Default to splitting in the middle
                middle = len(symbol) // 2
                return f"{symbol[:middle]}/{symbol[middle:]}"

        # If all else fails, just return the original symbol
        self.logger.warning(f"Could not normalize symbol: {symbol}")
        return symbol
